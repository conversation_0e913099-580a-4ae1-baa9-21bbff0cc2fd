package com.vgop.service.repository;

import com.vgop.service.validation.ValidationSummary;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 校验摘要仓库接口
 */
@Repository
public interface ValidationSummaryRepository {
    
    /**
     * 保存校验摘要
     * 
     * @param summary 校验摘要
     * @return 保存后的校验摘要
     */
    ValidationSummary save(ValidationSummary summary);
    
    /**
     * 根据条件查询校验摘要
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 校验摘要
     */
    ValidationSummary findByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate);
    
    /**
     * 查询接口的所有校验摘要
     * 
     * @param interfaceName 接口名称
     * @return 校验摘要列表
     */
    List<ValidationSummary> findByInterfaceName(String interfaceName);
    
    /**
     * 查询最近的校验摘要
     * 
     * @param limit 数量限制
     * @return 校验摘要列表
     */
    List<ValidationSummary> findRecentSummaries(int limit);
    
    /**
     * 删除校验摘要
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 删除的记录数
     */
    int deleteByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate);
} 