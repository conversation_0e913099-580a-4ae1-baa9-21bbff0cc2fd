package com.vgop.service.controller;

import com.vgop.service.validation.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 校验控制器
 * 提供校验相关的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/validation")
public class ValidationController {
    
    /**
     * 校验服务
     */
    private final ValidationService validationService;
    
    @Autowired
    public ValidationController(ValidationService validationService) {
        this.validationService = validationService;
    }
    
    /**
     * 校验文件
     * 
     * @param request 校验请求
     * @return 校验摘要
     */
    @PostMapping("/validate")
    public ResponseEntity<ValidationSummary> validateFile(@RequestBody ValidationRequest request) {
        log.info("收到校验请求: {}", request);
        
        ValidationSummary summary;
        if (request.getFieldNames() != null && !request.getFieldNames().isEmpty()) {
            // 使用自定义配置
            summary = validationService.validateFile(
                    request.getInterfaceName(),
                    request.getFilePath(),
                    request.getDataDate(),
                    request.getDelimiter(),
                    request.isHeaderLine(),
                    request.getFieldNames());
        } else {
            // 使用默认配置
            summary = validationService.validateFile(
                    request.getInterfaceName(),
                    request.getFilePath(),
                    request.getDataDate());
        }
        
        return ResponseEntity.ok(summary);
    }
    
    /**
     * 校验文件并导出校验通过的数据
     * 
     * @param request 校验导出请求
     * @return 校验摘要
     */
    @PostMapping("/validate-and-export")
    public ResponseEntity<ValidationSummary> validateAndExport(@RequestBody ValidationExportRequest request) {
        log.info("收到校验导出请求: {}", request);
        
        ValidationSummary summary = validationService.validateFileWithIndexAndExport(
                request.getInterfaceName(),
                request.getFilePath(),
                request.getOutputFilePath(),
                request.getDataDate());
        
        return ResponseEntity.ok(summary);
    }
    
    /**
     * 获取校验摘要
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 校验摘要
     */
    @GetMapping("/summary")
    public ResponseEntity<ValidationSummary> getSummary(
            @RequestParam String interfaceName,
            @RequestParam String fileName,
            @RequestParam String dataDate) {
        
        ValidationSummary summary = validationService.getSummary(interfaceName, fileName, dataDate);
        
        if (summary == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(summary);
    }
    
    /**
     * 获取错误记录
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 错误记录列表和总数
     */
    @GetMapping("/errors")
    public ResponseEntity<Map<String, Object>> getErrors(
            @RequestParam String interfaceName,
            @RequestParam String fileName,
            @RequestParam String dataDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<ErrorRecord> errors = validationService.getErrors(
                interfaceName, fileName, dataDate, page, size);
        
        long totalCount = validationService.getErrorCount(interfaceName, fileName, dataDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("errors", errors);
        response.put("totalCount", totalCount);
        response.put("page", page);
        response.put("size", size);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取所有接口
     * 
     * @return 接口名称集合
     */
    @GetMapping("/interfaces")
    public ResponseEntity<Set<String>> getAllInterfaces() {
        Set<String> interfaces = validationService.getAllInterfaces();
        return ResponseEntity.ok(interfaces);
    }
    
    /**
     * 获取接口的字段
     * 
     * @param interfaceName 接口名称
     * @return 字段名称集合
     */
    @GetMapping("/interfaces/{interfaceName}/fields")
    public ResponseEntity<Set<String>> getFieldsByInterface(
            @PathVariable String interfaceName) {
        
        Set<String> fields = validationService.getFieldsByInterface(interfaceName);
        return ResponseEntity.ok(fields);
    }
    
    /**
     * 获取接口的校验规则
     * 
     * @param interfaceName 接口名称
     * @param fieldName 字段名称（可选）
     * @return 校验规则列表
     */
    @GetMapping("/interfaces/{interfaceName}/rules")
    public ResponseEntity<List<ValidationRule>> getRules(
            @PathVariable String interfaceName,
            @RequestParam(required = false) String fieldName) {
        
        List<ValidationRule> rules = validationService.getRules(interfaceName, fieldName);
        return ResponseEntity.ok(rules);
    }
    
    /**
     * 注册字段元数据
     * 
     * @param interfaceName 接口名称
     * @param fieldName 字段名称
     * @param metadata 字段元数据
     * @return 注册结果
     */
    @PostMapping("/interfaces/{interfaceName}/fields/{fieldName}/metadata")
    public ResponseEntity<Map<String, String>> registerFieldMetadata(
            @PathVariable String interfaceName,
            @PathVariable String fieldName,
            @RequestBody FieldMetadata metadata) {
        
        validationService.registerFieldMetadata(interfaceName, fieldName, metadata);
        
        Map<String, String> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "字段元数据注册成功");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 校验请求类
     */
    public static class ValidationRequest {
        /**
         * 接口名称
         */
        private String interfaceName;
        
        /**
         * 文件路径
         */
        private String filePath;
        
        /**
         * 数据日期
         */
        private String dataDate;
        
        /**
         * 分隔符
         */
        private String delimiter = ",";
        
        /**
         * 是否有表头行
         */
        private boolean headerLine = false;
        
        /**
         * 字段名列表
         */
        private List<String> fieldNames;
        
        // Getters and setters
        
        public String getInterfaceName() {
            return interfaceName;
        }
        
        public void setInterfaceName(String interfaceName) {
            this.interfaceName = interfaceName;
        }
        
        public String getFilePath() {
            return filePath;
        }
        
        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }
        
        public String getDataDate() {
            return dataDate;
        }
        
        public void setDataDate(String dataDate) {
            this.dataDate = dataDate;
        }
        
        public String getDelimiter() {
            return delimiter;
        }
        
        public void setDelimiter(String delimiter) {
            this.delimiter = delimiter;
        }
        
        public boolean isHeaderLine() {
            return headerLine;
        }
        
        public void setHeaderLine(boolean headerLine) {
            this.headerLine = headerLine;
        }
        
        public List<String> getFieldNames() {
            return fieldNames;
        }
        
        public void setFieldNames(List<String> fieldNames) {
            this.fieldNames = fieldNames;
        }
        
        @Override
        public String toString() {
            return "ValidationRequest{" +
                    "interfaceName='" + interfaceName + '\'' +
                    ", filePath='" + filePath + '\'' +
                    ", dataDate='" + dataDate + '\'' +
                    ", delimiter='" + delimiter + '\'' +
                    ", headerLine=" + headerLine +
                    ", fieldNames=" + fieldNames +
                    '}';
        }
    }
    
    /**
     * 校验导出请求类
     */
    public static class ValidationExportRequest {
        /**
         * 接口名称
         */
        private String interfaceName;
        
        /**
         * 输入文件路径
         */
        private String filePath;
        
        /**
         * 输出文件路径（校验通过的数据）
         */
        private String outputFilePath;
        
        /**
         * 数据日期
         */
        private String dataDate;
        
        // Getters and setters
        
        public String getInterfaceName() {
            return interfaceName;
        }
        
        public void setInterfaceName(String interfaceName) {
            this.interfaceName = interfaceName;
        }
        
        public String getFilePath() {
            return filePath;
        }
        
        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }
        
        public String getOutputFilePath() {
            return outputFilePath;
        }
        
        public void setOutputFilePath(String outputFilePath) {
            this.outputFilePath = outputFilePath;
        }
        
        public String getDataDate() {
            return dataDate;
        }
        
        public void setDataDate(String dataDate) {
            this.dataDate = dataDate;
        }
        
        @Override
        public String toString() {
            return "ValidationExportRequest{" +
                    "interfaceName='" + interfaceName + '\'' +
                    ", filePath='" + filePath + '\'' +
                    ", outputFilePath='" + outputFilePath + '\'' +
                    ", dataDate='" + dataDate + '\'' +
                    '}';
        }
    }
} 