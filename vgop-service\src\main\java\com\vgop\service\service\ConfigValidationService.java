package com.vgop.service.service;

import com.vgop.service.config.TaskConfig;
import com.vgop.service.config.VgopAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置验证服务
 * 在应用启动时验证配置的完整性和正确性
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Order(1) // 确保在目录初始化之前执行
public class ConfigValidationService implements ApplicationRunner {
    
    private final VgopAppConfig appConfig;
    
    @Autowired
    public ConfigValidationService(VgopAppConfig appConfig) {
        this.appConfig = appConfig;
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始验证应用配置...");
        
        List<String> validationErrors = new ArrayList<>();
        
        // 验证基础路径配置
        validateBasePaths(validationErrors);
        
        // 验证SFTP配置
        validateSftpConfig(validationErrors);
        
        // 验证任务配置
        validateTaskConfigs(validationErrors);
        
        if (!validationErrors.isEmpty()) {
            log.error("配置验证失败，发现以下问题：");
            for (String error : validationErrors) {
                log.error("  - {}", error);
            }
            throw new IllegalStateException("配置验证失败，请检查配置文件");
        }
        
        log.info("配置验证通过");
    }
    
    /**
     * 验证基础路径配置
     */
    private void validateBasePaths(List<String> errors) {
        if (appConfig.getBasePath() == null) {
            errors.add("基础路径配置（app.base-path）不能为空");
            return;
        }
        
        if (!StringUtils.hasText(appConfig.getBasePath().getExportRoot())) {
            errors.add("导出根目录（app.base-path.export-root）不能为空");
        }
        
        if (!StringUtils.hasText(appConfig.getBasePath().getLogRoot())) {
            errors.add("日志根目录（app.base-path.log-root）不能为空");
        }
        
        if (!StringUtils.hasText(appConfig.getBasePath().getBackupRoot())) {
            errors.add("备份根目录（app.base-path.backup-root）不能为空");
        }
    }
    
    /**
     * 验证SFTP配置
     */
    private void validateSftpConfig(List<String> errors) {
        if (appConfig.getSftp() == null) {
            errors.add("SFTP配置（app.sftp）不能为空");
            return;
        }
        
        if (!StringUtils.hasText(appConfig.getSftp().getHost())) {
            errors.add("SFTP主机地址（app.sftp.host）不能为空");
        }
        
        if (!StringUtils.hasText(appConfig.getSftp().getUsername())) {
            errors.add("SFTP用户名（app.sftp.username）不能为空");
        }
        
        if (!StringUtils.hasText(appConfig.getSftp().getRemoteBasePath())) {
            errors.add("SFTP远程路径（app.sftp.remote-base-path）不能为空");
        }
    }
    
    /**
     * 验证任务配置
     */
    private void validateTaskConfigs(List<String> errors) {
        if (appConfig.getTasks() == null) {
            errors.add("任务配置（app.tasks）不能为空");
            return;
        }
        
        // 验证日常任务
        if (appConfig.getTasks().getDaily() != null) {
            for (TaskConfig task : appConfig.getTasks().getDaily()) {
                validateSingleTaskConfig(task, "daily", errors);
            }
        }
        
        // 验证月度任务
        if (appConfig.getTasks().getMonthly() != null) {
            for (TaskConfig task : appConfig.getTasks().getMonthly()) {
                validateSingleTaskConfig(task, "monthly", errors);
            }
        }
    }
    
    /**
     * 验证单个任务配置
     */
    private void validateSingleTaskConfig(TaskConfig task, String taskType, List<String> errors) {
        String prefix = String.format("任务 [%s:%s]", taskType, task.getInterfaceId());
        
        if (!StringUtils.hasText(task.getInterfaceId())) {
            errors.add(prefix + " 接口ID不能为空");
        }
        
        if (!StringUtils.hasText(task.getInterfaceName())) {
            errors.add(prefix + " 接口名称不能为空");
        }
        
        if (!task.isEnabled()) {
            log.debug("{} 已禁用，跳过详细验证", prefix);
            return;
        }
        
        // 验证导出配置
        if (task.getExport() == null) {
            errors.add(prefix + " 导出配置不能为空");
            return;
        }
        
        TaskConfig.ExportConfig exportConfig = task.getExport();
        
        if (!StringUtils.hasText(exportConfig.getSqlTemplate())) {
            errors.add(prefix + " SQL模板不能为空");
        }
        
        if (!StringUtils.hasText(exportConfig.getTempFileNameTemplate())) {
            errors.add(prefix + " 临时文件名模板不能为空");
        }
        
        if (!StringUtils.hasText(exportConfig.getOutputFileNameTemplate())) {
            errors.add(prefix + " 输出文件名模板不能为空");
        }
        
        if (!StringUtils.hasText(exportConfig.getVerfFileNameTemplate())) {
            errors.add(prefix + " 验证文件名模板不能为空");
        }
        
        // 验证文件名模板是否包含必要的占位符
        validateFileNameTemplate(exportConfig.getTempFileNameTemplate(), "临时文件名模板", prefix, errors);
        validateFileNameTemplate(exportConfig.getOutputFileNameTemplate(), "输出文件名模板", prefix, errors);
        validateFileNameTemplate(exportConfig.getVerfFileNameTemplate(), "验证文件名模板", prefix, errors);
    }
    
    /**
     * 验证文件名模板格式
     */
    private void validateFileNameTemplate(String template, String templateName, String prefix, List<String> errors) {
        if (!template.contains("{dataDate}")) {
            errors.add(prefix + " " + templateName + " 必须包含 {dataDate} 占位符");
        }
    }
} 