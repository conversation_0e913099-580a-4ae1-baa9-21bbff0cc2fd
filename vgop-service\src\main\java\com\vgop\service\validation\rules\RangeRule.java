package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 范围校验规则
 * 检查数值是否在指定范围内
 */
@Component
public class RangeRule extends AbstractValidationRule {
    
    /**
     * 最小值
     */
    private final String minValue;
    
    /**
     * 最大值
     */
    private final String maxValue;
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     * @param minValue 最小值
     * @param maxValue 最大值
     */
    public RangeRule(String fieldName, String minValue, String maxValue) {
        super(
                "common." + fieldName + ".range",
                "范围校验",
                String.format("检查字段值是否在范围 [%s, %s] 内", 
                        minValue != null ? minValue : "-∞", 
                        maxValue != null ? maxValue : "+∞"),
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.RANGE
        );
        this.minValue = minValue;
        this.maxValue = maxValue;
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public RangeRule() {
        this("*", null, null);
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 如果值为空，则跳过校验（由RequiredFieldRule处理）
        if (StringUtils.isBlank(value)) {
            return createValidResult();
        }
        
        // 获取字段元数据
        FieldMetadata metadata = context.getFieldMetadata(getFieldName());
        
        // 确定最小值和最大值
        String minValueToUse = minValue;
        String maxValueToUse = maxValue;
        
        if (metadata != null) {
            if (StringUtils.isNotBlank(metadata.getMinValue())) {
                minValueToUse = metadata.getMinValue();
            }
            if (StringUtils.isNotBlank(metadata.getMaxValue())) {
                maxValueToUse = metadata.getMaxValue();
            }
        }
        
        // 如果没有范围限制，则直接通过
        if (minValueToUse == null && maxValueToUse == null) {
            return createValidResult();
        }
        
        try {
            // 尝试将值转换为数字
            BigDecimal numValue = new BigDecimal(value);
            
            // 校验最小值
            if (minValueToUse != null && numValue.compareTo(new BigDecimal(minValueToUse)) < 0) {
                return createInvalidResult(
                        value,
                        String.format("字段 %s 的值 (%s) 小于最小值 (%s)", 
                                getFieldName(), value, minValueToUse),
                        context
                );
            }
            
            // 校验最大值
            if (maxValueToUse != null && numValue.compareTo(new BigDecimal(maxValueToUse)) > 0) {
                return createInvalidResult(
                        value,
                        String.format("字段 %s 的值 (%s) 大于最大值 (%s)", 
                                getFieldName(), value, maxValueToUse),
                        context
                );
            }
            
        } catch (NumberFormatException e) {
            // 如果无法转换为数字，则返回格式错误
            return createInvalidResult(
                    value,
                    String.format("字段 %s 的值 (%s) 不是有效的数字", getFieldName(), value),
                    context
            );
        }
        
        return createValidResult();
    }
} 