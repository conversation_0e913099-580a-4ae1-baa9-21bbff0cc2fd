package com.vgop.service.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.dto.FileTransferRequest;
import com.vgop.service.dto.FileTransferResponse;
import com.vgop.service.exception.VgopException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 文件清单通知服务
 * 内网端负责向DMZ发送文件清单通知的服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileListNotificationService {
    
    private final VgopAppConfig appConfig;
    private final ObjectMapper objectMapper;
    
    /**
     * 向DMZ发送文件清单通知
     * 
     * @param taskId 任务ID
     * @param dateId 数据日期
     * @param interfaceId 接口ID
     * @param taskType 任务类型
     * @param revision 版本号
     * @param outputDir 输出目录（包含生成的.dat和.verf文件）
     * @return 通知结果
     */
    public FileTransferResponse notifyDmzFileTransfer(String taskId, String dateId, String interfaceId, 
                                                     String taskType, String revision, String outputDir) {
        log.info("准备向DMZ发送文件清单通知 - 任务ID: {}, 接口ID: {}, 版本号: {}, 输出目录: {}", 
                taskId, interfaceId, revision, outputDir);
        
        try {
            // 1. 验证DMZ配置
            if (appConfig.getDmz() == null) {
                throw new VgopException("DMZ配置未设置");
            }
            
            // 2. 扫描输出目录，构建文件清单
            List<FileTransferRequest.FileInfo> fileList = scanOutputDirectory(outputDir, interfaceId, revision);
            
            if (fileList.isEmpty()) {
                log.warn("输出目录中没有找到符合条件的文件: {}", outputDir);
                return createEmptyResponse(taskId, "输出目录中没有找到符合条件的文件");
            }
            
            // 3. 构建文件传输请求
            FileTransferRequest request = FileTransferRequest.builder()
                    .taskId(taskId)
                    .dateId(dateId)
                    .interfaceId(interfaceId)
                    .taskType(taskType)
                    .requestTime(LocalDateTime.now())
                    .fileList(fileList)
                    .remarks("VGOP内网任务文件中转请求")
                    .build();
            
            // 4. 发送HTTP请求到DMZ
            FileTransferResponse response = sendNotificationToDmz(request);
            
            log.info("DMZ文件清单通知完成 - 任务ID: {}, 状态: {}, 成功文件: {}, 失败文件: {}", 
                     taskId, response.getStatus(), response.getSuccessFiles(), response.getFailedFiles());
            
            return response;
            
        } catch (Exception e) {
            log.error("向DMZ发送文件清单通知失败 - 任务ID: {}", taskId, e);
            return createErrorResponse(taskId, "文件清单通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 扫描输出目录，构建文件清单
     * 
     * @param outputDir 输出目录
     * @param interfaceId 接口ID（用于过滤文件）
     * @param revision 版本号（用于过滤特定版本的文件）
     * @return 文件信息列表
     */
    private List<FileTransferRequest.FileInfo> scanOutputDirectory(String outputDir, String interfaceId, String revision) {
        List<FileTransferRequest.FileInfo> fileList = new ArrayList<>();
        
        if (outputDir == null) {
            return fileList;
        }
        
        File directory = new File(outputDir);
        if (!directory.exists() || !directory.isDirectory()) {
            log.warn("输出目录不存在或不是目录: {}", outputDir);
            return fileList;
        }
        
        // 获取符合条件的.dat和.verf文件
        File[] files = directory.listFiles((dir, name) -> {
            if (!name.endsWith(".dat") && !name.endsWith(".verf")) {
                return false;
            }
            
            // 检查文件名是否包含当前任务的接口ID和版本号
            // 文件名格式：{prefix}_{dataDate}_{interfaceId}_{revision}_{fileNum}.dat/.verf
            String[] nameParts = name.split("_");
            if (nameParts.length >= 5) {  // 确保至少有5个部分，包括版本号
                String fileInterfaceId = nameParts[3];
                String fileRevision = nameParts[4];
                
                // 处理.verf文件（没有文件序号部分）
                if (name.endsWith(".verf")) {
                    fileRevision = fileRevision.replace(".verf", "");
                } else if (name.endsWith(".dat")) {
                    // 对于.dat文件，需要去除文件序号部分
                    fileRevision = fileRevision.split("\\.")[0];
                }
                
                // 同时匹配接口ID和版本号
                return interfaceId.equals(fileInterfaceId) && revision.equals(fileRevision);
            }
            return false;
        });
        
        if (files != null) {
            for (File file : files) {
                try {
                    String fileType = file.getName().endsWith(".dat") ? "dat" : "verf";
                    
                    FileTransferRequest.FileInfo fileInfo = FileTransferRequest.FileInfo.builder()
                            .fileName(file.getName())
                            .filePath(file.getAbsolutePath())
                            .fileType(fileType)
                            .fileSize(file.length())
                            .createTime(LocalDateTime.now())
                            .build();
                    
                    fileList.add(fileInfo);
                    log.debug("添加文件到传输清单: {}, 大小: {} bytes", file.getName(), file.length());
                    
                } catch (Exception e) {
                    log.warn("处理文件信息失败: {}", file.getAbsolutePath(), e);
                }
            }
        }
        
        log.info("扫描输出目录完成 - 目录: {}, 找到文件: {} 个", outputDir, fileList.size());
        return fileList;
    }
    
    /**
     * 发送通知到DMZ
     * 
     * @param request 文件传输请求
     * @return DMZ响应
     */
    private FileTransferResponse sendNotificationToDmz(FileTransferRequest request) throws VgopException {
        VgopAppConfig.DmzConfig dmzConfig = appConfig.getDmz();
        
        // 构建DMZ服务URL
        String dmzUrl = buildDmzUrl(dmzConfig);
        
        log.info("发送文件清单通知到DMZ - URL: {}, 文件数: {}", dmzUrl, request.getFileList().size());
        
        try {
            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 添加基础认证（如果配置了用户名密码）
            if (dmzConfig.getUsername() != null && dmzConfig.getPassword() != null) {
                String auth = dmzConfig.getUsername() + ":" + dmzConfig.getPassword();
                String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
                headers.set("Authorization", "Basic " + encodedAuth);
            }
            
            // 创建请求实体
            HttpEntity<FileTransferRequest> requestEntity = new HttpEntity<>(request, headers);
            
            // 发送请求并获取响应
            ResponseEntity<FileTransferResponse> responseEntity = restTemplate.exchange(
                    dmzUrl, HttpMethod.POST, requestEntity, FileTransferResponse.class);
            
            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                return responseEntity.getBody();
            } else {
                throw new VgopException("DMZ响应异常 - HTTP状态: " + responseEntity.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("发送DMZ通知请求失败: {}", dmzUrl, e);
            throw new VgopException("发送DMZ通知请求失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建DMZ服务URL
     * 
     * @param dmzConfig DMZ配置
     * @return 完整的DMZ服务URL
     */
    private String buildDmzUrl(VgopAppConfig.DmzConfig dmzConfig) {
        String baseUrl = dmzConfig.getBaseUrl();
        if (!baseUrl.startsWith("http://") && !baseUrl.startsWith("https://")) {
            baseUrl = "http://" + baseUrl;
        }
        
        if (dmzConfig.getPort() != 80 && dmzConfig.getPort() != 443) {
            baseUrl += ":" + dmzConfig.getPort();
        }
        
        String path = dmzConfig.getFileListNotifyPath();
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        
        return baseUrl + path;
    }
    
    /**
     * 创建空响应
     * 
     * @param taskId 任务ID
     * @param message 消息
     * @return 响应对象
     */
    private FileTransferResponse createEmptyResponse(String taskId, String message) {
        return FileTransferResponse.builder()
                .taskId(taskId)
                .status("SUCCESS")
                .responseTime(LocalDateTime.now())
                .totalFiles(0)
                .successFiles(0)
                .failedFiles(0)
                .notFoundFiles(0)
                .successFileList(new ArrayList<>())
                .failedFileList(new ArrayList<>())
                .message(message)
                .build();
    }
    
    /**
     * 创建错误响应
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     * @return 响应对象
     */
    private FileTransferResponse createErrorResponse(String taskId, String errorMessage) {
        return FileTransferResponse.builder()
                .taskId(taskId)
                .status("FAILED")
                .responseTime(LocalDateTime.now())
                .totalFiles(0)
                .successFiles(0)
                .failedFiles(0)
                .notFoundFiles(0)
                .successFileList(new ArrayList<>())
                .failedFileList(new ArrayList<>())
                .message(errorMessage)
                .errorDetail(errorMessage)
                .build();
    }
} 