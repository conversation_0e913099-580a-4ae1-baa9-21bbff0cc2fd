package com.vgop.service.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Stream;

/**
 * 文件处理工具类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class FileUtil {
    
    /**
     * 默认字符集
     */
    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;
    
    /**
     * 缓冲区大小
     */
    private static final int BUFFER_SIZE = 8192;
    
    private FileUtil() {
        // 工具类不允许实例化
    }
    
    /**
     * 获取文件行数
     * 
     * @param filePath 文件路径
     * @return 文件行数
     */
    public static long getFileLineCount(String filePath) {
        try (Stream<String> lines = Files.lines(Paths.get(filePath), DEFAULT_CHARSET)) {
            return lines.count();
        } catch (IOException e) {
            log.error("获取文件行数失败: {}", filePath, e);
            return 0;
        }
    }
    
    /**
     * 快速获取大文件行数（使用BufferedReader）
     * 
     * @param filePath 文件路径
     * @return 文件行数
     */
    public static long getFileLineCountFast(String filePath) {
        long lineCount = 0;
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), DEFAULT_CHARSET), BUFFER_SIZE)) {
            while (reader.readLine() != null) {
                lineCount++;
            }
        } catch (IOException e) {
            log.error("快速获取文件行数失败: {}", filePath, e);
            return 0;
        }
        return lineCount;
    }
    
    /**
     * 创建目录（如果不存在）
     * 
     * @param dirPath 目录路径
     * @return 是否成功
     */
    public static boolean createDirectoryIfNotExists(String dirPath) {
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                log.info("创建目录: {}", dirPath);
            }
            return true;
        } catch (IOException e) {
            log.error("创建目录失败: {}", dirPath, e);
            return false;
        }
    }
    
    /**
     * 获取文件大小（字节）
     * 
     * @param filePath 文件路径
     * @return 文件大小
     */
    public static long getFileSize(String filePath) {
        try {
            return Files.size(Paths.get(filePath));
        } catch (IOException e) {
            log.error("获取文件大小失败: {}", filePath, e);
            return -1;
        }
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @return 是否成功
     */
    public static boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.debug("删除文件: {}", filePath);
                return true;
            }
            return false;
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            return false;
        }
    }
    
    /**
     * 格式化文件大小（人类可读格式）
     * 
     * @param size 字节数
     * @return 格式化后的大小
     */
    public static String formatFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = {"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.2f %s", 
                size / Math.pow(1024, digitGroups), 
                units[digitGroups]);
    }
    
    /**
     * 构建文件路径
     * 
     * @param basePath 基础路径
     * @param dateDir 日期目录（如20240115）
     * @param fileName 文件名
     * @return 完整路径
     */
    public static String buildFilePath(String basePath, String dateDir, String fileName) {
        // 确保路径分隔符统一
        String normalizedBase = basePath.replace("\\", "/");
        if (!normalizedBase.endsWith("/")) {
            normalizedBase += "/";
        }
        
        return normalizedBase + dateDir + "/" + fileName;
    }
    
    /**
     * 生成文件序号（三位数字符串）
     * 
     * @param num 序号
     * @return 格式化的序号（如001, 002, 099, 100）
     */
    public static String formatFileNumber(int num) {
        return String.format("%03d", num);
    }
} 