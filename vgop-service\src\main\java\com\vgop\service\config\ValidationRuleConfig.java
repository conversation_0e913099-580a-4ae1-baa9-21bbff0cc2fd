package com.vgop.service.config;

import com.vgop.service.validation.*;
import com.vgop.service.validation.rules.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 校验规则配置类
 * 负责从配置文件加载校验规则到ValidationEngine
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ValidationRuleConfig {

    private final ValidationEngine validationEngine;
    private final ValidationRulesProperties rulesProperties;

    /**
     * 在应用启动完成后加载校验规则
     */
    @EventListener(ApplicationReadyEvent.class)
    public void loadValidationRules() {
        log.info("开始从配置文件加载校验规则...");
        
        if (rulesProperties.getInterfaces() == null || rulesProperties.getInterfaces().isEmpty()) {
            log.warn("未找到任何接口校验规则配置");
            return;
        }
        
        int totalRulesLoaded = 0;
        
        for (Map.Entry<String, ValidationRulesProperties.InterfaceConfig> entry : rulesProperties.getInterfaces().entrySet()) {
            String interfaceName = entry.getKey();
            ValidationRulesProperties.InterfaceConfig interfaceConfig = entry.getValue();
            
            log.info("正在加载接口 {} 的校验规则...", interfaceName);
            
            if (interfaceConfig.getFields() != null && !interfaceConfig.getFields().isEmpty()) {
                // 基于字段的配置（推荐方式）
                int rulesCount = loadFieldBasedRules(interfaceName, interfaceConfig.getFields());
                registerFieldMetadata(interfaceName, interfaceConfig.getFields());
                registerFieldIndexMapping(interfaceName, interfaceConfig.getFields());
                totalRulesLoaded += rulesCount;
                log.info("接口 {} 加载了 {} 个字段校验规则", interfaceName, rulesCount);
            } else if (interfaceConfig.getRules() != null && !interfaceConfig.getRules().isEmpty()) {
                // 基于规则的配置（向后兼容）
                int rulesCount = loadRuleBasedRules(interfaceName, interfaceConfig.getRules());
                totalRulesLoaded += rulesCount;
                log.info("接口 {} 加载了 {} 个直接校验规则", interfaceName, rulesCount);
            } else {
                log.warn("接口 {} 没有配置任何校验规则", interfaceName);
            }
        }
        
        log.info("校验规则加载完成，共加载 {} 个规则", totalRulesLoaded);
    }
    
    /**
     * 加载基于字段的规则配置
     */
    private int loadFieldBasedRules(String interfaceName, List<ValidationRulesProperties.FieldConfig> fieldConfigs) {
        int rulesLoaded = 0;
        
        for (ValidationRulesProperties.FieldConfig fieldConfig : fieldConfigs) {
            if (fieldConfig.getRules() != null) {
                for (ValidationRulesProperties.RuleConfig ruleConfig : fieldConfig.getRules()) {
                    // 如果规则配置中没有字段名，使用字段配置中的字段名
                    if (!StringUtils.hasText(ruleConfig.getFieldName())) {
                        ruleConfig.setFieldName(fieldConfig.getFieldName());
                    }
                    
                    ValidationRule rule = createRule(interfaceName, ruleConfig);
                    if (rule != null) {
                        validationEngine.addRule(interfaceName, rule);
                        rulesLoaded++;
                        log.debug("为接口 {} 字段 {} 添加了规则: {}", 
                                interfaceName, fieldConfig.getFieldName(), ruleConfig.getType());
                    }
                }
            }
        }
        
        return rulesLoaded;
    }
    
    /**
     * 加载基于规则的配置（向后兼容）
     */
    private int loadRuleBasedRules(String interfaceName, List<ValidationRulesProperties.RuleConfig> ruleConfigs) {
        int rulesLoaded = 0;
        
        for (ValidationRulesProperties.RuleConfig ruleConfig : ruleConfigs) {
            ValidationRule rule = createRule(interfaceName, ruleConfig);
            if (rule != null) {
                validationEngine.addRule(interfaceName, rule);
                rulesLoaded++;
                log.debug("为接口 {} 添加了规则: {} (字段: {})", 
                        interfaceName, ruleConfig.getType(), ruleConfig.getFieldName());
            }
        }
        
        return rulesLoaded;
    }
    
    /**
     * 注册字段元数据
     */
    private void registerFieldMetadata(String interfaceName, List<ValidationRulesProperties.FieldConfig> fieldConfigs) {
        Map<String, FieldMetadata> metadataMap = new HashMap<>();
        
        for (ValidationRulesProperties.FieldConfig fieldConfig : fieldConfigs) {
            FieldMetadata metadata = FieldMetadata.builder()
                    .fieldName(fieldConfig.getFieldName())
                    .description(fieldConfig.getDescription())
                    .fieldType(parseFieldType(fieldConfig.getType()))
                    .required(fieldConfig.isRequired())
                    .order(fieldConfig.getFieldIndex() != null ? fieldConfig.getFieldIndex() : 0)
                    .build();
            
            metadataMap.put(fieldConfig.getFieldName(), metadata);
        }
        
        validationEngine.registerMetadata(interfaceName, metadataMap);
        log.debug("为接口 {} 注册了 {} 个字段的元数据", interfaceName, metadataMap.size());
    }
    
    /**
     * 注册字段索引映射
     */
    private void registerFieldIndexMapping(String interfaceName, List<ValidationRulesProperties.FieldConfig> fieldConfigs) {
        Set<String> fieldNames = new HashSet<>();
        
        for (ValidationRulesProperties.FieldConfig fieldConfig : fieldConfigs) {
            fieldNames.add(fieldConfig.getFieldName());
        }
        
        validationEngine.registerFieldToInterfaceMapping(interfaceName, fieldNames);
        log.debug("为接口 {} 注册了字段到接口的映射关系", interfaceName);
    }
    
    /**
     * 解析字段类型
     */
    private FieldMetadata.FieldType parseFieldType(String type) {
        if (!StringUtils.hasText(type)) {
            return FieldMetadata.FieldType.STRING;
        }
        
        try {
            return FieldMetadata.FieldType.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("未知的字段类型: {}, 使用默认类型STRING", type);
            return FieldMetadata.FieldType.STRING;
        }
    }
    
    /**
     * 获取字段索引映射
     */
    public Map<Integer, String> getFieldIndexMapping(String interfaceName) {
        ValidationRulesProperties.InterfaceConfig interfaceConfig = rulesProperties.getInterfaces().get(interfaceName);
        if (interfaceConfig == null || interfaceConfig.getFields() == null) {
            return Collections.emptyMap();
        }
        
        Map<Integer, String> indexMap = new HashMap<>();
        for (ValidationRulesProperties.FieldConfig fieldConfig : interfaceConfig.getFields()) {
            if (fieldConfig.getFieldIndex() != null) {
                indexMap.put(fieldConfig.getFieldIndex(), fieldConfig.getFieldName());
            }
        }
        
        return indexMap;
    }
    
    /**
     * 获取接口配置
     */
    public ValidationRulesProperties.InterfaceConfig getInterfaceConfig(String interfaceName) {
        return rulesProperties.getInterfaces().get(interfaceName);
    }

    /**
     * 根据规则配置创建具体的校验规则实例
     */
    private ValidationRule createRule(String interfaceName, ValidationRulesProperties.RuleConfig config) {
        if (!config.isEnabled()) {
            log.debug("规则 {} 已禁用，跳过加载", config.getRuleId());
            return null;
        }
        
        String fieldName = config.getFieldName();
        if (!StringUtils.hasText(fieldName)) {
            log.warn("规则 {} 缺少字段名，跳过加载", config.getRuleId());
            return null;
        }
        
        try {
            ValidationRuleType type = ValidationRuleType.valueOf(config.getType().toUpperCase());
            ValidationRule rule = null;
            
            switch (type) {
                case REQUIRED:
                    rule = new RequiredFieldRule(fieldName);
                    break;
                case MOBILE_PHONE:
                    rule = new MobilePhoneRule(fieldName);
                    break;
                case IMSI_KEY:
                    rule = new ImsiRule(fieldName);
                    break;
                case IMEI_KEY:
                    rule = new ImeiRule(fieldName);
                    break;
                case LENGTH:
                    Integer minLength = config.getMin();
                    Integer maxLength = config.getMax();
                    if (config.getExact() != null) {
                        minLength = config.getExact();
                        maxLength = config.getExact();
                    }
                    rule = new LengthRule(fieldName, minLength, maxLength);
                    break;
                case FORMAT:
                    if (!StringUtils.hasText(config.getPattern())) {
                        log.warn("FORMAT规则 {} 缺少正则表达式模式", config.getRuleId());
                        return null;
                    }
                    rule = new FormatRule(fieldName, config.getPattern());
                    break;
                case ENUM:
                    if (config.getValues() == null || config.getValues().isEmpty()) {
                        log.warn("ENUM规则 {} 缺少枚举值列表", config.getRuleId());
                        return null;
                    }
                    rule = new EnumValueRule(fieldName, String.join(",", config.getValues()));
                    break;
                case DATE_FORMAT:
                    if (!StringUtils.hasText(config.getFormat())) {
                        log.warn("DATE_FORMAT规则 {} 缺少日期格式", config.getRuleId());
                        return null;
                    }
                    rule = new DateFormatRule(fieldName, config.getFormat());
                    break;
                case RANGE:
                    rule = new RangeRule(fieldName, config.getMinValue(), config.getMaxValue());
                    break;
                default:
                    log.warn("不支持的规则类型: {}", config.getType());
                    return null;
            }
            
            if (rule != null) {
                log.debug("成功创建规则实例: {} - {}", config.getRuleId(), config.getType());
            }
            
            return rule;
            
        } catch (IllegalArgumentException e) {
            log.error("无效的规则类型: {} (规则ID: {})", config.getType(), config.getRuleId());
            return null;
        } catch (Exception e) {
            log.error("创建规则失败: {} - {}", config.getRuleId(), e.getMessage(), e);
            return null;
        }
    }
} 