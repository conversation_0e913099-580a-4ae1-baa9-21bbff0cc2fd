package com.vgop.service;

import com.vgop.service.config.ValidationRulesProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * VGOP数据校验系统主应用类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})  // 排除自动数据源配置，使用自定义多数据源
@EnableScheduling  // 启用定时任务
@EnableAsync  // 启用异步支持
@EnableConfigurationProperties(ValidationRulesProperties.class) // 显式启用配置属性绑定
public class VgopServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(VgopServiceApplication.class, args);
    }
} 