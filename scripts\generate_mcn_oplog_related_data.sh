#!/bin/bash
#
# 脚本功能: 为 mcn_apploginlog 和 mcn_oplog 表生成有关联关系的测试数据。
#           确保两个表通过 phonenumber 字段关联，能够被查询SQL正常查出数据。
# 使用方法: ./generate_mcn_oplog_related_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_oplog_related_data.sh 20250620 500 bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"

# 创建临时数据文件
APPLOGINLOG_FILE=$(mktemp "mcn_apploginlog_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")
OPLOG_FILE=$(mktemp "mcn_oplog_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 生成 '${DATA_COUNT}' 条关联数据。"
echo "mcn_apploginlog 临时文件: ${APPLOGINLOG_FILE}"
echo "mcn_oplog 临时文件: ${OPLOG_FILE}"

# --- Helper Functions ---
PHONE_PREFIX="1380000"

generate_phone() {
    printf "%s%04d" "${PHONE_PREFIX}" "$1"
}

generate_datetime() {
    local base_hour=$1
    local variance=${2:-0}
    
    HOUR=$(printf "%02d" $(( (base_hour + variance) % 24 )))
    MINUTE=$(printf "%02d" $(($RANDOM % 60)))
    SECOND=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
}

generate_version() {
    echo "$(shuf -i 1-5 -n 1).$(shuf -i 0-9 -n 1).$(shuf -i 0-20 -n 1)"
}

generate_optype() {
    optype_opts=("s" "z" "01" "02" "03")
    echo ${optype_opts[$RANDOM % ${#optype_opts[@]}]}
}

echo "正在生成关联数据..."

# 生成基础电话号码池，确保两个表有关联数据
for i in $(seq 1 ${DATA_COUNT})
do
    phonenumber=$(generate_phone $i)
    
    # 为每个手机号生成登录时间（较早的时间）
    logintime=$(generate_datetime $(($i % 20)) 0)
    version=$(generate_version)
    firstflag="Y"
    
    # mcn_apploginlog 数据：只插入关键字段 phonenumber, firstflag, logintime, version
    echo "${phonenumber}${DELIMITER}${firstflag}${DELIMITER}${logintime}${DELIMITER}${version}" >> "${APPLOGINLOG_FILE}"
    
    # 为同一个手机号生成操作记录（稍晚的时间），确保关联性
    # 80% 的概率生成对应的操作记录
    if [ $((RANDOM % 10)) -lt 8 ]; then
        optime=$(generate_datetime $(($i % 20)) $((1 + $RANDOM % 3)))
        optype=$(generate_optype)
        opmanner=4  # 查询SQL中指定的opmanner值
        
        # mcn_oplog 数据：只插入关键字段 phonenumber, optype, optime, opmanner
        echo "${phonenumber}${DELIMITER}${optype}${DELIMITER}${optime}${DELIMITER}${opmanner}" >> "${OPLOG_FILE}"
    fi
done

# 额外生成一些随机的mcn_oplog数据（增加数据多样性）
for i in $(seq 1 $((DATA_COUNT / 3)))
do
    random_phone=$(generate_phone $((DATA_COUNT + i)))
    optime=$(generate_datetime $(($i % 20)) 0)
    optype=$(generate_optype)
    opmanner=4
    
    echo "${random_phone}${DELIMITER}${optype}${DELIMITER}${optime}${DELIMITER}${opmanner}" >> "${OPLOG_FILE}"
done

echo -e "\n数据生成完毕."
echo "准备将数据导入数据库..."

# --- 准备 LOAD 命令 ---
SQL="
set lock mode to wait 10;

-- 导入 mcn_apploginlog 数据 (只插入关键字段)
LOAD FROM '${APPLOGINLOG_FILE}' DELIMITER '${DELIMITER}' INSERT INTO mcn_apploginlog (phonenumber, firstflag, logintime, version);

-- 导入 mcn_oplog 数据 (只插入关键字段)  
LOAD FROM '${OPLOG_FILE}' DELIMITER '${DELIMITER}' INSERT INTO mcn_oplog (phonenumber, optype, optime, opmanner);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${APPLOGINLOG_FILE}" "${OPLOG_FILE}"
  
  echo ""
  echo "数据导入完成！可以使用以下SQL验证数据："
  echo "SELECT '', phonenumber, type, optime, version FROM ("
  echo "  SELECT phonenumber, 1 as type, logintime as optime, version FROM mcn_apploginlog"
  echo "  WHERE logintime>='${TARGET_DATE}000000' AND logintime<'${TARGET_DATE}235959'"
  echo "  UNION ALL"
  echo "  SELECT phonenumber, 2 as type, optime, '' as version FROM mcn_oplog"
  echo "  WHERE optime>='${TARGET_DATE}000000' AND optime<'${TARGET_DATE}235959' AND opmanner=4"
  echo ") ORDER BY phonenumber, type;"
  
else
  echo "数据导入失败！"
  echo "临时数据文件已保留："
  echo "  mcn_apploginlog: '${APPLOGINLOG_FILE}'"
  echo "  mcn_oplog: '${OPLOG_FILE}'"
  exit 1
fi

echo "脚本执行完毕。" 