package com.vgop.service.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件传输响应DTO
 * DMZ返回给内网的传输结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileTransferResponse {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 处理状态（SUCCESS/FAILED/PARTIAL）
     */
    private String status;
    
    /**
     * 响应时间
     */
    private LocalDateTime responseTime;
    
    /**
     * 处理开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 处理结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 总文件数
     */
    private Integer totalFiles;
    
    /**
     * 成功传输文件数
     */
    private Integer successFiles;
    
    /**
     * 失败文件数
     */
    private Integer failedFiles;
    
    /**
     * 文件不存在数量
     */
    private Integer notFoundFiles;
    
    /**
     * 成功文件清单
     */
    private List<FileTransferResult> successFileList;
    
    /**
     * 失败文件清单
     */
    private List<FileTransferResult> failedFileList;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 错误详情
     */
    private String errorDetail;
    
    /**
     * 文件传输结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileTransferResult {
        
        /**
         * 文件名
         */
        private String fileName;
        
        /**
         * 本地文件路径
         */
        private String localFilePath;
        
        /**
         * 远程文件路径
         */
        private String remoteFilePath;
        
        /**
         * 传输状态（SUCCESS/FAILED/NOT_FOUND）
         */
        private String status;
        
        /**
         * 传输开始时间
         */
        private LocalDateTime transferStartTime;
        
        /**
         * 传输结束时间
         */
        private LocalDateTime transferEndTime;
        
        /**
         * 传输耗时（毫秒）
         */
        private Long transferDuration;
        
        /**
         * 文件大小（字节）
         */
        private Long fileSize;
        
        /**
         * 错误原因
         */
        private String errorReason;
    }
} 