server:
  port: 18080

spring:
  application:
    name: vgop-service-test
  
  # 数据源配置（测试环境）
  datasource:
    driver-class-name: com.informix.jdbc.IfxDriver
    url: jdbc:informix-sqli://localhost:9088/test_db:informixserver=ol_informix1170
    username: test_user
    password: test_password
    hikari:
      minimum-idle: 2
      maximum-pool-size: 5
      connection-timeout: 10000
      idle-timeout: 300000
      max-lifetime: 900000
      leak-detection-threshold: 30000
      connection-test-query: SELECT 1 FROM systables WHERE tabid=1

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.vgop.service.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# VGOP业务配置（测试环境）
vgop:
  # 数据库配置
  database:
    connection-timeout: 10
    query-timeout: 30
    lock-wait-timeout: 5
    batch-size: 500
    
  # 文件处理配置
  file-processing:
    output-path: /tmp/vgop-test/output
    temp-path: /tmp/vgop-test/temp
    max-lines-per-file: 10000  # 测试环境使用较小的分片
    encoding: GBK
    separator: "\\200"
    line-ending: "\\r"
    
  # 调度器配置
  scheduler:
    core-pool-size: 2
    max-pool-size: 4
    queue-capacity: 10
    task-timeout-minutes: 5  # 测试环境使用较短的超时
    keep-alive-seconds: 60
    thread-name-prefix: "VgopTask-Test-"
    
  # 重试配置
  retry:
    max-attempts: 2  # 测试环境减少重试次数
    initial-delay: 10  # 10秒
    multiplier: 2.0
    max-delay: 60  # 60秒
    cleanup-interval: 300  # 5分钟

# 日志配置
logging:
  level:
    com.vgop.service: DEBUG
    org.springframework.jdbc: DEBUG
    org.apache.ibatis: DEBUG
    com.informix: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always 