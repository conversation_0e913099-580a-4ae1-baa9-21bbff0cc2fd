package com.vgop.service.validation;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 校验摘要类
 * 用于记录校验结果统计信息
 */
@Data
@Builder
public class ValidationSummary {
    
    /**
     * 摘要ID
     */
    private Long id;
    
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 数据日期
     */
    private String dataDate;
    
    /**
     * 总行数
     */
    private Long totalRows;
    
    /**
     * 错误行数
     */
    private Long errorRows;
    
    /**
     * 错误总数
     */
    private Long totalErrors;
    
    /**
     * 错误率（百分比）
     */
    private Double errorRate;
    
    /**
     * 错误严重程度统计（严重程度 -> 数量）
     */
    private Map<String, Long> severityCounts;
    
    /**
     * 错误规则统计（规则ID -> 数量）
     */
    private Map<String, Long> ruleCounts;
    
    /**
     * 错误字段统计（字段名 -> 数量）
     */
    private Map<String, Long> fieldCounts;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 校验状态（RUNNING, COMPLETED, FAILED）
     */
    private String status;
    
    /**
     * 创建一个新的校验摘要
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 校验摘要
     */
    public static ValidationSummary create(String interfaceName, String fileName, String dataDate) {
        return ValidationSummary.builder()
                .interfaceName(interfaceName)
                .fileName(fileName)
                .dataDate(dataDate)
                .totalRows(0L)
                .errorRows(0L)
                .totalErrors(0L)
                .errorRate(0.0)
                .severityCounts(new HashMap<>())
                .ruleCounts(new HashMap<>())
                .fieldCounts(new HashMap<>())
                .startTime(LocalDateTime.now())
                .status("RUNNING")
                .build();
    }
    
    /**
     * 更新校验摘要
     * 
     * @param result 校验结果
     */
    public void updateWithResult(ValidationResult result) {
        if (!result.isValid()) {
            // 更新错误总数
            totalErrors++;
            
            // 更新严重程度统计
            String severity = result.getSeverity().name();
            severityCounts.put(severity, severityCounts.getOrDefault(severity, 0L) + 1);
            
            // 更新规则统计
            String ruleId = result.getRuleId();
            ruleCounts.put(ruleId, ruleCounts.getOrDefault(ruleId, 0L) + 1);
            
            // 更新字段统计
            String fieldName = result.getFieldName();
            fieldCounts.put(fieldName, fieldCounts.getOrDefault(fieldName, 0L) + 1);
        }
    }
    
    /**
     * 更新行统计
     * 
     * @param hasErrors 是否有错误
     */
    public void updateRowCount(boolean hasErrors) {
        totalRows++;
        if (hasErrors) {
            errorRows++;
        }
        
        // 更新错误率
        if (totalRows > 0) {
            errorRate = (double) errorRows / totalRows * 100;
        }
    }
    
    /**
     * 完成校验
     */
    public void complete() {
        endTime = LocalDateTime.now();
        duration = endTime.toInstant(java.time.ZoneOffset.UTC).toEpochMilli()
                - startTime.toInstant(java.time.ZoneOffset.UTC).toEpochMilli();
        status = "COMPLETED";
    }
    
    /**
     * 标记校验失败
     */
    public void fail() {
        endTime = LocalDateTime.now();
        duration = endTime.toInstant(java.time.ZoneOffset.UTC).toEpochMilli()
                - startTime.toInstant(java.time.ZoneOffset.UTC).toEpochMilli();
        status = "FAILED";
    }
} 