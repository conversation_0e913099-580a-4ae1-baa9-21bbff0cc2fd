package com.vgop.service.validation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 校验引擎
 * 负责加载和执行校验规则
 */
@Slf4j
@Component
public class ValidationEngine {
    
    /**
     * 规则映射（接口名 -> 字段名 -> 规则列表）
     */
    private final Map<String, Map<String, List<ValidationRule>>> ruleMap = new ConcurrentHashMap<>();
    
    /**
     * 字段元数据映射（接口名 -> 字段名 -> 字段元数据）
     */
    private final Map<String, Map<String, FieldMetadata>> metadataMap = new ConcurrentHashMap<>();
    
    /**
     * 存储字段到接口的映射关系
     */
    private final Map<String, String> fieldToInterfaceMap = new ConcurrentHashMap<>();
    
    /**
     * 默认构造函数
     */
    public ValidationEngine() {
        log.info("ValidationEngine已初始化，等待规则配置...");
    }
    
    /**
     * 添加校验规则（带接口名）
     *
     * @param interfaceName 接口名
     * @param rule          校验规则
     */
    public void addRule(String interfaceName, ValidationRule rule) {
        String fieldName = rule.getFieldName();
        
        // 获取接口规则映射，不存在则创建
        Map<String, List<ValidationRule>> interfaceRules = ruleMap.computeIfAbsent(
                interfaceName, k -> new ConcurrentHashMap<>());
        
        // 获取字段规则列表，不存在则创建
        List<ValidationRule> fieldRules = interfaceRules.computeIfAbsent(
                fieldName, k -> new ArrayList<>());
        
        // 添加规则
        fieldRules.add(rule);
        
        log.info("加载校验规则: 接口={}, 字段={}, 规则ID={}, 规则名称={}",
                interfaceName, fieldName, rule.getRuleId(), rule.getRuleName());
    }
    
    /**
     * 添加校验规则
     * 
     * @param rule 校验规则
     */
    public void addRule(ValidationRule rule) {
        String interfaceName = getInterfaceNameFromRule(rule);
        addRule(interfaceName, rule);
    }
    
    /**
     * 批量添加校验规则
     * 
     * @param rules 规则列表
     */
    public void addRules(List<ValidationRule> rules) {
        for (ValidationRule rule : rules) {
            addRule(rule);
        }
        log.info("批量加载了 {} 个校验规则", rules.size());
    }
    
    /**
     * 从规则中提取接口名
     * 
     * @param rule 校验规则
     * @return 接口名
     */
    private String getInterfaceNameFromRule(ValidationRule rule) {
        // 从规则ID中提取接口名，规则ID格式：{接口名}.{字段名}.{规则类型}
        String ruleId = rule.getRuleId();
        String[] parts = ruleId.split("\\.");
        
        // 如果规则ID包含接口名（如：VGOP1-R2.10-24201.phonenumber.required）
        if (parts.length >= 3 && ruleId.startsWith("VGOP")) {
            // 提取接口名部分
            StringBuilder interfaceName = new StringBuilder();
            for (int i = 0; i < parts.length - 2; i++) {
                if (i > 0) interfaceName.append(".");
                interfaceName.append(parts[i]);
            }
            return interfaceName.toString();
        }
        
        // 从字段到接口的映射中查找
        String fieldName = rule.getFieldName();
        String mappedInterface = fieldToInterfaceMap.get(fieldName);
        if (mappedInterface != null) {
            return mappedInterface;
        }
        
        // 如果是简单的字段名（如：phonenumber），则需要从配置中推断接口名
        // 这里我们假设当前正在配置VGOP1-R2.10-24201接口的规则
        if (isVgop24201Field(fieldName)) {
            return "VGOP1-R2.10-24201";
        }
        
        return parts.length > 0 ? parts[0] : "default";
    }
    
    /**
     * 注册字段到接口的映射关系
     * 
     * @param interfaceName 接口名
     * @param fieldNames 字段名列表
     */
    public void registerFieldToInterfaceMapping(String interfaceName, Set<String> fieldNames) {
        for (String fieldName : fieldNames) {
            fieldToInterfaceMap.put(fieldName, interfaceName);
        }
        log.debug("注册字段到接口映射: {} -> {}", fieldNames, interfaceName);
    }
    
    /**
     * 判断字段是否属于VGOP1-R2.10-24201接口
     * 
     * @param fieldName 字段名
     * @return 是否属于该接口
     */
    private boolean isVgop24201Field(String fieldName) {
        return fieldName != null && (
            fieldName.equals("line_number") ||
            fieldName.equals("phonenumber") ||
            fieldName.equals("phonestate") ||
            fieldName.equals("phoneimsi") ||
            fieldName.equals("phoneimei") ||
            fieldName.equals("locationid") ||
            fieldName.equals("provinceid") ||
            fieldName.equals("openingtime") ||
            fieldName.equals("Optime") ||
            fieldName.equals("sex")
        );
    }
    
    /**
     * 注册字段元数据
     * 
     * @param interfaceName 接口名
     * @param metadata 字段元数据映射
     */
    public void registerMetadata(String interfaceName, Map<String, FieldMetadata> metadata) {
        metadataMap.put(interfaceName, metadata);
        log.info("注册字段元数据: 接口={}, 字段数量={}", interfaceName, metadata.size());
    }
    
    /**
     * 校验单行数据
     * 
     * @param interfaceName 接口名
     * @param rowData 行数据
     * @param rowNumber 行号
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 校验结果列表
     */
    public List<ValidationResult> validateRow(String interfaceName, Map<String, String> rowData,
                                             long rowNumber, String fileName, String dataDate) {
        List<ValidationResult> results = new ArrayList<>();
        
        // 获取接口规则映射
        Map<String, List<ValidationRule>> interfaceRules = ruleMap.get(interfaceName.replace(".", "-"));
        if (interfaceRules == null || interfaceRules.isEmpty()) {
            log.warn("未找到接口的校验规则: {}", interfaceName);
            return results;
        }
        
        // 获取接口字段元数据
        Map<String, FieldMetadata> fieldMetadata = metadataMap.get(interfaceName.replace(".", "-"));
        if (fieldMetadata == null) {
            fieldMetadata = Collections.emptyMap();
        }
        
        // 创建校验上下文
        ValidationContext context = ValidationContext.builder()
                .interfaceName(interfaceName)
                .fileName(fileName)
                .dataDate(dataDate)
                .rowNumber(rowNumber)
                .rowData(rowData)
                .fieldMetadata(fieldMetadata)
                .build();
        
        // 对每个字段执行校验
        for (Map.Entry<String, String> entry : rowData.entrySet()) {
            String fieldName = entry.getKey();
            String fieldValue = entry.getValue();
            
            // 获取字段的规则列表
            List<ValidationRule> fieldRules = interfaceRules.get(fieldName);
            if (fieldRules == null || fieldRules.isEmpty()) {
                continue;
            }
            
            // 执行每个规则的校验
            for (ValidationRule rule : fieldRules) {
                ValidationResult result = rule.validate(fieldValue, context);
                if (!result.isValid()) {
                    results.add(result);
                }
            }
        }
        
        return results;
    }
    
    /**
     * 获取接口的所有规则
     * 
     * @param interfaceName 接口名
     * @return 规则列表
     */
    public List<ValidationRule> getRulesByInterface(String interfaceName) {
        Map<String, List<ValidationRule>> interfaceRules = ruleMap.get(interfaceName);
        if (interfaceRules == null) {
            return Collections.emptyList();
        }
        
        return interfaceRules.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取字段的所有规则
     * 
     * @param interfaceName 接口名
     * @param fieldName 字段名
     * @return 规则列表
     */
    public List<ValidationRule> getRulesByField(String interfaceName, String fieldName) {
        Map<String, List<ValidationRule>> interfaceRules = ruleMap.get(interfaceName);
        if (interfaceRules == null) {
            return Collections.emptyList();
        }
        
        List<ValidationRule> fieldRules = interfaceRules.get(fieldName);
        return fieldRules != null ? fieldRules : Collections.emptyList();
    }
    
    /**
     * 获取所有接口名
     * 
     * @return 接口名列表
     */
    public Set<String> getAllInterfaces() {
        return ruleMap.keySet();
    }
    
    /**
     * 获取接口的所有字段名
     * 
     * @param interfaceName 接口名
     * @return 字段名列表
     */
    public Set<String> getFieldsByInterface(String interfaceName) {
        Map<String, List<ValidationRule>> interfaceRules = ruleMap.get(interfaceName);
        return interfaceRules != null ? interfaceRules.keySet() : Collections.emptySet();
    }
} 