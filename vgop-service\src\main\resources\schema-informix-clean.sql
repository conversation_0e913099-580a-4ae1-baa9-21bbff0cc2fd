-- GBase/Informix兼容的DDL脚本（带存在性检查）
-- 注意：GBase可能不完全支持IF NOT EXISTS，此脚本仅供参考

-- 告警信息表
CREATE TABLE vgop_validation_alerts (
    alert_id SERIAL8 PRIMARY KEY,
    alert_time VARCHAR(14) DEFAULT '' NOT NULL,
    interface_name VARCHAR(100) NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    alert_level VARCHAR(20) DEFAULT 'WARNING' NOT NULL,
    alert_message VARCHAR(255) NOT NULL,
    file_name VARCHAR(200),
    line_number INT8,
    error_data TEXT,
    field_errors TEXT,
    metric_details TEXT,
    excel_report_path VARCHAR(255),
    status VARCHAR(20) DEFAULT 'NEW' NOT NULL,
    handled_by VA<PERSON>HAR(100),
    handled_time VARCHAR(14)
);

-- 创建索引
CREATE INDEX idx_alerts_time ON vgop_validation_alerts(alert_time);
CREATE INDEX idx_alerts_interface ON vgop_validation_alerts(interface_name);
CREATE INDEX idx_alerts_status ON vgop_validation_alerts(status);

-- 历史统计表
CREATE TABLE vgop_metrics_history (
    history_id SERIAL8 PRIMARY KEY,
    processing_date DATE NOT NULL,
    interface_name VARCHAR(100) NOT NULL,
    total_records INT8 DEFAULT 0 NOT NULL,
    compliant_records INT8 DEFAULT 0 NOT NULL,
    non_compliant_records INT8 DEFAULT 0 NOT NULL,
    export_file_count INTEGER DEFAULT 0 NOT NULL,
    export_status VARCHAR(20) NOT NULL,
    transfer_status VARCHAR(20),
    created_at DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3) NOT NULL,
    UNIQUE (interface_name, processing_date) CONSTRAINT uk_interface_date
);

-- 任务执行日志表
CREATE TABLE vgop_task_execution_log (
    log_id SERIAL8 PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    task_name VARCHAR(100) NOT NULL,
    task_type VARCHAR(20) NOT NULL,
    start_time DATETIME YEAR TO FRACTION(3) NOT NULL,
    end_time DATETIME YEAR TO FRACTION(3),
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    processed_records INT8,
    generated_files INTEGER
);

-- 创建索引
CREATE INDEX idx_task_log_task_id ON vgop_task_execution_log(task_id);
CREATE INDEX idx_task_log_start_time ON vgop_task_execution_log(start_time);

-- 文件备份表
CREATE TABLE vgop_file_backup (
    backup_id SERIAL8 PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    data_date DATE NOT NULL,
    file_type VARCHAR(20) NOT NULL,
    original_path VARCHAR(255) NOT NULL,
    backup_path VARCHAR(255) NOT NULL,
    file_size INT8 NOT NULL,
    backup_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3) NOT NULL,
    is_recovered BOOLEAN DEFAULT 'f' NOT NULL,
    recovery_date DATE
);

-- 创建索引
CREATE INDEX idx_backup_interface ON vgop_file_backup(interface_name);
CREATE INDEX idx_backup_date ON vgop_file_backup(data_date);

-- 任务执行记录表
CREATE TABLE vgop_task_execution (
    id SERIAL8 PRIMARY KEY,
    task_type VARCHAR(20) NOT NULL,
    data_date VARCHAR(8) NOT NULL,
    stage VARCHAR(20) NOT NULL,
    revision INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL,
    start_time DATETIME YEAR TO FRACTION(3),
    end_time DATETIME YEAR TO FRACTION(3),
    duration INT8,
    error_message VARCHAR(255),
    processed_count INTEGER,
    success_count INTEGER,
    fail_count INTEGER,
    extra_info TEXT,
    create_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3) NOT NULL,
    update_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3) NOT NULL
);

-- 创建索引
CREATE INDEX idx_task_execution_date ON vgop_task_execution(data_date);
CREATE INDEX idx_task_execution_type ON vgop_task_execution(task_type);
CREATE INDEX idx_task_execution_stage ON vgop_task_execution(stage);
CREATE INDEX idx_task_execution_status ON vgop_task_execution(status);

-- 任务执行表
CREATE TABLE vgop_task_executions (
    id SERIAL8 PRIMARY KEY,
    task_id VARCHAR(36) NOT NULL,
    task_name VARCHAR(100) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    start_time DATETIME YEAR TO FRACTION(3) NOT NULL,
    end_time DATETIME YEAR TO FRACTION(3),
    duration INT8,
    parameters TEXT,
    result TEXT,
    error_message TEXT,
    create_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3)
);

-- 错误记录表
CREATE TABLE vgop_error_records (
    id SERIAL8 PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    data_date VARCHAR(8) NOT NULL,
    row_number INT8 NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    field_value TEXT,
    error_message TEXT NOT NULL,
    rule_id VARCHAR(100) NOT NULL,
    rule_name VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    create_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3)
);

-- 创建索引
CREATE INDEX idx_interface_file_date ON vgop_error_records(interface_name, file_name, data_date);
CREATE INDEX idx_row_number ON vgop_error_records(row_number);
CREATE INDEX idx_field_name ON vgop_error_records(field_name);
CREATE INDEX idx_rule_id ON vgop_error_records(rule_id);
CREATE INDEX idx_severity ON vgop_error_records(severity);

-- 校验摘要表
CREATE TABLE vgop_validation_summaries (
    id SERIAL8 PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    data_date VARCHAR(8) NOT NULL,
    total_rows INT8 NOT NULL,
    error_rows INT8 NOT NULL,
    total_errors INT8 NOT NULL,
    error_rate DECIMAL(10,4) NOT NULL,
    severity_counts TEXT NOT NULL,
    rule_counts TEXT NOT NULL,
    field_counts TEXT NOT NULL,
    start_time DATETIME YEAR TO FRACTION(3) NOT NULL,
    end_time DATETIME YEAR TO FRACTION(3),
    duration INT8,
    status VARCHAR(20) NOT NULL,
    create_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3),
    UNIQUE (interface_name, file_name, data_date) CONSTRAINT uk_interface_file_date_val
);

-- 创建索引
CREATE INDEX idx_interface_name_val ON vgop_validation_summaries(interface_name);
CREATE INDEX idx_file_name_val ON vgop_validation_summaries(file_name);
CREATE INDEX idx_data_date_val ON vgop_validation_summaries(data_date);
CREATE INDEX idx_start_time_val ON vgop_validation_summaries(start_time);

-- 字段元数据表
CREATE TABLE vgop_field_metadata (
    id SERIAL8 PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    field_type VARCHAR(50) NOT NULL,
    required BOOLEAN DEFAULT 'f',
    max_length INTEGER,
    min_length INTEGER,
    pattern VARCHAR(255),
    min_value VARCHAR(100),
    max_value VARCHAR(100),
    enum_values TEXT,
    default_value VARCHAR(255),
    field_order INTEGER,
    create_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3),
    update_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3),
    UNIQUE (interface_name, field_name) CONSTRAINT uk_interface_field_meta
);

-- 创建索引
CREATE INDEX idx_interface_name_meta ON vgop_field_metadata(interface_name);
CREATE INDEX idx_field_name_meta ON vgop_field_metadata(field_name); 