package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * IMSI校验规则
 * 检查IMSI格式是否正确
 */
@Component
public class ImsiRule extends AbstractValidationRule {
    
    /**
     * IMSI正则表达式
     * IMSI通常是15位数字
     */
    private static final Pattern IMSI_PATTERN = Pattern.compile("^\\d{15}$");
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     */
    public ImsiRule(String fieldName) {
        super(
                "common." + fieldName + ".imsi",
                "IMSI格式校验",
                "检查IMSI格式是否正确",
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.FORMAT
        );
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public ImsiRule() {
        this("imsi");
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 如果值为空，则跳过校验（由RequiredFieldRule处理）
        if (StringUtils.isBlank(value)) {
            return createValidResult();
        }
        
        // 校验IMSI格式
        if (!IMSI_PATTERN.matcher(value).matches()) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 的值 (%s) 不是有效的IMSI", getFieldName(), value),
                    context
            );
        }
        
        return createValidResult();
    }
} 