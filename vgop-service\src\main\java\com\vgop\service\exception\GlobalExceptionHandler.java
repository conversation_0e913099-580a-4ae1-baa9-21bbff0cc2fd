package com.vgop.service.exception;

import com.vgop.service.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(VgopException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleVgopException(VgopException e, HttpServletRequest request) {
        log.error("业务异常 [{}] {}: {}", request.getRequestURI(), e.getErrorCode(), e.getMessage(), e);
        ApiResponse<Void> response = ApiResponse.error(500, "[" + e.getErrorCode() + "] " + e.getMessage());
        response.setRequestId(getRequestId(request));
        return response;
    }
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleValidationException(Exception e, HttpServletRequest request) {
        String message;
        if (e instanceof MethodArgumentNotValidException) {
            message = ((MethodArgumentNotValidException) e).getBindingResult().getFieldErrors().stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.joining("; "));
        } else {
            message = ((BindException) e).getFieldErrors().stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.joining("; "));
        }
        
        log.warn("参数校验失败 [{}]: {}", request.getRequestURI(), message);
        ApiResponse<Void> response = ApiResponse.error(400, "参数校验失败: " + message);
        response.setRequestId(getRequestId(request));
        return response;
    }
    
    /**
     * 处理参数类型转换异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String message = String.format("参数类型错误: '%s' 应该是 '%s' 类型", 
                e.getName(), e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "未知");
        log.warn("参数类型错误 [{}]: {}", request.getRequestURI(), message);
        ApiResponse<Void> response = ApiResponse.error(400, message);
        response.setRequestId(getRequestId(request));
        return response;
    }
    
    /**
     * 处理SQL异常
     */
    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleSQLException(SQLException e, HttpServletRequest request) {
        log.error("数据库异常 [{}]: {}", request.getRequestURI(), e.getMessage(), e);
        ApiResponse<Void> response = ApiResponse.error(500, "数据库操作异常");
        response.setRequestId(getRequestId(request));
        return response;
    }
    
    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常 [{}]: {}", request.getRequestURI(), e.getMessage(), e);
        ApiResponse<Void> response = ApiResponse.error(500, "系统内部错误");
        response.setRequestId(getRequestId(request));
        return response;
    }
    
    /**
     * 获取请求ID
     */
    private String getRequestId(HttpServletRequest request) {
        String requestId = request.getHeader("X-Request-ID");
        return requestId != null ? requestId : String.valueOf(System.currentTimeMillis());
    }
} 