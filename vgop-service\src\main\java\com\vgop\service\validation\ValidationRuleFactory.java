package com.vgop.service.validation;

import com.vgop.service.validation.rules.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 校验规则工厂
 * 用于创建各种校验规则
 */
@Component
public class ValidationRuleFactory {
    
    /**
     * 为字段创建必填校验规则
     * 
     * @param fieldName 字段名
     * @return 校验规则
     */
    public ValidationRule createRequiredRule(String fieldName) {
        return new RequiredFieldRule(fieldName);
    }
    
    /**
     * 为字段创建长度校验规则
     * 
     * @param fieldName 字段名
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 校验规则
     */
    public ValidationRule createLengthRule(String fieldName, Integer minLength, Integer maxLength) {
        return new LengthRule(fieldName, minLength, maxLength);
    }
    
    /**
     * 为字段创建格式校验规则
     * 
     * @param fieldName 字段名
     * @param regex 正则表达式
     * @return 校验规则
     */
    public ValidationRule createFormatRule(String fieldName, String regex) {
        return new FormatRule(fieldName, regex);
    }
    
    /**
     * 为字段创建范围校验规则
     * 
     * @param fieldName 字段名
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 校验规则
     */
    public ValidationRule createRangeRule(String fieldName, String minValue, String maxValue) {
        return new RangeRule(fieldName, minValue, maxValue);
    }
    
    /**
     * 为字段创建手机号校验规则
     * 
     * @param fieldName 字段名
     * @return 校验规则
     */
    public ValidationRule createMobilePhoneRule(String fieldName) {
        return new MobilePhoneRule(fieldName);
    }
    
    /**
     * 为字段创建IMSI校验规则
     * 
     * @param fieldName 字段名
     * @return 校验规则
     */
    public ValidationRule createImsiRule(String fieldName) {
        return new ImsiRule(fieldName);
    }
    
    /**
     * 为字段创建IMEI校验规则
     * 
     * @param fieldName 字段名
     * @return 校验规则
     */
    public ValidationRule createImeiRule(String fieldName) {
        return new ImeiRule(fieldName);
    }
    
    /**
     * 为字段创建日期格式校验规则
     * 
     * @param fieldName 字段名
     * @param dateFormat 日期格式
     * @return 校验规则
     */
    public ValidationRule createDateFormatRule(String fieldName, String dateFormat) {
        return new DateFormatRule(fieldName, dateFormat);
    }
    
    /**
     * 为字段创建枚举值校验规则
     * 
     * @param fieldName 字段名
     * @param enumValues 枚举值（逗号分隔）
     * @return 校验规则
     */
    public ValidationRule createEnumValueRule(String fieldName, String enumValues) {
        return new EnumValueRule(fieldName, enumValues);
    }
    
    /**
     * 根据字段元数据创建校验规则
     * 
     * @param metadata 字段元数据
     * @return 校验规则列表
     */
    public List<ValidationRule> createRulesFromMetadata(FieldMetadata metadata) {
        List<ValidationRule> rules = new ArrayList<>();
        String fieldName = metadata.getFieldName();
        
        // 必填校验
        if (metadata.isRequired()) {
            rules.add(createRequiredRule(fieldName));
        }
        
        // 长度校验
        if (metadata.getMinLength() != null || metadata.getMaxLength() != null) {
            rules.add(createLengthRule(fieldName, metadata.getMinLength(), metadata.getMaxLength()));
        }
        
        // 格式校验
        if (metadata.getPattern() != null) {
            rules.add(createFormatRule(fieldName, metadata.getPattern()));
        }
        
        // 范围校验
        if (metadata.getMinValue() != null || metadata.getMaxValue() != null) {
            rules.add(createRangeRule(fieldName, metadata.getMinValue(), metadata.getMaxValue()));
        }
        
        // 枚举值校验
        if (metadata.getEnumValues() != null) {
            rules.add(createEnumValueRule(fieldName, metadata.getEnumValues()));
        }
        
        // 根据字段类型添加特定规则
        if (metadata.getFieldType() != null) {
            switch (metadata.getFieldType()) {
                case MOBILE:
                    rules.add(createMobilePhoneRule(fieldName));
                    break;
                case IMSI:
                    rules.add(createImsiRule(fieldName));
                    break;
                case IMEI:
                    rules.add(createImeiRule(fieldName));
                    break;
                case DATE:
                    rules.add(createDateFormatRule(fieldName, "yyyyMMdd"));
                    break;
                case TIMESTAMP:
                    rules.add(createDateFormatRule(fieldName, "yyyyMMddHHmmss"));
                    break;
                default:
                    break;
            }
        }
        
        return rules;
    }
} 