# 脚本 `VGOP1-R2.10-24202month.sh` SQL逻辑分析

-   **脚本用途**: 抽取截止到**上月末**的**全量**有效用户副号码信息。
-   **数据来源**: `mcn_user_minor` (用户副号码信息表)。
-   **核心逻辑**:
    -   `openingtime < ${endtime}`: 筛选所有开户时间早于执行月第一天的记录，实现全量抽取。
    -   `phonenumber != '' and phonenumber is not null`: 确保副号已绑定到主号。
    -   `mcnnum in (1,2,3)`: 筛选特定类型的副号码。
-   **输出内容**: 全量的副号码详细信息。
-   **说明**: 该脚本生成的是**全量快照**数据，逻辑与日统计中的 `VGOP1-R2.10-24202day.sh` 脚本一致，可用于月度归档。
