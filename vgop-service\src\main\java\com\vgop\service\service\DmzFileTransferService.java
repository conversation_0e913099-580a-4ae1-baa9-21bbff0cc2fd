package com.vgop.service.service;

import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.dto.FileTransferRequest;
import com.vgop.service.dto.FileTransferResponse;
import com.vgop.service.exception.VgopException;
import com.vgop.service.sftp.SftpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * DMZ文件中转服务
 * 负责接收内网文件清单通知，在DMZ本地查找文件并转发到下游系统
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DmzFileTransferService {
    
    private final VgopAppConfig appConfig;
    
    /**
     * 处理文件中转请求
     * 
     * @param request 文件传输请求
     * @return 传输结果
     */
    public FileTransferResponse processFileTransfer(FileTransferRequest request) {
        log.info("开始处理文件中转请求 - 任务ID: {}, 接口ID: {}, 文件数: {}", 
                 request.getTaskId(), request.getInterfaceId(), 
                 request.getFileList() != null ? request.getFileList().size() : 0);
        
        LocalDateTime startTime = LocalDateTime.now();
        
        FileTransferResponse.FileTransferResponseBuilder responseBuilder = FileTransferResponse.builder()
                .taskId(request.getTaskId())
                .responseTime(LocalDateTime.now())
                .startTime(startTime)
                .totalFiles(request.getFileList() != null ? request.getFileList().size() : 0)
                .successFiles(0)
                .failedFiles(0)
                .notFoundFiles(0)
                .successFileList(new ArrayList<>())
                .failedFileList(new ArrayList<>());
        
        try {
            // 验证DMZ配置
            if (appConfig.getDmz() == null || appConfig.getDmz().getDownstreamSftp() == null) {
                throw new VgopException("DMZ配置或下游SFTP配置未设置");
            }
            
            // 验证文件清单
            if (request.getFileList() == null || request.getFileList().isEmpty()) {
                log.warn("文件清单为空，无需处理");
                return responseBuilder
                        .status("SUCCESS")
                        .endTime(LocalDateTime.now())
                        .message("文件清单为空，无需处理")
                        .build();
            }
            
            // 处理每个文件
            List<FileTransferResponse.FileTransferResult> successResults = new ArrayList<>();
            List<FileTransferResponse.FileTransferResult> failedResults = new ArrayList<>();
            
            for (FileTransferRequest.FileInfo fileInfo : request.getFileList()) {
                FileTransferResponse.FileTransferResult result = transferSingleFile(fileInfo, request);
                
                if ("SUCCESS".equals(result.getStatus())) {
                    successResults.add(result);
                } else {
                    failedResults.add(result);
                }
            }
            
            // 计算统计信息
            int successCount = successResults.size();
            int failedCount = failedResults.size();
            int notFoundCount = (int) failedResults.stream()
                    .filter(r -> "NOT_FOUND".equals(r.getStatus()))
                    .count();
            
            // 确定整体状态
            String overallStatus;
            if (successCount == request.getFileList().size()) {
                overallStatus = "SUCCESS";
            } else if (successCount == 0) {
                overallStatus = "FAILED";
            } else {
                overallStatus = "PARTIAL";
            }
            
            LocalDateTime endTime = LocalDateTime.now();
            
            String message = String.format("文件中转处理完成 - 总计: %d, 成功: %d, 失败: %d, 未找到: %d", 
                                          request.getFileList().size(), successCount, failedCount, notFoundCount);
            
            return responseBuilder
                    .status(overallStatus)
                    .endTime(endTime)
                    .successFiles(successCount)
                    .failedFiles(failedCount)
                    .notFoundFiles(notFoundCount)
                    .successFileList(successResults)
                    .failedFileList(failedResults)
                    .message(message)
                    .build();
                    
        } catch (Exception e) {
            log.error("文件中转处理异常 - 任务ID: {}", request.getTaskId(), e);
            
            return responseBuilder
                    .status("FAILED")
                    .endTime(LocalDateTime.now())
                    .errorDetail(e.getMessage())
                    .message("文件中转处理失败: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 传输单个文件
     * 
     * @param fileInfo 文件信息
     * @param request 原始请求
     * @return 传输结果
     */
    private FileTransferResponse.FileTransferResult transferSingleFile(
            FileTransferRequest.FileInfo fileInfo, FileTransferRequest request) {
        
        LocalDateTime transferStartTime = LocalDateTime.now();
        
        FileTransferResponse.FileTransferResult.FileTransferResultBuilder resultBuilder = 
                FileTransferResponse.FileTransferResult.builder()
                        .fileName(fileInfo.getFileName())
                        .transferStartTime(transferStartTime);
        
        try {
            // 1. 构建DMZ本地文件路径
            String dmzLocalPath = buildDmzLocalFilePath(fileInfo, request);
            resultBuilder.localFilePath(dmzLocalPath);
            
            // 2. 检查文件是否存在
            File localFile = new File(dmzLocalPath);
            if (!localFile.exists()) {
                log.warn("DMZ本地文件不存在: {}", dmzLocalPath);
                return resultBuilder
                        .status("NOT_FOUND")
                        .transferEndTime(LocalDateTime.now())
                        .errorReason("DMZ本地文件不存在: " + dmzLocalPath)
                        .build();
            }
            
            // 3. 验证文件大小和MD5（可选）
            long actualFileSize = localFile.length();
            if (fileInfo.getFileSize() != null && !fileInfo.getFileSize().equals(actualFileSize)) {
                log.warn("文件大小不匹配 - 期望: {}, 实际: {}, 文件: {}", 
                         fileInfo.getFileSize(), actualFileSize, dmzLocalPath);
            }
            
            // 4. 构建下游SFTP路径
            String remoteFilePath = buildDownstreamRemoteFilePath(fileInfo, request);
            resultBuilder.remoteFilePath(remoteFilePath);
            
            // 5. 执行SFTP传输
            VgopAppConfig.DmzConfig.DownstreamSftpConfig sftpConfig = appConfig.getDmz().getDownstreamSftp();
            uploadWithRetry(sftpConfig, dmzLocalPath, remoteFilePath);
            
            LocalDateTime transferEndTime = LocalDateTime.now();
            long duration = java.time.Duration.between(transferStartTime, transferEndTime).toMillis();
            
            log.info("文件传输成功: {} -> {}, 耗时: {}ms", dmzLocalPath, remoteFilePath, duration);
            
            return resultBuilder
                    .status("SUCCESS")
                    .transferEndTime(transferEndTime)
                    .transferDuration(duration)
                    .fileSize(actualFileSize)
                    .build();
                    
        } catch (Exception e) {
            LocalDateTime transferEndTime = LocalDateTime.now();
            long duration = java.time.Duration.between(transferStartTime, transferEndTime).toMillis();
            
            log.error("文件传输失败: {}", fileInfo.getFileName(), e);
            
            return resultBuilder
                    .status("FAILED")
                    .transferEndTime(transferEndTime)
                    .transferDuration(duration)
                    .errorReason(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 构建DMZ本地文件路径
     * 
     * @param fileInfo 文件信息
     * @param request 请求信息
     * @return DMZ本地文件路径
     */
    private String buildDmzLocalFilePath(FileTransferRequest.FileInfo fileInfo, FileTransferRequest request) {
        String dmzLocalDir = appConfig.getDmz().getLocalFileDirectory();
        if (!dmzLocalDir.endsWith("/")) {
            dmzLocalDir += "/";
        }
        
        // 按日期组织目录结构: /data/vgop/files/20250620/
        dmzLocalDir += request.getDateId() + "/" + (request.getTaskType().equals("daily") ? "day" : "month") + "/" ;
        
        return dmzLocalDir + fileInfo.getFileName();
    }
    
    /**
     * 构建下游SFTP远程文件路径
     * 
     * @param fileInfo 文件信息
     * @param request 请求信息
     * @return 下游SFTP远程文件路径
     */
    private String buildDownstreamRemoteFilePath(FileTransferRequest.FileInfo fileInfo, FileTransferRequest request) {
        String remoteBasePath = appConfig.getDmz().getDownstreamSftp().getRemoteBasePath();
        if (!remoteBasePath.endsWith("/")) {
            remoteBasePath += "/";
        }
        
        // 远程路径结构: /remote/base/path/20250620/filename.dat
        return remoteBasePath + request.getDateId() + "/" + fileInfo.getFileName();
    }
    
    /**
     * 带重试的SFTP上传
     * 
     * @param sftpConfig SFTP配置
     * @param localFilePath 本地文件路径
     * @param remoteFilePath 远程文件路径
     */
    private void uploadWithRetry(VgopAppConfig.DmzConfig.DownstreamSftpConfig sftpConfig, 
                                String localFilePath, String remoteFilePath) throws VgopException {
        int maxRetries = sftpConfig.getRetryTimes();
        int attempt = 0;
        Exception lastException = null;
        
        // 转换为SftpConfig格式（复用现有的SftpUtil）
        VgopAppConfig.SftpConfig adaptedConfig = new VgopAppConfig.SftpConfig();
        adaptedConfig.setHost(sftpConfig.getHost());
        adaptedConfig.setPort(sftpConfig.getPort());
        adaptedConfig.setUsername(sftpConfig.getUsername());
        adaptedConfig.setPassword(sftpConfig.getPassword());
        adaptedConfig.setConnectionTimeout(sftpConfig.getConnectionTimeout());
        
        while (attempt <= maxRetries) {
            try {
                SftpUtil.upload(adaptedConfig, localFilePath, remoteFilePath);
                return; // 成功，退出方法
                
            } catch (VgopException e) {
                lastException = e;
                attempt++;
                log.error("下游SFTP上传失败 (尝试 {}/{}): {} -> {}", 
                         attempt, maxRetries + 1, localFilePath, remoteFilePath, e);
                
                if (attempt <= maxRetries) {
                    try {
                        Thread.sleep(1000 * attempt); // 等待后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new VgopException("SFTP上传在重试等待中被中断", ie);
                    }
                }
            }
        }
        
        throw new VgopException("下游SFTP上传最终失败，已达最大重试次数: " + localFilePath, lastException);
    }
    
    /**
     * 计算文件MD5值
     * 
     * @param filePath 文件路径
     * @return MD5值
     */
    private String calculateMD5(String filePath) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] fileBytes = Files.readAllBytes(Paths.get(filePath));
            byte[] hashBytes = md.digest(fileBytes);
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
            
        } catch (Exception e) {
            log.warn("计算文件MD5失败: {}", filePath, e);
            return null;
        }
    }
} 