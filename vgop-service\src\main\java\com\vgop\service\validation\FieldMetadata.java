package com.vgop.service.validation;

import lombok.Builder;
import lombok.Data;

/**
 * 字段元数据类
 * 描述字段的属性和约束
 */
@Data
@Builder
public class FieldMetadata {
    /**
     * 字段名
     */
    private String fieldName;
    
    /**
     * 字段描述
     */
    private String description;
    
    /**
     * 字段类型
     */
    private FieldType fieldType;
    
    /**
     * 是否必填
     */
    private boolean required;
    
    /**
     * 最大长度
     */
    private Integer maxLength;
    
    /**
     * 最小长度
     */
    private Integer minLength;
    
    /**
     * 正则表达式模式
     */
    private String pattern;
    
    /**
     * 最小值（数值类型）
     */
    private String minValue;
    
    /**
     * 最大值（数值类型）
     */
    private String maxValue;
    
    /**
     * 枚举值列表（逗号分隔）
     */
    private String enumValues;
    
    /**
     * 默认值
     */
    private String defaultValue;
    
    /**
     * 字段顺序
     */
    private int order;
    
    /**
     * 字段类型枚举
     */
    public enum FieldType {
        /**
         * 字符串
         */
        STRING,
        
        /**
         * 整数
         */
        INTEGER,
        
        /**
         * 浮点数
         */
        DECIMAL,
        
        /**
         * 日期（格式：yyyyMMdd）
         */
        DATE,
        
        /**
         * 时间戳（格式：yyyyMMddHHmmss）
         */
        TIMESTAMP,
        
        /**
         * 布尔值（0/1）
         */
        BOOLEAN,
        
        /**
         * 手机号
         */
        MOBILE,
        
        /**
         * IMSI
         */
        IMSI,
        
        /**
         * IMEI
         */
        IMEI
    }
} 