package com.vgop.service.service;

import com.vgop.service.config.DatabaseType;
import com.vgop.service.exception.VgopException;
import com.vgop.service.util.DatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

/**
 * UNLOAD执行服务
 * 负责执行数据库的UNLOAD命令，将查询结果导出到文件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class UnloadExecutorService {
    
    private final JdbcTemplate primaryJdbcTemplate;
    private final DatabaseUtil databaseUtil;
    
    @Autowired
    public UnloadExecutorService(@Qualifier("primaryJdbcTemplate") JdbcTemplate primaryJdbcTemplate,
                                DatabaseUtil databaseUtil) {
        this.primaryJdbcTemplate = primaryJdbcTemplate;
        this.databaseUtil = databaseUtil;
    }
    
    /**
     * 执行UNLOAD命令
     * 
     * @param sql SQL查询语句
     * @param outputPath 输出文件路径
     * @param delimiter 字段分隔符（默认为|）
     * @return 导出是否成功
     */
    public boolean executeUnload(String sql, String outputPath, String delimiter) {
        // 检查数据库是否支持UNLOAD
        if (!databaseUtil.supportsUnload()) {
            DatabaseType dbType = databaseUtil.getDatabaseType();
            if (dbType == DatabaseType.H2) {
                // 开发环境使用H2数据库，模拟UNLOAD操作
                return executeH2Export(sql, outputPath, delimiter);
            }
            throw new VgopException("当前数据库 " + dbType.getName() + " 不支持UNLOAD命令");
        }
        
        // 确保输出目录存在
        ensureDirectoryExists(outputPath);
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 构建UNLOAD命令
            String lockModeStatement = databaseUtil.getLockModeStatement();
            String unloadStatement = databaseUtil.buildUnloadStatement(sql, outputPath, delimiter);
            String fullCommand = lockModeStatement + "\n" + unloadStatement;
            
            log.info("执行UNLOAD命令通过dbaccess: {}", fullCommand);
            
            // 通过命令行执行dbaccess
            boolean success = executeDbAccessCommand(fullCommand);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            if (success) {
                // 检查文件是否生成
                File outputFile = new File(outputPath);
                if (outputFile.exists()) {
                    long fileSize = outputFile.length();
                    log.info("UNLOAD执行成功 - 文件: {}, 大小: {} bytes, 耗时: {} ms", 
                            outputPath, fileSize, duration);
                    return true;
                } else {
                    log.error("UNLOAD执行后文件未生成: {}", outputPath);
                    return false;
                }
            } else {
                log.error("dbaccess命令执行失败");
                return false;
            }
            
        } catch (Exception e) {
            log.error("UNLOAD执行失败 - SQL: {}, 错误: {}", sql, e.getMessage(), e);
            throw new VgopException("UNLOAD执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 异步执行UNLOAD命令（用于大数据量导出）
     * 
     * @param sql SQL查询语句
     * @param outputPath 输出文件路径
     * @param delimiter 字段分隔符
     * @param timeoutMinutes 超时时间（分钟）
     * @return 导出是否成功
     */
    public boolean executeUnloadAsync(String sql, String outputPath, String delimiter, int timeoutMinutes) {
        // 使用单独的线程执行UNLOAD
        Thread unloadThread = new Thread(() -> {
            try {
                executeUnload(sql, outputPath, delimiter);
            } catch (Exception e) {
                log.error("异步UNLOAD执行失败", e);
            }
        });
        
        unloadThread.setName("UnloadExecutor-" + System.currentTimeMillis());
        unloadThread.start();
        
        try {
            // 等待执行完成
            unloadThread.join(TimeUnit.MINUTES.toMillis(timeoutMinutes));
            
            if (unloadThread.isAlive()) {
                log.warn("UNLOAD执行超时（{}分钟），尝试中断", timeoutMinutes);
                unloadThread.interrupt();
                return false;
            }
            
            // 检查文件是否生成
            File outputFile = new File(outputPath);
            return outputFile.exists() && outputFile.length() > 0;
            
        } catch (InterruptedException e) {
            log.error("等待UNLOAD执行时被中断", e);
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    /**
     * H2数据库的导出实现（开发环境用）
     */
    private boolean executeH2Export(String sql, String outputPath, String delimiter) {
        log.info("H2数据库模拟UNLOAD操作 - SQL: {}, 输出: {}", sql, outputPath);
        
        try {
            // 确保目录存在
            ensureDirectoryExists(outputPath);
            
            // 使用CSVWRITE函数导出数据
            String h2ExportSql = String.format(
                "CALL CSVWRITE('%s', '%s', 'charset=UTF-8 fieldDelimiter=%s')",
                outputPath, sql, delimiter != null ? delimiter : "|"
            );
            
            primaryJdbcTemplate.execute(h2ExportSql);
            
            File outputFile = new File(outputPath);
            if (outputFile.exists()) {
                log.info("H2导出成功 - 文件: {}, 大小: {} bytes", outputPath, outputFile.length());
                return true;
            }
            
        } catch (Exception e) {
            log.error("H2导出失败", e);
        }
        
        return false;
    }
    
    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(String filePath) {
        try {
            Path path = Paths.get(filePath);
            Path parentDir = path.getParent();
            
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                log.info("创建目录: {}", parentDir);
            }
        } catch (IOException e) {
            log.error("创建目录失败: {}", filePath, e);
            throw new VgopException("创建目录失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除临时文件（如果存在）
     */
    public void deleteTempFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.info("删除临时文件: {}", filePath);
            }
        } catch (IOException e) {
            log.warn("删除临时文件失败: {}", filePath, e);
        }
    }
    
    /**
     * 通过命令行执行dbaccess命令
     * 
     * @param sqlCommand SQL命令内容
     * @return 执行是否成功
     */
    private boolean executeDbAccessCommand(String sqlCommand) {
        try {
            // 获取数据库连接信息
            String databaseName = databaseUtil.getDatabaseName();
            
            // 构建dbaccess命令
            ProcessBuilder processBuilder = new ProcessBuilder("dbaccess", databaseName);
            processBuilder.redirectErrorStream(true);
            
            log.debug("启动dbaccess进程，数据库: {}", databaseName);
            Process process = processBuilder.start();
            
            // 向进程输入SQL命令
            try (var outputStream = process.getOutputStream()) {
                outputStream.write(sqlCommand.getBytes());
                outputStream.flush();
            }
            
            // 读取进程输出
            StringBuilder output = new StringBuilder();
            try (var inputStream = process.getInputStream();
                 var reader = new java.io.BufferedReader(new java.io.InputStreamReader(inputStream))) {
                
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            // 等待进程完成
            int exitCode = process.waitFor();
            
            String processOutput = output.toString();
            log.debug("dbaccess进程输出: {}", processOutput);
            
            if (exitCode == 0) {
                log.info("dbaccess命令执行成功");
                return true;
            } else {
                log.error("dbaccess命令执行失败，退出码: {}, 输出: {}", exitCode, processOutput);
                return false;
            }
            
        } catch (Exception e) {
            log.error("执行dbaccess命令时发生异常", e);
            return false;
        }
    }
} 