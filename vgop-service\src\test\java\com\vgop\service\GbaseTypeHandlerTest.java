package com.vgop.service;

import com.vgop.service.config.GbaseLocalDateTimeTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GBase LocalDateTime TypeHandler 测试类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class GbaseTypeHandlerTest {

    @Test
    public void testSetNonNullParameter() throws Exception {
        // 准备测试数据
        GbaseLocalDateTimeTypeHandler handler = new GbaseLocalDateTimeTypeHandler();
        PreparedStatement ps = mock(PreparedStatement.class);
        LocalDateTime dateTime = LocalDateTime.of(2025, 6, 13, 10, 59, 0);
        
        // 执行测试
        handler.setNonNullParameter(ps, 1, dateTime, JdbcType.TIMESTAMP);
        
        // 验证结果
        verify(ps).setTimestamp(1, Timestamp.valueOf(dateTime));
    }

    @Test
    public void testGetNullableResultByColumnName() throws Exception {
        // 准备测试数据
        GbaseLocalDateTimeTypeHandler handler = new GbaseLocalDateTimeTypeHandler();
        ResultSet rs = mock(ResultSet.class);
        LocalDateTime expected = LocalDateTime.of(2025, 6, 13, 10, 59, 0);
        Timestamp timestamp = Timestamp.valueOf(expected);
        
        when(rs.getTimestamp("start_time")).thenReturn(timestamp);
        
        // 执行测试
        LocalDateTime result = handler.getNullableResult(rs, "start_time");
        
        // 验证结果
        assertEquals(expected, result);
    }

    @Test
    public void testGetNullableResultWithNull() throws Exception {
        // 准备测试数据
        GbaseLocalDateTimeTypeHandler handler = new GbaseLocalDateTimeTypeHandler();
        ResultSet rs = mock(ResultSet.class);
        
        when(rs.getTimestamp("start_time")).thenReturn(null);
        
        // 执行测试
        LocalDateTime result = handler.getNullableResult(rs, "start_time");
        
        // 验证结果
        assertNull(result);
    }

    @Test
    public void testGetNullableResultByColumnIndex() throws Exception {
        // 准备测试数据
        GbaseLocalDateTimeTypeHandler handler = new GbaseLocalDateTimeTypeHandler();
        ResultSet rs = mock(ResultSet.class);
        LocalDateTime expected = LocalDateTime.of(2025, 6, 13, 10, 59, 0);
        Timestamp timestamp = Timestamp.valueOf(expected);
        
        when(rs.getTimestamp(1)).thenReturn(timestamp);
        
        // 执行测试
        LocalDateTime result = handler.getNullableResult(rs, 1);
        
        // 验证结果
        assertEquals(expected, result);
    }
} 