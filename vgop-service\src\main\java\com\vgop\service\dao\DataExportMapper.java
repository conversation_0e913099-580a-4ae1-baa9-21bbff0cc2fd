package com.vgop.service.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 数据导出数据访问接口
 */
@Mapper
public interface DataExportMapper {
    
    /**
     * 调用业务分析统计存储过程
     */
    void callBmsSpVgopBanalyse(@Param("debugFile") String debugFile,
                               @Param("traceFlag") String traceFlag,
                               @Param("taskId") String taskId);
    
    /**
     * 调用业务分析存储过程
     */
    void callBanalyseStoredProcedure(@Param("beforeDay") String beforeDay,
                                    @Param("traceFlag") String traceFlag,
                                    @Param("sourceDbName") String sourceDbName);
    
    /**
     * 查询业务分析统计结果
     */
    List<Map<String, Object>> selectBanalyseData(@Param("statTime") String statTime);
    
    /**
     * 查询每日新增主号用户信息
     */
    List<Map<String, Object>> selectDailyNewMajorUsers(@Param("startTime") String startTime,
                                                       @Param("endTime") String endTime);
    
    /**
     * 查询副号用户信息
     */
    List<Map<String, Object>> selectMinorUsers(@Param("mcnNums") String mcnNums);
    
    /**
     * 查询每日用户活动日志
     */
    List<Map<String, Object>> selectDailyUserActivity(@Param("startTime") String startTime,
                                                      @Param("endTime") String endTime);
    
    /**
     * 查询每日通话话单记录
     */
    List<Map<String, Object>> selectDailyCallLogs(@Param("startTime") String startTime,
                                                  @Param("endTime") String endTime);
    
    /**
     * 查询每日短信日志
     */
    List<Map<String, Object>> selectDailySmsLogs(@Param("startTime") String startTime,
                                                @Param("endTime") String endTime);
    
    /**
     * 查询每日新增实体副号用户信息
     */
    List<Map<String, Object>> selectDailyNewSecUsers(@Param("startTime") String startTime,
                                                     @Param("endTime") String endTime);
    
    /**
     * 查询维表数据 - 服务类型
     */
    List<Map<String, Object>> selectServTypeData();
    
    /**
     * 查询维表数据 - 渠道信息
     */
    List<Map<String, Object>> selectChannelData();
    
    /**
     * 查询维表数据 - 关机原因
     */
    List<Map<String, Object>> selectShutdownData();
    
    /**
     * 查询维表数据 - MCN类型
     */
    List<Map<String, Object>> selectMcnTypeData();
    
    /**
     * 查询主号用户全量快照（月统计）
     */
    List<Map<String, Object>> selectMajorUsersSnapshot(@Param("endDate") String endDate);
    
    /**
     * 查询副号用户全量快照（月统计）
     */
    List<Map<String, Object>> selectMinorUsersSnapshot(@Param("endDate") String endDate);
    
    /**
     * 查询月度操作日志
     */
    List<Map<String, Object>> selectMonthlyOpLogs(@Param("startTime") String startTime,
                                                  @Param("endTime") String endTime);
    
    /**
     * 查询特定状态实体副号用户全量快照
     */
    List<Map<String, Object>> selectSecUsersSnapshot(@Param("businessState") String businessState);
    
    /**
     * 执行自定义SQL查询
     * 
     * @param sql 自定义SQL语句
     * @return 查询结果
     */
    @Select("${sql}")
    List<Map<String, Object>> executeCustomQuery(@Param("sql") String sql);
} 