# 脚本 `VGOP1-R2.10-24206.sh` SQL逻辑分析

-   **脚本用途**: 抽取前一天发生的、被视为**有效的国内手机间**的短信/彩信记录。
-   **数据来源**: `mcn_smslog` (短信/彩信日志表)。
-   **核心逻辑**:
    -   `optime >= '${starttime}' and optime < '${endtime}'`: 按操作时间，筛选出前一天的短信记录。
    -   `length(phonenumber) = '11'`: 要求本端主号码长度为11位。
    -   `length(sendorreceNum) = 11 and sendorreceNum like '1%'`: 要求对端号码 (`sendorreceNum`) 长度为11位且以'1'开头。
-   **输出内容**: 清洗和过滤后的短信话单，包括话单类型（收/发）、主号码、副号码、对端号码和操作时间。
-   **说明**: 此脚本旨在筛选出国内手机号码之间的短信交互记录，排除了非标准号码或国际号码的短信。 