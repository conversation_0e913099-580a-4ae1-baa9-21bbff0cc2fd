package com.vgop.service.validation;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 错误记录实体类
 * 用于记录校验错误信息
 */
@Data
@Builder
public class ErrorRecord {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 数据日期
     */
    private String dataDate;
    
    /**
     * 行号
     */
    private Long rowNumber;
    
    /**
     * 字段名
     */
    private String fieldName;
    
    /**
     * 字段值
     */
    private String fieldValue;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 严重程度
     */
    private String severity;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 从校验结果创建错误记录
     * 
     * @param result 校验结果
     * @param context 校验上下文
     * @return 错误记录
     */
    public static ErrorRecord fromValidationResult(ValidationResult result, ValidationContext context) {
        return ErrorRecord.builder()
                .interfaceName(context.getInterfaceName())
                .fileName(context.getFileName())
                .dataDate(context.getDataDate())
                .rowNumber(context.getRowNumber())
                .fieldName(result.getFieldName())
                .fieldValue(result.getFieldValue())
                .errorMessage(result.getMessage())
                .ruleId(result.getRuleId())
                .ruleName(result.getRuleName())
                .severity(result.getSeverity().name())
                .createTime(LocalDateTime.now())
                .build();
    }
} 