package com.vgop.service.service;

import com.vgop.service.config.VgopProperties;
import com.vgop.service.dto.TaskExecutionRequest;
import com.vgop.service.dto.TaskExecutionResponse;
import com.vgop.service.entity.TaskConfig;
import com.vgop.service.exception.TaskExecutionException;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 任务调度服务
 * 实现Shell脚本中的主调度逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskSchedulerService {
    
    private final VgopDataExtractionService dataExtractionService;
    private final FileProcessingService fileProcessingService;
    private final RevisionService revisionService;
    private final VgopProperties vgopProperties;
    
    /**
     * 执行日统计任务
     * 对应 bms_VGOP_daystat.sh 脚本
     */
    @Async("taskExecutor")
    public CompletableFuture<TaskExecutionResponse> executeDayStatTask(TaskExecutionRequest request) {
        log.info("开始执行日统计任务: {}", request.getActionInstanceId());
        
        LocalDateTime startTime = LocalDateTime.now();
        TaskExecutionResponse response = TaskExecutionResponse.builder()
            .taskId(request.getActionInstanceId())
            .status(TaskConfig.TaskStatus.RUNNING)
            .startTime(startTime)
            .success(false)
            .build();
        
        try {
            List<String> generatedFiles = new ArrayList<>();
            
            // 1. VGOP1-R2.11-24101.sh - 业务分析统计
            String result1 = executeScript("VGOP1-R2.11-24101", request);
            if (result1 != null) generatedFiles.add(result1);
            
            // 2. VGOP1-R2.10-24201day.sh - 每日新增主号用户
            String result2 = executeScript("VGOP1-R2.10-24201day", request);
            if (result2 != null) generatedFiles.add(result2);
            
            // 3. VGOP1-R2.10-24202day.sh - 副号用户信息
            String result3 = executeScript("VGOP1-R2.10-24202day", request);
            if (result3 != null) generatedFiles.add(result3);
            
            // 4. VGOP1-R2.10-24203.sh - 每日用户活动日志
            String result4 = executeScript("VGOP1-R2.10-24203", request);
            if (result4 != null) generatedFiles.add(result4);
            
            // 5. VGOP1-R2.10-24205.sh - 每日通话话单
            String result5 = executeScript("VGOP1-R2.10-24205", request);
            if (result5 != null) generatedFiles.add(result5);
            
            // 6. VGOP1-R2.10-24206.sh - 每日短信日志
            String result6 = executeScript("VGOP1-R2.10-24206", request);
            if (result6 != null) generatedFiles.add(result6);
            
            // 7. 维表数据导出
            String result7 = executeScript("VGOP1-R2.13-24301", request); // 服务类型
            if (result7 != null) generatedFiles.add(result7);
            
            String result8 = executeScript("VGOP1-R2.13-24302", request); // 渠道信息
            if (result8 != null) generatedFiles.add(result8);
            
            String result9 = executeScript("VGOP1-R2.13-24303", request); // 关机原因
            if (result9 != null) generatedFiles.add(result9);
            
            String result10 = executeScript("VGOP1-R2.13-24304", request); // MCN类型
            if (result10 != null) generatedFiles.add(result10);
            
            // 8. VGOP1-R2.10-24207day.sh - 每日新增实体副号用户
            String result11 = executeScript("VGOP1-R2.10-24207day", request);
            if (result11 != null) generatedFiles.add(result11);
            
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            response.setStatus(TaskConfig.TaskStatus.SUCCESS);
            response.setEndTime(endTime);
            response.setExecutionTimeMs(executionTime);
            response.setGeneratedFiles(generatedFiles);
            response.setSuccess(true);
            response.setMessage(String.format("日统计任务执行成功，生成 %d 个文件", generatedFiles.size()));
            
            log.info("日统计任务执行完成: {}, 耗时: {}ms, 生成文件: {}", 
                    request.getActionInstanceId(), executionTime, generatedFiles.size());
            
        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            response.setStatus(TaskConfig.TaskStatus.FAILED);
            response.setEndTime(endTime);
            response.setExecutionTimeMs(executionTime);
            response.setErrorMessage(e.getMessage());
            response.setMessage("日统计任务执行失败: " + e.getMessage());
            
            log.error("日统计任务执行失败: {}", request.getActionInstanceId(), e);
        }
        
        return CompletableFuture.completedFuture(response);
    }
    
    /**
     * 执行月统计任务
     * 对应 bms_VGOP_monthstat.sh 脚本
     */
    @Async("taskExecutor")
    public CompletableFuture<TaskExecutionResponse> executeMonthStatTask(TaskExecutionRequest request) {
        log.info("开始执行月统计任务: {}", request.getActionInstanceId());
        
        LocalDateTime startTime = LocalDateTime.now();
        TaskExecutionResponse response = TaskExecutionResponse.builder()
            .taskId(request.getActionInstanceId())
            .status(TaskConfig.TaskStatus.RUNNING)
            .startTime(startTime)
            .success(false)
            .build();
        
        try {
            List<String> generatedFiles = new ArrayList<>();
            
            // 1. VGOP1-R2.10-24201month.sh - 主号用户全量快照
            String result1 = executeScript("VGOP1-R2.10-24201month", request);
            if (result1 != null) generatedFiles.add(result1);
            
            // 2. VGOP1-R2.10-24202month.sh - 副号用户全量快照
            String result2 = executeScript("VGOP1-R2.10-24202month", request);
            if (result2 != null) generatedFiles.add(result2);
            
            // 3. VGOP1-R2.10-24204.sh - 月度操作日志
            String result3 = executeScript("VGOP1-R2.10-24204", request);
            if (result3 != null) generatedFiles.add(result3);
            
            // 4. VGOP1-R2.10-24207month.sh - 实体副号用户全量快照
            String result4 = executeScript("VGOP1-R2.10-24207month", request);
            if (result4 != null) generatedFiles.add(result4);
            
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            response.setStatus(TaskConfig.TaskStatus.SUCCESS);
            response.setEndTime(endTime);
            response.setExecutionTimeMs(executionTime);
            response.setGeneratedFiles(generatedFiles);
            response.setSuccess(true);
            response.setMessage(String.format("月统计任务执行成功，生成 %d 个文件", generatedFiles.size()));
            
            log.info("月统计任务执行完成: {}, 耗时: {}ms, 生成文件: {}", 
                    request.getActionInstanceId(), executionTime, generatedFiles.size());
            
        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            response.setStatus(TaskConfig.TaskStatus.FAILED);
            response.setEndTime(endTime);
            response.setExecutionTimeMs(executionTime);
            response.setErrorMessage(e.getMessage());
            response.setMessage("月统计任务执行失败: " + e.getMessage());
            
            log.error("月统计任务执行失败: {}", request.getActionInstanceId(), e);
        }
        
        return CompletableFuture.completedFuture(response);
    }
    
    /**
     * 执行单个脚本对应的功能
     */
    private String executeScript(String scriptId, TaskExecutionRequest request) {
        try {
            log.info("执行脚本: {} - {}", scriptId, request.getActionInstanceId());
            
            // 根据脚本ID调用相应的数据提取和处理逻辑
            String unloadFilePath = dataExtractionService.extractData(scriptId, 
                    request.getActionInstanceId(), 
                    request.getImagePath(), 
                    request.getDateId(),
                    request.getTraceFlag(),
                    request.getSourceDbName());
            
            if (unloadFilePath == null) {
                log.warn("脚本 {} 没有生成数据文件", scriptId);
                return null;
            }
            
            // 文件处理和分割
            String dataDate = DateTimeUtil.extractDate(request.getDateId());
            String beforeDay = DateTimeUtil.calculateBeforeDay(request.getDateId());
            
            // 确定文件前缀和子路径
            String filePrefix = getFilePrefix(scriptId);
            String subPath = request.getTaskType() == TaskConfig.TaskType.DAY ? 
                vgopProperties.getFileProcessing().getDaySubPath() : 
                vgopProperties.getFileProcessing().getMonthSubPath();
            
            // 生成输出目录
            String outputDir = fileProcessingService.generateOutputDirectory(
                request.getImagePath(), beforeDay, subPath);
            fileProcessingService.ensureDirectoryExists(outputDir);
            
            // 获取版本号
            String tmpFileName = generateTmpFileName(filePrefix, beforeDay, scriptId);
            String revision = revisionService.getNextRevision(beforeDay, tmpFileName);
            
            // 处理和分割文件
            fileProcessingService.splitAndProcessFile(unloadFilePath, outputDir, 
                    filePrefix, scriptId, beforeDay, revision);
            
            log.info("脚本 {} 执行完成", scriptId);
            return outputDir;
            
        } catch (Exception e) {
            log.error("脚本 {} 执行失败", scriptId, e);
            throw new TaskExecutionException("脚本执行失败: " + scriptId, e);
        }
    }
    
    /**
     * 根据脚本ID获取文件前缀
     */
    private String getFilePrefix(String scriptId) {
        // 根据脚本逻辑文档的说明
        switch (scriptId) {
            case "VGOP1-R2.11-24101":    // 业务分析统计
            case "VGOP1-R2.10-24202day": // 副号用户信息
            case "VGOP1-R2.13-24301":   // 维表数据
            case "VGOP1-R2.13-24302":
            case "VGOP1-R2.13-24303":
            case "VGOP1-R2.13-24304":
            case "VGOP1-R2.10-24201month": // 主号用户全量快照
            case "VGOP1-R2.10-24202month": // 副号用户全量快照
            case "VGOP1-R2.10-24204":      // 月度操作日志
            case "VGOP1-R2.10-24207month": // 实体副号用户全量快照
                return "i_10000";
                
            case "VGOP1-R2.10-24201day": // 每日新增主号用户
            case "VGOP1-R2.10-24203":    // 每日用户活动日志
            case "VGOP1-R2.10-24205":    // 每日通话话单
            case "VGOP1-R2.10-24206":    // 每日短信日志
            case "VGOP1-R2.10-24207day": // 每日新增实体副号用户
                return "a_10000";
                
            default:
                return "i_10000"; // 默认前缀
        }
    }
    
    /**
     * 生成临时文件名
     */
    private String generateTmpFileName(String prefix, String dataDate, String scriptId) {
        return String.format("%s_%s_%s.unl", prefix, dataDate, scriptId);
    }
} 