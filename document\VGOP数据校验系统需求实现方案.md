# VGOP数据校验系统需求实现方案

## 目录

1. [项目概述](#1-项目概述)
2. [核心处理流程](#2-核心处理流程)
   - [系统总体流程图](#21-系统总体流程图)
   - [数据导出详细流程图](#22-数据导出详细流程图)
3. [模块功能设计](#3-模块功能设计)
4. [配置文件结构](#4-配置文件-applicationyml-结构)
5. [数据库表结构设计](#5-数据库表结构设计)
6. [核心类设计](#6-核心类设计)
7. [性能指标与优化](#7-性能指标与优化)
8. [技术栈](#8-技术栈)
9. [部署架构](#9-部署架构)
10. [数据校验流程详述](#10-数据校验流程详述)
    - [数据校验详细流程图](#101-数据校验详细流程图)
    - [告警处理流程图](#102-告警处理流程图)
11. [数据格式规范](#11-数据格式规范)
12. [关键要求总结](#12-关键要求总结)
13. [流程图总结](#13-流程图总结)

## 1. 项目概述

### 1.1. 目标

构建一个全自动化的、独立的后台服务，以替代现有的数据导出Shell脚本。该服务将承担数据导出、数据校验和文件传输三个阶段的完整工作：

- **数据导出阶段**：定时连接Informix数据库，执行多个并行的导出任务，将原始数据生成到本地的原始数据目录。
- **数据校验阶段**：在数据导出完成后，服务会读取这些原始数据文件，进行多维度校验，生成最终的合规交付文件，并将所有校验告警信息存入指定的数据库表中。
- **文件传输阶段**：只有在数据校验通过后，才通过SFTP将校验后的合规数据文件上传到DMZ主机（前置机）。

### 1.2. 系统特点

- **全流程自动化**：将原先分散的Shell脚本逻辑统一收归到一个Java应用中，实现端到端的自动化。
- **多任务并行**：能够并行处理多个数据接口的导出任务，提升效率。
- **双周期调度**：支持日数据（每日执行）和月数据（每月执行）两种调度周期。
- **原始数据保留**：保存数据库导出的原始文件，便于数据追溯和问题排查。
- **详细错误记录**：校验过程中发现的错误数据行会被完整记录到告警信息中。
- **格式兼容性**：确保导出的数据格式与现有系统完全一致。

### 1.3. 现有系统分析

基于Shell脚本分析，当前系统包含：
- **日数据接口**：11个（24101, 24201-24203, 24205-24207, 24301-24304）
- **月数据接口**：4个（24201月, 24202月, 24204, 24207月）
- **处理流程**：数据导出 → 文件分割 → SFTP上传
- **数据格式要求**：
  - 使用\200（八进制）作为字段分隔符
  - 每行末尾添加回车符\r
  - 第一个字段为行号（从1开始递增）
  - 数据字段与数据库查询结果字段顺序保持一致
  - 所有数据文件必须使用.dat扩展名

## 2. 核心处理流程

系统的主流程是一个清晰的三阶段工作流，由调度任务来编排。

### 2.1. 系统总体流程图

```mermaid
graph TD
    A[系统启动] --> B{调度触发判断}
    B -->|每日0点| C[日数据处理流程]
    B -->|每月1日5点| D[月数据处理流程]

    C --> E[阶段一：数据导出]
    D --> E

    E --> F[从Informix数据库<br/>并行导出多个接口数据]
    F --> G[文件分割处理<br/>每200万行一个文件]
    G --> H[生成.dat和.verf文件]

    H --> I[阶段二：数据校验]
    I --> J[读取原始数据文件进行校验]
    J --> K[字段格式校验<br/>数据量波动检测]
    K --> L[生成Excel报告<br/>记录告警信息]
    L --> M{校验是否通过?}
    M -->|失败| N[记录校验失败告警]
    M -->|成功| O[阶段三：文件传输]

    O --> P[通过SFTP上传到DMZ主机]
    P --> Q{传输是否成功?}
    Q -->|失败| R[记录传输失败告警]
    Q -->|成功| S[更新历史统计数据]

    N --> T[流程结束]
    R --> T
    S --> T

    style E fill:#e1f5fe
    style I fill:#e8f5e8
    style O fill:#f3e5f5
    style T fill:#fff3e0
```

**说明**：这是VGOP数据校验系统的总体业务流程图，清晰展示了系统的三个核心阶段。蓝色区域代表数据导出阶段，绿色区域代表数据校验阶段，紫色区域代表文件传输阶段。系统支持双周期调度，自动化处理日数据和月数据。注意：只有在数据校验通过后，才会进行文件传输，确保传输的数据质量。

### 2.2. 数据导出详细流程图

```mermaid
graph TD
    A[开始数据导出] --> B[计算数据日期<br/>处理前一天数据]
    B --> C[获取配置中的导出任务<br/>日数据11个接口<br/>月数据4个接口]

    C --> D[启动线程池并行处理]
    D --> E[单个接口导出任务开始]

    E --> F[查询并更新修订版本号<br/>操作bms_vgop_revtimes表]
    F --> G[创建输出目录]
    G --> H[生成SQL语句<br/>替换时间参数变量]
    H --> I[执行UNLOAD TO命令<br/>导出到.unl临时文件]

    I --> J{导出是否成功?}
    J -->|失败| K[记录导出失败告警<br/>到告警表]
    J -->|成功| L[获取文件总行数<br/>如果为0则设为1]

    L --> M[开始文件分割处理]
    M --> N[按200万行分割大文件]
    N --> O[处理每行数据：<br/>1.添加递增行号<br/>2.删除反斜杠和空格<br/>3.转换分隔符为\200<br/>4.添加回车符\r]

    O --> P[生成最终.dat文件]
    P --> Q[生成.verf校验文件<br/>记录文件信息]
    Q --> R[保留原始.unl文件<br/>供后续校验使用]

    R --> S[单个接口处理完成]
    K --> S

    S --> T{所有接口完成?}
    T -->|否| E
    T -->|是| U[数据导出阶段结束]

    style I fill:#ffecb3
    style O fill:#c8e6c9
    style P fill:#e1f5fe
```

**说明**：这个流程图详细展示了数据导出阶段的核心逻辑。系统通过线程池并行处理多个接口，每个接口都经过版本控制、SQL执行、文件分割和格式处理等步骤。黄色区域突出显示数据库操作，绿色区域显示数据处理逻辑，蓝色区域显示文件生成过程。

### 2.3. 主流程伪代码

```text
// --- 主流程伪代码 ---

FUNCTION executeDailyProcess():

    // ==========================================================
    // 阶段一：数据导出 (替代bms_VGOP_daystat.sh)
    // ==========================================================
    LOG "--- 阶段一：数据导出任务开始 ---"
    
    // 1. 计算数据日期（处理前一天的数据）
    dataDate = today - 1
    startTime = dataDate + "000000"
    endTime = today + "000000"
    
    // 2. 从配置中获取所有需要导出的日接口任务
    allDailyTasks = config.getDailyExportTasks()
    
    // 3. 使用线程池并行执行所有导出任务
    threadPool.executeParallel(allDailyTasks, (task) -> {
        exportSingleInterfaceData(task, dataDate, startTime, endTime)
    })
    
    // 4. 等待所有导出任务完成
    threadPool.waitForCompletion()
    LOG "--- 阶段一：所有数据导出任务完成 ---"

    // ==========================================================
    // 阶段二：数据校验
    // ==========================================================
    LOG "--- 阶段二：数据校验任务开始 ---"
    
    // 1. 从配置中获取所有需要校验的接口任务
    allValidationTasks = config.getAllValidationTasks()
    
    // 2. 串行或并行执行每个接口的校验流程
    validationSuccess = true
    for (task in allValidationTasks):
        if (!executeValidationForInterface(task, dataDate)):
            validationSuccess = false
        endif
    endfor
    
    // 3. 如果校验失败，不进行文件传输
    if (!validationSuccess):
        LOG "--- 阶段二：数据校验失败，终止处理流程 ---"
        return FAILED
    endif
    
    LOG "--- 阶段二：所有数据校验任务完成 ---"

    // ==========================================================
    // 阶段三：文件传输 (替代bms_VGOP_dayputter.sh)
    // ==========================================================
    LOG "--- 阶段三：文件传输任务开始 ---"
    
    // 1. 获取当日生成的所有文件路径（只传输校验通过的文件）
    localPath = config.getExportPath() + "/" + dataDate
    
    // 2. 通过SFTP上传文件到DMZ
    sftpService.uploadFiles(localPath, config.getRemotePath())
    
    LOG "--- 阶段三：文件传输任务完成 ---"

END FUNCTION

// --- 单个接口的数据导出子流程 ---
FUNCTION exportSingleInterfaceData(taskConfig, dataDate, startTime, endTime):
    LOG "开始导出接口: " + taskConfig.interfaceName
    try:
        // 1. 查询并更新修订版本号
        revisionTimes = getAndUpdateRevisionTimes(dataDate, taskConfig.interfaceName)
        
        // 2. 创建输出目录
        outputDir = createOutputDirectory(dataDate, taskConfig.cycleType)
        
        // 3. 生成UNLOAD文件路径
        tmpFileName = generateTempFileName(taskConfig, dataDate)
        unloadFilePath = outputDir + "/" + tmpFileName
        
        // 4. 执行数据库导出命令
        sql = taskConfig.sqlTemplate.replace("{startTime}", startTime)
                                   .replace("{endTime}", endTime)
        database.executeUnloadCommand(sql, unloadFilePath, taskConfig.delimiter)
        
        // 5. 分割大文件（每200万行一个文件）
        splitAndProcessFiles(unloadFilePath, taskConfig, dataDate, revisionTimes)
        
        // 6. 保留原始文件用于后续校验
        markFileForValidation(unloadFilePath, taskConfig.interfaceName)
        
        LOG "接口 " + taskConfig.interfaceName + " 导出成功"
    catch (error):
        LOG_ERROR "接口 " + taskConfig.interfaceName + " 导出失败: " + error.message
        // 记录导出失败的告警到数据库
        storeExportFailureAlert(taskConfig.interfaceName, error.message)
    endtry
END FUNCTION

// --- 文件分割和处理子流程 ---
FUNCTION splitAndProcessFiles(sourceFile, taskConfig, dataDate, revisionTimes):
    // 1. 获取文件总行数
    totalLines = getFileLineCount(sourceFile)
    if (totalLines == 0) totalLines = 1
    
    // 2. 初始化文件序号和校验文件
    fileNum = "001"
    verfFile = createVerificationFile(taskConfig, dataDate, revisionTimes)
    
    // 3. 分割文件
    lineStart = 1
    while (lineStart <= totalLines):
        lineEnd = min(lineStart + 2000000 - 1, totalLines)
        
        // 生成目标文件名
        outputFile = generateOutputFileName(taskConfig, dataDate, revisionTimes, fileNum)
        
        // 提取指定行范围并处理
        extractAndProcessLines(sourceFile, outputFile, lineStart, lineEnd, taskConfig)
        
        // 记录文件信息到校验文件
        appendToVerificationFile(verfFile, outputFile, dataDate)
        
        // 更新行号和文件序号
        lineStart = lineEnd + 1
        fileNum = incrementFileNumber(fileNum)
    endwhile
END FUNCTION

// --- 数据校验子流程 ---
FUNCTION executeValidationForInterface(taskConfig, dataDate):
    LOG "开始校验接口: " + taskConfig.interfaceName
    
    try:
        // 1. 获取原始数据文件路径
        originalFiles = getOriginalDataFiles(taskConfig, dataDate)
        
        // 2. 初始化校验统计
        validationStats = initializeValidationStats()
        errorRecords = []
        
        // 3. 逐文件进行流式校验
        for (file in originalFiles):
            streamValidateFile(file, taskConfig, validationStats, errorRecords)
        endfor
        
        // 4. 生成校验报告
        generateValidationReport(taskConfig, dataDate, validationStats)
        
        // 5. 保存错误记录到告警表
        if (errorRecords.size() > 0):
            saveErrorRecordsToAlerts(taskConfig, errorRecords)
        endif
        
        // 6. 更新历史统计信息
        updateMetricsHistory(taskConfig, dataDate, validationStats)
        
        LOG "接口 " + taskConfig.interfaceName + " 校验完成"
    catch (error):
        LOG_ERROR "接口 " + taskConfig.interfaceName + " 校验失败: " + error.message
        storeValidationFailureAlert(taskConfig.interfaceName, error.message)
    endtry
END FUNCTION

// --- 文件流式校验子流程 ---
FUNCTION streamValidateFile(filePath, taskConfig, stats, errorRecords):
    lineNumber = 0
    reader = openFileReader(filePath)
    
    while (line = reader.readLine()):
        lineNumber++
        
        // 1. 解析数据行
        fields = parseDataLine(line, taskConfig.delimiter)
        
        // 2. 执行字段级校验
        validationErrors = validateFields(fields, taskConfig.validationRules)
        
        // 3. 记录统计信息
        stats.totalRecords++
        if (validationErrors.isEmpty()):
            stats.compliantRecords++
        else:
            stats.nonCompliantRecords++
            
            // 4. 保存错误记录详情
            errorRecord = createErrorRecord(
                filePath, lineNumber, line, fields, validationErrors
            )
            errorRecords.add(errorRecord)
            
            // 5. 如果错误记录过多，批量保存并清空
            if (errorRecords.size() >= 1000):
                saveErrorRecordsBatch(taskConfig, errorRecords)
                errorRecords.clear()
            endif
        endif
    endwhile
    
    reader.close()
END FUNCTION
```

## 3. 模块功能设计

### 3.1. 数据导出模块

**职责**：替代所有.sh脚本，负责执行UNLOAD TO命令将数据从数据库导出为文件。

**实现要点**：
- **配置驱动**：每个接口的导出逻辑被抽象为一个配置项。
- **SQL模板化**：使用模板字符串来动态替换SQL中的日期等变量。
- **版本控制**：通过bms_vgop_revtimes表管理文件修订版本。
- **执行UNLOAD**：通过JDBC执行包含UNLOAD TO子句的完整SQL语句。
- **数据库兼容性**：系统需同时支持Informix（生产环境）和GBase（测试环境），两者的UNLOAD语法基本相同。
- **文件分割**：自动将大文件分割为多个200万行的小文件。
- **特殊处理**：
  - 添加行号（使用管道符分隔）
  - 去除反斜杠和空格
  - 将分隔符转换为\200（八进制）
  - 添加回车符\r到每行末尾
- **校验文件生成**：生成.verf文件记录所有数据文件信息。
- **并行处理**：使用Java线程池并行执行多个接口的导出任务。

### 3.2. 数据上传模块

**职责**：负责将内网主机经过校验合格的最终数据文件，通过DMZ主机（前置机）安全地上传到下游业务系统。该模块解耦了内网数据生成与外网数据传输，增强了系统安全性。只有通过数据校验的文件才会被上传，确保数据质量。

**部署架构**：本模块采用两阶段部署模式：
- **内网主机**：部署数据上传服务的客户端部分，负责将文件推送至DMZ。
- **DMZ主机**：部署数据上传服务的主体部分，负责接收文件并转发至最终的下游系统。

**核心处理流程**：
该流程涉及内网主机、DMZ主机和下游系统三方，通过SFTP和HTTP API协同工作，确保文件传输的可靠性和可追溯性。

```mermaid
graph TD
    subgraph Internal["内网主机"]
        A["生成并完成数据校验"] --> B["SFTP批量上传文件至DMZ"]
        B --> C["组装本次上传的文件清单"]
        C --> D["发起HTTP请求通知DMZ"]
    end

    subgraph DMZ["DMZ主机"]
        E["API接收文件清单"] --> F["在指定目录查找文件"]
        F --> G{"文件是否存在?"}
        G -->|是| H["SFTP上传至下游系统"]
        G -->|否| I["记录文件丢失异常"]
        H --> J["记录每个文件的上传结果"]
        I --> J
        J --> K["组装HTTP响应"]
    end
    
    subgraph Downstream["下游系统"]
        L["接收最终数据文件"]
    end
    
    D --> E
    K --> M["接收DMZ的响应"]
    H --> L
    
    M --> N{"响应是否成功?"}
    N -->|是| O["流程正常结束"]
    N -->|否| P["记录传输失败告警"]
    P --> O
```

**流程详述**：
1.  **内网主机 - 数据导出**：系统在内网主机完成所有数据导出任务，生成原始数据文件。
2.  **内网主机 - 数据校验**：对导出的原始数据进行质量校验，包括字段格式检查、数据量波动检测等，只有校验通过的数据才进入下一步。
3.  **内网主机 - SFTP至DMZ**：内网主机通过SFTP，将校验通过的`.dat`和`.verf`文件批量上传到DMZ主机上的一个预定义中转目录。
4.  **内网主机 - 准备通知**：所有文件上传完成后，内网主机整理本次任务生成的所有文件的名称，形成一个文件清单。
5.  **内网主机 - 发起通知**：内网主机向DMZ主机上的数据上传模块发起一个HTTP POST请求，请求体中包含该文件清单。
6.  **DMZ主机 - 接收通知**：DMZ主机上的Web服务接收到HTTP请求，并解析出文件清单。
7.  **DMZ主机 - 定位文件**：根据清单中的文件名，在预定义的中转目录中查找并核对文件。
8.  **DMZ主机 - 上传至下游**：DMZ主机通过SFTP将文件逐个上传到最终的下游系统。
9.  **DMZ主机 - 记录结果**：详细记录每个文件的上传状态（成功/失败）及失败原因。
10. **DMZ主机 - 准备响应**：在所有文件处理完毕后，将详细的执行结果组装成一个JSON格式的HTTP响应。
11. **内网主机 - 接收响应**：内网主机阻塞等待并接收DMZ返回的HTTP响应。
12. **内网主机 - 记录告警**：内网主机解析响应。如果响应中包含任何上传失败的记录，则将这些失败信息记录到系统的告警表中，以便后续跟踪处理。

**接口设计 (API)**：
- **路径**: `POST /api/v1/upload-tasks`
- **请求体 (Request Body)**:
  ```json
  {
    "taskId": "daily-20240410",
    "files": [
      "a_10000_20240409_VGOP1-R2.10-24201_00_001.dat",
      "a_10000_20240409_VGOP1-R2.10-24201_00.verf"
    ]
  }
  ```
- **响应体 (Response Body)**:
  ```json
  {
    "taskId": "daily-20240410",
    "overallStatus": "PARTIAL_SUCCESS",
    "details": [
      {
        "file": "a_10000_20240409_VGOP1-R2.10-24201_00_001.dat",
        "status": "SUCCESS"
      },
      {
        "file": "a_10000_20240409_VGOP1-R2.10-24201_00.verf",
        "status": "FAILED",
        "error": "Failed to connect to downstream SFTP server: Connection timed out"
      }
    ]
  }
  ```

**实现要点**：
- **内网模块**：
  - 需要健壮的SFTP客户端，支持批量上传和错误处理。
  - 需要一个HTTP客户端来调用DMZ的API。
  - 需要与告警模块集成，能将远程失败信息写入告警库。
- **DMZ模块**：
  - 需要一个轻量级的Web服务器（如内嵌Tomcat的Spring Boot应用）来提供REST API。
  - 需要一个SFTP客户端将文件推送到下游系统。
  - 需要可靠的日志记录机制，用于审计和问题排查。
  - 接口需要考虑安全性，如来源IP白名单、token验证等。

### 3.3. 数据读取模块

**职责**：从本地文件系统流式读取在"数据导出阶段"生成的原始数据文件。

**实现要点**：
- **输入源**：本地文件系统。
- **文件流处理**：使用java.io.BufferedReader和Files.lines()逐行读取文件内容。
- **解析**：每读取一行文本，按分隔符分割，映射成一个DataRecord对象。

### 3.4. 数据校验模块

**职责**：对导出的数据进行质量检查，生成校验报告。

**实现要点**：
- **原始文件读取**：从数据导出阶段生成的.unl文件读取数据。
- **流式处理**：采用流式读取方式，避免大文件内存溢出。
- **字段格式校验**：检查手机号、IMSI、IMEI等字段格式。
- **特殊字段处理**：
  - 24205接口的拨出号码、接听号码字段不做格式限制
  - 24206接口的对端号码字段不做格式限制
- **数据量波动检测**：与历史数据对比，检测异常波动。
- **合规率统计**：计算各类数据的合规比例。
- **Excel报告生成**：生成《不符合字段定义的数据记录.年月日.xlsx》文件。
- **错误数据记录**：保存完整的错误行信息，包括：
  - 文件名和行号
  - 原始数据内容
  - 各字段值
  - 具体的校验错误信息
- **告警生成**：将校验问题记录到告警表。
- **批量处理**：错误记录达到阈值时批量保存，提高性能。

### 3.5. 调度管理模块

**职责**：管理日数据和月数据的定时调度。

**实现要点**：
- **日数据调度**：每日凌晨0点执行，处理前一天数据。
- **月数据调度**：每月1日凌晨5点执行，处理上月整月数据。
- **调度监控**：记录调度执行状态和耗时。
- **上传时间监控**：确保各接口在规定时间内完成上传（日数据8点前，月数据每月1日8点前）。

### 3.6. 告警记录模块

**职责**：负责生成和记录各类告警信息。

**实现要点**：
- **告警存储**：将告警信息记录到数据库表中。
- **告警类型**：
  - 字段校验告警（关联Excel报告路径）
  - 不合规数据量占比告警
  - 数据量环比告警
  - 上传超时告警
  - 校验文件内容告警
- **告警详情**：包含完整的错误信息、统计数据和时间戳。
- **查询接口**：提供REST API供运维脚本查询告警信息。



## 4. 配置文件 (application.yml) 结构

配置文件是系统的核心，用于管理所有接口的导出、传输和校验任务。

```yaml
# 数据库连接信息
datasource:
  # 生产环境配置
  production:
    url: jdbc:informix-sqli://your-db-host:port/bms:INFORMIXSERVER=your-server
    username: your-db-username
    password: your-db-password
    driver-class-name: com.informix.jdbc.IfxDriver
  
  # 测试环境配置
  test:
    url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
    username: ismp
    password: 1qaz@WSX
    driver-class-name: com.gbase.jdbc.Driver

# 应用核心配置
app:
  # 定时任务触发时间
  schedules:
    daily-cron: "0 0 0 * * ?"    # 日数据：每日凌晨0点
    monthly-cron: "0 0 5 1 * ?"  # 月数据：每月1日凌晨5点

  # 根目录配置
  base-path:
    # 数据导出根目录 (数据库服务器可访问的路径)
    export-root: "/path/to/VGOPdata/datafile/"
    # 日志目录
    log-root: "/path/to/log/"
  
  # SFTP配置
  sftp:
    host: "************"
    port: 22
    username: "bms"
    password: ""
    remote-base-path: "/home/<USER>/executor/log/image/VGOPdata/datafile/"
    connection-timeout: 30000
    retry-times: 3
    
  # 文件处理配置
  file-processing:
    max-rows-per-file: 2000000  # 每个文件最大行数
    default-delimiter: "|"       # 默认字段分隔符
    output-delimiter: "\200"     # 输出文件分隔符（八进制）
    line-ending: "\r"            # 行结束符（回车）
    add-line-number: true        # 是否添加行号
    line-number-delimiter: "|"   # 行号与数据之间的分隔符
    
  # 接口任务配置列表
  tasks:
    # 日数据接口配置
    daily:
      - interface-id: "24101"
        interface-name: "VGOP1-R2.11-24101"
        enabled: true
        export:
          sql-template: |
            SELECT ... FROM table 
            WHERE time >= '{startTime}' AND time < '{endTime}'
          temp-file-name-template: "a_10000_{dataDate}_VGOP1-R2.11-24101.unl"
          output-file-name-template: "a_10000_{dataDate}_VGOP1-R2.11-24101_{revTimes}_{fileNum}.dat"
          verf-file-name-template: "a_10000_{dataDate}_VGOP1-R2.11-24101_{revTimes}.verf"
          
      - interface-id: "24201"
        interface-name: "VGOP1-R2.10-24201"
        enabled: true
        export:
          sql-template: |
            SELECT mum.phonenumber as phonenumber,
                   mum.phonestate as phonestate,
                   mum.phoneimsi as phoneimsi,
                   mum.phoneimei as phoneimei,
                   mum.locationid as locationid,
                   substr(bp.bossid, 1, 3) as provinceid,
                   mum.openingtime as openingtime,
                   mum.Optime as Optime,
                   mum.sex as sex
            FROM mcn_user_major mum
            LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid
            WHERE mum.openingtime >= '{startTime}'
              AND mum.openingtime < '{endTime}'
              AND mum.phonestate IN ('0','1')
          temp-file-name-template: "a_10000_{dataDate}_VGOP1-R2.10-24201.unl"
          output-file-name-template: "a_10000_{dataDate}_VGOP1-R2.10-24201_{revTimes}_{fileNum}.dat"
        validation:
          non-compliant-ratio-threshold: 0.1
          volume-fluctuation-threshold: 0.1
          field-rules:
            - field-index: 0
              field-name: "phonenumber"
              rules:
                - type: "regex"
                  pattern: "^1[3-9]\\d{9}$"
                  error-message: "手机号格式不正确"
                - type: "length"
                  value: 11
                  error-message: "手机号长度必须为11位"
            - field-index: 2
              field-name: "phoneimsi"
              rules:
                - type: "length"
                  value: 15
                  error-message: "IMSI长度必须为15位"
                - type: "numeric"
                  error-message: "IMSI必须为数字"
            - field-index: 3
              field-name: "phoneimei"
              rules:
                - type: "length-range"
                  min: 14
                  max: 16
                  error-message: "IMEI长度必须为14-16位"
          
      - interface-id: "24205"
        interface-name: "VGOP1-R2.10-24205"
        enabled: true
        export:
          sql-template: |
            SELECT callType, callingPartyNumber, calledPartyNumber, mcnnumber, 
                   CallBeginTime, CallEndTime, CallDuration 
            FROM Mcn_contralog 
            WHERE CallBeginTime >= '{startTime}' AND CallBeginTime < '{endTime}' 
              AND Cause != '80 81' AND reason != '1' 
          temp-file-name-template: "a_10000_{dataDate}_VGOP1-R2.10-24205.unl"
          output-file-name-template: "a_10000_{dataDate}_VGOP1-R2.10-24205_{revTimes}_{fileNum}.dat"
        validation:
          non-compliant-ratio-threshold: 0.1
          volume-fluctuation-threshold: 5.0  # 500%
          special-fields:  # 特殊字段不做格式校验
            - "callingPartyNumber"
            - "calledPartyNumber"
            
      - interface-id: "24206"
        interface-name: "VGOP1-R2.10-24206"
        enabled: true
        validation:
          volume-fluctuation-threshold: 10.0  # 1000%
          special-fields:
            - "oppositeNumber"  # 对端号码不做格式限制
            
      - interface-id: "24207"
        interface-name: "VGOP1-R2.10-24207"
        enabled: true
        validation:
          expect-zero-records: true  # 期望数据量为0
          
      # 各接口环比阈值配置（根据原始需求）
      # 24101: 10%
      # 24201日增量: 200%
      # 24202日增量: 100%
      # 24203: 100%
      # 24204: 100%
      # 24205: 500%
      # 24206: 1000%
      # 24207: 数据量必须为0
      # 24301: 10%
      # 24302: 50%
      # 24303: 50%
      # 24304: 100%
      
    # 月数据接口配置
    monthly:
      - interface-id: "24201-month"
        interface-name: "VGOP1-R2.10-24201month"
        enabled: true
        export:
          sql-template: |
            SELECT ... FROM table 
            WHERE time >= '{monthStart}' AND time < '{monthEnd}'
          temp-file-name-template: "a_10000_{yearMonth}_VGOP1-R2.10-24201month.unl"
          output-file-name-template: "a_10000_{yearMonth}_VGOP1-R2.10-24201month_{revTimes}_{fileNum}.dat"
          
  # 告警配置
  alert:
    # 告警存储配置
    storage:
      enable-database: true    # 启用数据库存储
      enable-file: false       # 是否同时保存到文件
      alert-file-path: "/path/to/alerts/"  # 告警文件保存路径
      
    # 上传时间监控（小时）
    upload-deadline:
      daily: 8    # 每日8点前
      monthly: 8  # 每月1日8点前
      
    # 告警阈值配置
    thresholds:
      max-alerts-per-batch: 1000  # 批量保存的最大告警数
```

## 5. 数据库表结构设计

本方案涉及到的所有必需的数据库表如下。

### 5.1. 版本控制表 (bms_vgop_revtimes) - 已存在

**用途**：记录每个文件的修订版本，支持数据重新生成。

**表结构详情**：

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键/索引 | 默认值 | 字段说明 |
|--------|----------|------|----------|-----------|--------|----------|
| datatime | VARCHAR | 14 | YES | PK |  | 数据日期，格式：YYYYMMDDHHMMSS |
| times | INTEGER | 4 | YES |  |  | 重传次数，从0开始递增 |
| tmpfilename | VARCHAR | 50 | YES | PK |  | 临时文件名（含接口标识） |
| optime | VARCHAR | 14 | YES |  |  | 操作时间，格式：YYYYMMDDHHMMSS |

**主键**：(datatime, tmpfilename)
**当前数据量**：33,985条记录

### 5.2. 告警信息表 (vgop_validation_alerts)

**用途**：存储所有由校验任务生成的告警事件。

**表结构详情**：

| 字段名 | 建议数据类型 | 是否为空 | 主键/索引 | 默认值 | 字段说明 |
|--------|--------------|----------|-----------|--------|----------|
| alert_id | BIGINT | NOT NULL | PK (自增) |  | 告警的唯一数字标识符。 |
| alert_time | DATETIME | NOT NULL | INDEX | CURRENT_TIMESTAMP | 告警事件发生的精确时间。 |
| interface_name | VARCHAR(100) | NOT NULL | INDEX |  | 产生告警的接口/任务标识。 |
| alert_type | VARCHAR(50) | NOT NULL |  |  | 告警类型：EXPORT_FAILED, TRANSFER_FAILED, NON_COMPLIANT_RATIO_EXCEEDED, VOLUME_FLUCTUATION_EXCEEDED, UPLOAD_TIMEOUT等。 |
| alert_level | VARCHAR(20) | NOT NULL |  | 'WARNING' | 告警的严重级别：INFO, WARNING, ERROR, CRITICAL。 |
| alert_message | VARCHAR(1024) | NOT NULL |  |  | 对告警事件的格式化、人类可读的摘要描述。 |
| file_name | VARCHAR(200) | NULL |  |  | 产生错误的文件名。 |
| line_number | BIGINT | NULL |  |  | 错误数据所在的行号。 |
| error_data | TEXT | NULL |  |  | 原始错误数据行的完整内容。 |
| field_errors | JSON | NULL |  |  | JSON格式的字段级错误详情，如：{"phone": "格式错误", "imsi": "长度不足"}。 |
| metric_details | TEXT 或 JSON | NULL |  |  | 存储JSON格式的详细上下文指标。 |
| excel_report_path | VARCHAR(500) | NULL |  |  | 生成的Excel报告文件路径。 |
| status | VARCHAR(20) | NOT NULL | INDEX | 'NEW' | 告警的处理状态：NEW, ACKNOWLEDGED, RESOLVED。 |
| handled_by | VARCHAR(100) | NULL |  |  | 处理人员（运维脚本或人工处理时更新）。 |
| handled_time | DATETIME | NULL |  |  | 处理时间。 |

### 5.3. 历史统计表 (vgop_metrics_history)

**用途**：为每个接口独立持久化每日的校验统计结果。

**表结构详情**：

| 字段名 | 建议数据类型 | 是否为空 | 主键/索引 | 默认值 | 字段说明 |
|--------|--------------|----------|-----------|--------|----------|
| history_id | BIGINT | NOT NULL | PK (自增) |  | 记录的唯一数字标识符。 |
| processing_date | DATE | NOT NULL |  |  | 统计数据对应的业务日期。 |
| interface_name | VARCHAR(100) | NOT NULL |  |  | 统计数据所属的接口标识。 |
| total_records | BIGINT | NOT NULL |  | 0 | 当日总记录数。 |
| compliant_records | BIGINT | NOT NULL |  | 0 | 当日合规记录数。 |
| non_compliant_records | BIGINT | NOT NULL |  | 0 | 当日不合规记录数。 |
| export_file_count | INT | NOT NULL |  | 0 | 生成的文件数量。 |
| export_status | VARCHAR(20) | NOT NULL |  |  | 导出状态：SUCCESS, FAILED, PARTIAL。 |
| transfer_status | VARCHAR(20) | NULL |  |  | 传输状态：SUCCESS, FAILED, PENDING。 |
| created_at | TIMESTAMP | NOT NULL |  | CURRENT_TIMESTAMP | 该条统计记录在数据库中的创建时间。 |

**索引建议**：在 (interface_name, processing_date) 上建立一个联合唯一索引。

### 5.5. 基础数据字典表 - 已存在

以下表为系统运行所需的基础数据字典表，已在现网环境中存在：

#### 5.5.1. 省份机构对应表 (bossprovince)

**用途**：BOSS机构代码与省份ID对应关系，用于24201接口的省份信息关联。

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键/索引 | 字段说明 |
|--------|----------|------|----------|-----------|----------|
| bossid | CHAR | 4 | YES | PK | 4位BOSS机构编码（前3位为省份编码） |
| provinceid | SMALLINT |  | YES |  | 省份ID |

**当前数据量**：31条记录

#### 5.5.2. 业务资费类型表 (vgop_servtype)

**用途**：VGOP资费类型表，定义各种业务代码和对应的业务名称。

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键/索引 | 字段说明 |
|--------|----------|------|----------|-----------|----------|
| ServID | VARCHAR | 6 | YES |  | 业务代码（如：201000免费1月，100000包月等） |
| ServName | VARCHAR | 50 | YES |  | 业务名称 |

**当前数据量**：22条记录

#### 5.5.3. 用户开户渠道表 (vgop_channel)

**用途**：VGOP用户开户媒介表，定义各种开户渠道代码。

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键/索引 | 字段说明 |
|--------|----------|------|----------|-----------|----------|
| channelcode | VARCHAR | 2 | YES |  | 开户渠道代码（0-9，分别对应不同渠道） |
| channelname | VARCHAR | 50 | YES |  | 渠道名称 |

**渠道代码说明**：
- 0: 客服网站
- 1: 短信
- 2: 手机网站
- 3: 用户网站（PC）
- 4: 客户端
- 5: 一致性稽核
- 6: BOSS正向（订购关系同步）
- 7: 割接
- 8: 批量开户
- 9: 第三方平台

**当前数据量**：10条记录

#### 5.5.4. 副号停机标识表 (vgop_shutdown)

**用途**：VGOP副号停机标识表，定义副号码的各种状态。

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键/索引 | 字段说明 |
|--------|----------|------|----------|-----------|----------|
| shutdown | VARCHAR | 1 | YES |  | 副号码停机标识（0-8，对应不同状态） |
| shutdownname | VARCHAR | 50 | YES |  | 标识名称 |

**停机标识说明**：
- 0: 正常
- 1: 停机
- 2: 销号（空号）
- 4: 实体副号码销号异常
- 5: 使用不恰当，限制使用
- 6: 续订失败，限制使用
- 7: app、网站、短信渠道下用户不可见
- 8: 省boss删除数据，用户补录后重新发送开户消息

**当前数据量**：8条记录

#### 5.5.5. 用户类型表 (vgop_mcntype)

**用途**：VGOP用户类型表，定义用户类型分类。

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键/索引 | 字段说明 |
|--------|----------|------|----------|-----------|----------|
| mcntype | VARCHAR | 1 | YES |  | 用户类型（0: 普通个人用户） |
| mcntypename | VARCHAR | 50 | YES |  | 类型名称 |

**当前数据量**：1条记录

### 5.4. 任务执行日志表 (vgop_task_execution_log) - 新增

**用途**：记录每个任务的执行情况，用于监控和故障排查。

**表结构详情**：

| 字段名 | 建议数据类型 | 是否为空 | 主键/索引 | 默认值 | 字段说明 |
|--------|--------------|----------|-----------|--------|----------|
| log_id | BIGINT | NOT NULL | PK (自增) |  | 日志记录的唯一标识符。 |
| task_id | VARCHAR(50) | NOT NULL | INDEX |  | 任务标识（接口ID）。 |
| task_name | VARCHAR(100) | NOT NULL |  |  | 任务名称（接口名称）。 |
| task_type | VARCHAR(20) | NOT NULL |  |  | 任务类型：EXPORT, TRANSFER, VALIDATION。 |
| start_time | DATETIME | NOT NULL |  |  | 任务开始时间。 |
| end_time | DATETIME | NULL |  |  | 任务结束时间。 |
| status | VARCHAR(20) | NOT NULL |  |  | 执行状态：RUNNING, SUCCESS, FAILED。 |
| error_message | TEXT | NULL |  |  | 错误信息（如果失败）。 |
| processed_records | BIGINT | NULL |  |  | 处理的记录数。 |
| generated_files | INT | NULL |  |  | 生成的文件数。 |



## 6. 核心类设计

### 6.1. 数据导出相关类

```text
// 数据导出服务
DataExportService:
    - exportInterfaceData(config, dataDate)      // 执行单个接口的数据导出
    - getAndUpdateRevisionTimes(date, fileName)  // 版本控制
    - executeUnloadCommand(sql, path, delimiter) // 执行UNLOAD命令
    - splitAndProcessFiles(sourceFile, config)   // 文件分割处理
    - generateVerificationFile(dataFiles, path)  // 生成校验文件

// 文件处理工具类
FileProcessingUtil:
    - processDataLine(rawLine, lineNumber):      // 处理单行数据
        1. 添加行号
        2. 去除行尾分隔符
        3. 删除反斜杠和空格
        4. 将|替换为\200
        5. 添加回车符\r
    - writeBinaryLine(writer, processedLine)     // 写入二进制格式
```

### 6.2. 文件传输相关类

```text
// SFTP传输服务
SftpTransferService:
    - uploadFilesToDMZ(localPath, filePatterns)  // 上传文件到DMZ
    - connectSFTP()                              // 建立SFTP连接
    - batchUpload(channel, files, remotePath)    // 批量上传
    - handleTransferError(file, error)           // 错误处理

// SFTP连接池
SftpConnectionPool:
    - borrowConnection()                          // 获取连接
    - returnConnection(channel)                   // 归还连接
```

### 6.3. 调度相关类

```text
// 调度任务
VgopScheduledTasks:
    - executeDailyTasks()    // 每日凌晨0点执行
    - executeMonthlyTasks()  // 每月1日凌晨5点执行

// 任务编排器
TaskOrchestrator:
    - executeDailyWorkflow():
        1. 执行数据导出阶段
        2. 执行文件传输阶段
        3. 执行数据校验阶段
    - executeMonthlyWorkflow()   // 类似日流程，处理月数据
```

### 6.4. 数据校验相关类

```text
// 数据校验服务
DataValidationService:
    - validateInterfaceData(config, dataDate)         // 执行接口数据校验
    - streamValidateFile(filePath, config, stats)     // 流式校验文件
    - validateFields(fields, rules, specialFields)    // 字段级校验
    - checkVolumeFluctuation(current, previous)       // 数据量波动检测
    - checkExpectZeroRecords(config, recordCount)     // 检查零记录期望
    - generateExcelReport(config, errorRecords)       // 生成Excel报告

// 错误记录实体
ErrorRecord:
    - fileName: String           // 文件名
    - lineNumber: Long          // 行号
    - originalData: String      // 原始数据
    - fieldValues: String[]     // 字段值数组
    - errors: List<FieldError>  // 错误列表
    - createTime: DateTime      // 创建时间
```

### 6.5. 告警记录相关类

```text
// 告警记录服务
AlertRecordService:
    - recordFieldValidationAlert(config, stats, excelPath)     // 记录字段校验告警
    - recordComplianceRatioAlert(config, ratio)                // 记录合规率告警
    - recordVolumeFluctuationAlert(config, fluctuation)        // 记录环比告警
    - recordUploadTimeoutAlert(config, deadline)               // 记录超时告警
    - saveAlertsBatch(alerts)                                  // 批量保存
    - writeAlertsToFile(alerts, date)                         // 可选文件记录

// 告警查询API
AlertQueryController:
    GET  /api/alerts/date/{date}              // 按日期查询
    GET  /api/alerts/interface/{name}         // 按接口查询
    GET  /api/alerts/pending                  // 查询待处理
    POST /api/alerts/{id}/status              // 更新状态
```

### 6.6. 报告和监控相关类

```text
// Excel报告生成服务
ExcelReportService:
    - generateErrorRecordsExcel(errorRecords, date): // 生成不合规数据Excel
        返回: "不符合字段定义的数据记录.YYYYMMDD.xlsx"

// 校验文件内容校验服务
VerificationFileValidationService:
    - validateVerificationFile(verfPath, config):    // 校验.verf文件
        1. 检查文件格式（5个字段）
        2. 验证数据文件是否存在
        3. 验证文件大小是否匹配

// 上传时间监控服务
UploadTimeMonitorService:
    - checkUploadStatus():                           // 每小时检查
        IF 当前时间 == 8:00-8:05:
            检查日数据上传情况
        IF 当前日期 == 1日 AND 时间 == 8:00-8:05:
            检查月数据上传情况
```



## 7. 性能指标与优化

### 7.1. 现网数据量分析

基于当前生产环境的数据量统计，系统需要处理的核心表数据规模如下：

| 表名 | 数据量 | 说明 | 性能影响 |
|------|--------|------|----------|
| mcn_oplog | 811,429,763 | 操作日志表（8亿+） | **高影响** - 主要查询表，需重点优化 |
| Mcn_contralog | 284,821,902 | 通话记录表（2.8亿+） | **高影响** - 24205接口数据源 |
| mcn_apploginlog | 156,292,192 | 应用登录日志（1.5亿+） | **中影响** - 需考虑查询效率 |
| mcn_smslog | 100,297,862 | 短信日志（1亿+） | **中影响** - 需优化查询性能 |
| mcn_user_minor | 2,316,179 | 用户次要信息（230万+） | **低影响** - 数据量相对较小 |
| mcn_user_major | 1,933,657 | 用户主要信息（190万+） | **低影响** - 24201接口数据源 |
| mcn_sec_major | 1,822,234 | 安全主要信息（180万+） | **低影响** - 数据量适中 |
| bms_vgop_banalyse | 942,162 | 业务分析表（94万+） | **低影响** - 数据量较小 |

### 7.2. 性能设计目标

基于数据量分析，制定以下性能目标：

#### 7.2.1. 数据导出性能目标
- **大表查询优化**：针对8亿+记录的mcn_oplog表，单次查询响应时间控制在30分钟内
- **并行处理能力**：支持最多8个接口并行导出，避免数据库连接池耗尽
- **内存使用控制**：单个导出任务内存使用不超过2GB
- **文件生成速度**：平均处理速度达到10万条/分钟

#### 7.2.2. 数据校验性能目标
- **流式处理能力**：支持处理单文件最大1000万条记录
- **校验速度**：平均校验速度达到50万条/分钟
- **内存占用**：校验过程内存使用不超过1GB
- **批量入库效率**：错误记录批量入库，每批1000条，入库时间控制在5秒内

#### 7.2.3. 整体性能目标
- **日数据处理时间**：11个日接口总处理时间控制在6小时内（0点-6点）
- **月数据处理时间**：4个月接口总处理时间控制在8小时内
- **系统可用性**：99.5%以上，支持7x24小时运行
- **并发连接数**：数据库连接池最大20个连接

### 7.3. 性能优化策略

#### 7.3.1. 数据库查询优化
```sql
-- 针对大表的查询优化示例（mcn_oplog表）
-- 1. 确保时间字段有索引
CREATE INDEX idx_mcn_oplog_time ON mcn_oplog(time);

-- 2. 使用分区查询，避免全表扫描
SELECT /*+ USE_INDEX(mcn_oplog, idx_mcn_oplog_time) */
       field1, field2, field3
FROM mcn_oplog
WHERE time >= '20240409000000'
  AND time < '20240410000000'
  AND other_conditions;

-- 3. 对于超大结果集，使用UNLOAD TO避免内存问题
UNLOAD TO '/path/to/file.unl' DELIMITER '|'
SELECT field1, field2, field3
FROM mcn_oplog
WHERE time >= '20240409000000'
  AND time < '20240410000000';
```

#### 7.3.2. 应用层优化
- **连接池配置**：
  ```yaml
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  ```

- **线程池配置**：
  ```yaml
  app:
    thread-pool:
      core-size: 4
      max-size: 8
      queue-capacity: 100
      keep-alive: 60
  ```

- **JVM参数优化**：
  ```bash
  -Xms4g -Xmx8g
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=200
  -XX:+HeapDumpOnOutOfMemoryError
  ```

#### 7.3.3. 文件处理优化
- **流式读写**：使用BufferedReader/BufferedWriter，缓冲区大小64KB
- **分批处理**：大文件按200万行分割，避免单文件过大
- **压缩传输**：SFTP传输时启用压缩，减少网络传输时间

### 7.4. 监控指标

#### 7.4.1. 系统监控指标
- **CPU使用率**：平均使用率<70%，峰值<90%
- **内存使用率**：平均使用率<80%，峰值<95%
- **磁盘I/O**：读写速度监控，确保不成为瓶颈
- **网络带宽**：SFTP传输带宽使用率<80%

#### 7.4.2. 业务监控指标
- **任务执行时间**：每个接口的导出、传输、校验时间
- **数据处理量**：每小时处理的记录数
- **错误率**：导出失败率、传输失败率、校验错误率
- **队列积压**：待处理任务队列长度

#### 7.4.3. 数据库监控指标
- **连接池使用率**：活跃连接数/最大连接数
- **查询响应时间**：平均查询时间、95%分位数查询时间
- **锁等待时间**：数据库锁等待情况
- **慢查询监控**：执行时间超过30秒的查询

## 8. 技术栈

- **框架**：Spring Boot 2.7.x
- **数据库**：
  - Informix JDBC Driver 4.50.x（生产环境）
  - GBase JDBC Driver（测试环境）
  - HikariCP 连接池
- **文件传输**：JSch 0.1.55（SFTP客户端）
- **文件处理**：Apache POI 5.2.x（Excel文件生成）
- **定时任务**：Spring Scheduler
- **并发处理**：Java ExecutorService
- **监控组件**：Micrometer + Prometheus（可选）
- **日志**：SLF4J + Logback
- **构建工具**：Maven 3.8.x

## 9. 部署架构

```text
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Informix DB    │◄────│  VGOP Service   │────►│   DMZ Server    │
│  (Source Data)  │     │  (Internal)     │SFTP │   (Frontend)    │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │
                               │
                               ▼
                        ┌─────────────────┐
                        │                 │
                        │  Alert Database │
                        │  (Monitoring)   │
                        │                 │
                        └─────────────────┘
```

## 10. 数据校验流程详述

### 10.1. 数据校验详细流程图

```mermaid
graph TD
    A[开始数据校验] --> B[获取需要校验的接口配置]
    B --> C[逐个接口执行校验]

    C --> D[读取原始.unl数据文件]
    D --> E[初始化校验统计信息<br/>总记录数、合规数、不合规数]
    E --> F[流式读取文件<br/>逐行处理避免内存溢出]

    F --> G[解析数据行<br/>按分隔符拆分字段]
    G --> H[执行字段级校验]

    H --> I{字段校验结果}
    I -->|全部合规| J[合规记录计数+1]
    I -->|存在错误| K[不合规记录计数+1<br/>记录详细错误信息]

    K --> L[保存错误记录：<br/>文件名、行号、原始数据<br/>字段值、错误原因]
    L --> M{错误记录是否达到1000条?}
    M -->|是| N[批量保存到告警表<br/>清空缓存]
    M -->|否| O[继续处理下一行]
    N --> O
    J --> O

    O --> P{文件是否处理完成?}
    P -->|否| F
    P -->|是| Q[计算合规率统计]

    Q --> R{合规率是否低于阈值?}
    R -->|是| S[生成合规率告警]
    R -->|否| T[检查数据量波动]
    S --> T

    T --> U{数据量波动是否超过阈值?}
    U -->|是| V[生成数据量波动告警]
    U -->|否| W[生成Excel校验报告]
    V --> W

    W --> X[更新历史统计表<br/>vgop_metrics_history]
    X --> Y{所有接口校验完成?}
    Y -->|否| C
    Y -->|是| Z[数据校验阶段结束]

    style H fill:#fff3e0
    style L fill:#ffebee
    style W fill:#e8f5e8
```

**说明**：这个流程图展示了数据校验阶段的完整逻辑。系统采用流式处理方式逐行校验数据，避免大文件导致的内存问题。黄色区域显示校验逻辑，红色区域显示错误处理，绿色区域显示报告生成。系统会进行多维度检查，包括字段格式、合规率和数据量波动。

### 10.2. 告警处理流程图

```mermaid
graph TD
    A[系统运行过程中] --> B{检测到异常情况}

    B -->|导出失败| C[导出失败告警]
    B -->|传输失败| D[传输失败告警]
    B -->|字段校验错误| E[字段校验告警]
    B -->|合规率过低| F[合规率告警]
    B -->|数据量波动异常| G[数据量波动告警]
    B -->|上传超时| H[上传超时告警]

    C --> I[构造告警信息]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I

    I --> J[设置告警级别<br/>INFO/WARNING/ERROR/CRITICAL]
    J --> K[记录详细上下文信息<br/>接口名、时间、错误详情]

    K --> L{是否为字段校验错误?}
    L -->|是| M[生成Excel报告<br/>《不符合字段定义的数据记录.日期.xlsx》]
    L -->|否| N[保存到告警表<br/>vgop_validation_alerts]

    M --> O[关联Excel报告路径]
    O --> N

    N --> P[更新告警状态为NEW]
    P --> Q[运维脚本定期查询]

    Q --> R{告警是否需要处理?}
    R -->|是| S[更新状态为ACKNOWLEDGED<br/>记录处理人和处理时间]
    R -->|否| T[保持NEW状态]

    S --> U[问题解决后<br/>更新状态为RESOLVED]
    T --> V[告警处理流程结束]
    U --> V

    style I fill:#fff3e0
    style M fill:#e8f5e8
    style N fill:#e1f5fe
```

**说明**：这个流程图展示了系统的完整告警处理机制。系统能够检测多种异常情况并生成相应告警，支持不同的告警级别和状态管理。黄色区域显示告警构造过程，绿色区域显示报告生成，蓝色区域显示数据库操作。





### 10.3. 错误记录示例

当数据校验发现错误时，系统会记录详细的错误信息：

```json
{
  "alert_type": "FIELD_VALIDATION_ERROR",
  "interface_name": "VGOP1-R2.10-24201",
  "file_name": "a_10000_20240115_VGOP1-R2.10-24201.unl",
  "line_number": 12345,
  "error_data": "1234567890|1|460001234567890|86012345678901234|101|010|20240115120000|20240115120100|1",
  "field_errors": {
    "phonenumber": {
      "value": "1234567890",
      "error": "手机号格式不正确，必须以13-19开头的11位数字"
    },
    "phoneimei": {
      "value": "86012345678901234",
      "error": "IMEI长度不正确，应为14-16位"
    }
  }
}
```

### 10.4. 校验规则类型

系统支持以下几种校验规则类型：

1. **格式校验**
   - 正则表达式匹配
   - 长度校验
   - 数值范围校验

2. **业务规则校验**
   - 手机号合法性
   - IMSI/IMEI校验
   - 时间格式和逻辑校验

3. **数据质量校验**
   - 空值检查
   - 重复数据检查
   - 数据一致性检查

4. **统计规则校验**
   - 数据量波动检测
   - 合规率阈值检测
   - 异常模式识别

## 11. 数据格式规范

### 11.1. 导出文件格式要求

为确保与现有系统的兼容性，新系统导出的数据文件必须严格遵循以下格式规范：

#### 11.1.1. 文件命名规范
所有数据文件必须使用`.dat`扩展名，文件命名格式如下：
- **日数据文件**：`a_10000_{日期}_VGOP1-R2.XX-XXXXX_{修订版本}_{文件序号}.dat`
  - 示例：`a_10000_20250409_VGOP1-R2.10-24201_00_001.dat`
- **月数据文件**：`a_10000_{年月}_VGOP1-R2.XX-XXXXXmonth_{修订版本}_{文件序号}.dat`
  - 示例：`a_10000_202503_VGOP1-R2.10-24201month_00_001.dat`
- **校验文件**：使用`.verf`扩展名
  - 示例：`a_10000_20250409_VGOP1-R2.10-24201_00.verf`

#### 11.1.2. 文件格式示例
```
11787642152104600751753334567531820250409065549
21501431800004600231431301707541820250409065608
31351063066904600006901548917551820250409065648
```

#### 11.1.3. 格式说明
- **字段分隔符**：使用\200（八进制，显示为）作为字段分隔符
- **行号**：每行第一个字段为递增的行号，从1开始
- **行结束符**：每行以\r（回车符）结束
- **字段顺序**：必须与SQL查询的字段顺序保持一致
- **空字段处理**：空字段保留，连续的分隔符表示空字段

#### 11.1.4. 字段映射（以24201接口为例）

基于数据字典和脚本分析，24201接口的字段映射如下：

| 位置 | 字段名 | 数据来源 | 说明 |
|------|--------|----------|------|
| 1 | 行号 | 自动生成 | 自动生成的递增序号（从1开始） |
| 2 | phonenumber | mcn_user_major.phonenumber | 手机号码（11位） |
| 3 | phonestate | mcn_user_major.phonestate | 手机状态（0:正常, 1:正常） |
| 4 | phoneimsi | mcn_user_major.phoneimsi | IMSI号码（15位） |
| 5 | phoneimei | mcn_user_major.phoneimei | IMEI号码（14-16位） |
| 6 | locationid | mcn_user_major.locationid | 位置ID |
| 7 | provinceid | substr(bossprovince.bossid, 1, 3) | 省份代码（3位BOSS编码） |
| 8 | openingtime | mcn_user_major.openingtime | 开通时间（YYYYMMDDHHMMSS） |
| 9 | optime | mcn_user_major.Optime | 操作时间（YYYYMMDDHHMMSS） |
| 10 | sex | mcn_user_major.sex | 性别 |

**关联表说明**：
- 主表：mcn_user_major（用户主要信息表，190万+记录）
- 关联表：bossprovince（省份机构对应表，31条记录）
- 关联条件：mum.provinceid = bp.provinceid
- 查询条件：按开通时间范围查询，且手机状态为0或1

### 11.2. 文件处理特殊逻辑

在生成最终文件时，系统需要执行以下特殊处理：

```bash
# Shell脚本中的处理逻辑
sed -n "${n1},${n2}p" ${UnloadFileName} | nl -s '|' | sed 's/|$//' | tr -d '\' | tr -d ' ' | tr '|' '\200' | awk '{print($0"\r")}'
```

对应的Java实现逻辑：
1. 提取指定行范围的数据
2. 添加行号（使用|作为临时分隔符）
3. 去除行尾的多余分隔符
4. 删除反斜杠字符
5. 删除空格字符
6. 将|分隔符替换为\200
7. 在每行末尾添加\r

### 11.3. 实现示例（伪代码）

```text
// 文件分割和处理的核心实现
FUNCTION splitAndProcessFiles(sourceFile, config, dataDate, revisionTimes):
    
    // 1. 获取文件总行数
    totalLines = countFileLines(sourceFile)
    IF totalLines == 0: totalLines = 1
    
    // 2. 初始化变量
    fileNum = 1
    lineNumber = 1
    currentFileLines = 0
    generatedFiles = []
    
    // 3. 逐行读取和处理
    FOR each line IN sourceFile:
        // 检查是否需要创建新文件
        IF currentFileLines >= MAX_ROWS_PER_FILE (200万):
            closeCurrentFile()
            fileNum++
            createNewFile(fileNum)
            currentFileLines = 0
        
        // 处理当前行
        processedLine = processDataLine(line, lineNumber):
            - 添加行号
            - 删除反斜杠和空格
            - 转换分隔符为\200
            - 添加回车符\r
        
        // 写入处理后的行
        writeToFile(processedLine)
        currentFileLines++
        lineNumber++
    
    // 4. 生成校验文件(.verf)
    FOR each generatedFile IN generatedFiles:
        verfContent = fileName + \200 + fileSize + \200 + date + \200 + lineCount + \200 + timestamp
        appendToVerfFile(verfContent)
    
    RETURN generatedFiles
```

## 12. 关键要求总结

为确保新系统与现有系统的完全兼容，必须严格遵循以下要求：

1. **文件扩展名**：所有数据文件必须使用`.dat`扩展名，校验文件使用`.verf`扩展名
2. **数据格式**：使用\200（八进制）作为字段分隔符，每行以\r结束
3. **行号处理**：每行第一个字段为递增的行号，从1开始
4. **文件分割**：每个文件最多200万行
5. **字符处理**：删除反斜杠和空格字符
6. **数据库兼容**：支持Informix（生产）和GBase（测试）两种数据库
7. **校验报告**：生成Excel格式的不合规数据记录文件
8. **告警记录**：将各类告警信息记录到数据库，供运维脚本监控
9. **环比阈值**：根据不同接口设置不同的环比阈值
10. **上传时限**：日数据8点前上传，月数据每月1日8点前上传
11. **特殊字段**：24205和24206的部分字段不做格式限制
12. **数据字典依赖**：系统依赖5个基础数据字典表（bossprovince、vgop_servtype、vgop_channel、vgop_shutdown、vgop_mcntype）
13. **版本控制机制**：使用bms_vgop_revtimes表管理文件修订版本，支持数据重新生成
14. **查询接口**：提供REST API供运维脚本查询和管理告警

这个方案完整地保留了原有Shell脚本的核心逻辑，同时提供了更好的可维护性、可监控性和扩展性。

## 13. 流程图总结

本文档通过以下四个核心流程图，完整展示了VGOP数据校验系统的业务逻辑和实现方案：

### 13.1. 流程图概览

1. **系统总体流程图**（第2.1节）
   - 展示了系统的三阶段处理架构：数据导出 → 文件传输 → 数据校验
   - 说明了双周期调度机制：日数据处理和月数据处理
   - 突出了系统的自动化特性和错误处理能力

2. **数据导出详细流程图**（第2.2节）
   - 详细说明了并行导出、版本控制、文件分割等核心功能
   - 展示了从数据库查询到文件生成的完整数据流
   - 强调了系统的容错机制和告警处理

3. **数据校验详细流程图**（第10.1节）
   - 展示了流式校验、多维度检查、批量处理等校验逻辑
   - 说明了错误记录的详细处理过程
   - 体现了系统的性能优化考虑（避免内存溢出）

4. **告警处理流程图**（第10.2节）
   - 说明了完整的告警生成、记录、查询和状态管理机制
   - 展示了多种告警类型的统一处理流程
   - 体现了系统的可监控性和运维友好性

### 13.2. 关键设计理念

通过这些流程图可以看出，VGOP数据校验系统的设计遵循以下关键理念：

- **全流程自动化**：从数据导出到校验报告生成的端到端自动化
- **高并发处理**：通过线程池实现多接口并行处理，提升系统效率
- **流式处理**：采用流式读取避免大文件内存问题，保证系统稳定性
- **完善的监控**：多维度告警机制，确保问题及时发现和处理
- **容错设计**：完善的错误处理和重试机制，保证系统稳定运行
- **可扩展性**：配置驱动的设计，便于新增接口和调整校验规则

### 13.3. 业务价值

这套流程图清晰地展示了系统如何解决现有Shell脚本架构的痛点：

1. **替代分散脚本**：将多个Shell脚本的功能统一到一个Java应用中
2. **提升处理效率**：通过并行处理大幅提升数据导出和校验效率
3. **增强监控能力**：提供详细的执行日志、告警信息和统计报告
4. **保证数据质量**：多维度校验确保数据的准确性和完整性
5. **提高系统可靠性**：完善的错误处理和重试机制保证服务稳定运行

通过这些流程图，技术人员可以快速理解系统架构，非技术人员也能清晰了解业务流程，为系统的开发、部署和运维提供了重要的参考依据。