package com.vgop.service.dao;

import com.vgop.service.entity.RevisionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 版本控制数据访问接口
 */
@Mapper
public interface RevisionMapper {
    
    /**
     * 根据数据时间和文件名查询版本信息
     */
    RevisionEntity findByDateAndFileName(@Param("dataTime") String dataTime, 
                                       @Param("tmpFileName") String tmpFileName);
    
    /**
     * 插入新的版本记录
     */
    int insert(RevisionEntity revisionEntity);
    
    /**
     * 更新版本次数
     */
    int updateTimes(@Param("dataTime") String dataTime, 
                   @Param("tmpFileName") String tmpFileName, 
                   @Param("times") Integer times);
} 