package com.vgop.service.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL模板处理服务
 * 负责处理SQL模板中的变量替换
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class SqlTemplateService {
    
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\{([^}]+)\\}");
    
    /**
     * 处理日数据SQL模板
     * 
     * @param sqlTemplate SQL模板
     * @param dataDate 数据日期（YYYYMMDD）
     * @return 处理后的SQL
     */
    public String processDailySqlTemplate(String sqlTemplate, String dataDate) {
        Map<String, String> variables = new HashMap<>();
        
        // 计算开始时间和结束时间
        String startTime = dataDate + "000000";
        String endTime = getNextDay(dataDate) + "000000";
        
        variables.put("dataDate", dataDate);
        variables.put("startTime", startTime);
        variables.put("endTime", endTime);
        
        return replaceVariables(sqlTemplate, variables);
    }
    
    /**
     * 处理月数据SQL模板
     * 
     * @param sqlTemplate SQL模板
     * @param yearMonth 年月（YYYYMM）
     * @return 处理后的SQL
     */
    public String processMonthlySqlTemplate(String sqlTemplate, String yearMonth) {
        Map<String, String> variables = new HashMap<>();
        
        // 解析年月
        String year = yearMonth.substring(0, 4);
        String month = yearMonth.substring(4, 6);
        
        // 计算月份的开始和结束时间
        String monthStart = yearMonth + "01000000";
        String monthEnd = getNextMonth(yearMonth) + "01000000";
        
        variables.put("yearMonth", yearMonth);
        variables.put("year", year);
        variables.put("month", month);
        variables.put("monthStart", monthStart);
        variables.put("monthEnd", monthEnd);
        
        return replaceVariables(sqlTemplate, variables);
    }
    
    /**
     * 处理文件名模板
     * 
     * @param fileNameTemplate 文件名模板
     * @param variables 变量映射
     * @return 处理后的文件名
     */
    public String processFileNameTemplate(String fileNameTemplate, Map<String, String> variables) {
        return replaceVariables(fileNameTemplate, variables);
    }
    
    /**
     * 替换模板中的变量
     * 
     * @param template 模板字符串
     * @param variables 变量映射
     * @return 替换后的字符串
     */
    private String replaceVariables(String template, Map<String, String> variables) {
        if (template == null || template.isEmpty()) {
            return template;
        }
        
        StringBuffer result = new StringBuffer();
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        
        while (matcher.find()) {
            String variable = matcher.group(1);
            String value = variables.get(variable);
            
            if (value != null) {
                matcher.appendReplacement(result, Matcher.quoteReplacement(value));
            } else {
                log.warn("未找到变量 {} 的值，保留原始占位符", variable);
                matcher.appendReplacement(result, Matcher.quoteReplacement(matcher.group(0)));
            }
        }
        
        matcher.appendTail(result);
        return result.toString();
    }
    
    /**
     * 获取下一天的日期
     * 
     * @param dateStr YYYYMMDD格式的日期
     * @return 下一天的日期（YYYYMMDD）
     */
    private String getNextDay(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate date = LocalDate.parse(dateStr, formatter);
        return date.plusDays(1).format(formatter);
    }
    
    /**
     * 获取下个月
     * 
     * @param yearMonth YYYYMM格式的年月
     * @return 下个月（YYYYMM）
     */
    private String getNextMonth(String yearMonth) {
        String year = yearMonth.substring(0, 4);
        String month = yearMonth.substring(4, 6);
        
        LocalDate date = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
        LocalDate nextMonth = date.plusMonths(1);
        
        return nextMonth.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }
    
    /**
     * 创建文件名变量映射
     * 
     * @param dataDate 数据日期（YYYYMMDD或YYYYMM）
     * @param revTimes 修订版本号（格式化后的，如"00"）
     * @param fileNum 文件序号（如"001"）
     * @return 变量映射
     */
    public Map<String, String> createFileNameVariables(String dataDate, String revTimes, String fileNum) {
        Map<String, String> variables = new HashMap<>();
        variables.put("dataDate", dataDate);
        variables.put("yearMonth", dataDate.length() == 6 ? dataDate : dataDate.substring(0, 6));
        variables.put("revTimes", revTimes);
        variables.put("fileNum", fileNum);
        return variables;
    }
} 