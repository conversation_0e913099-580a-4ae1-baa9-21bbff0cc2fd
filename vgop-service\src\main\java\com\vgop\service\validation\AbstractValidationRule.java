package com.vgop.service.validation;

import lombok.Getter;

/**
 * 抽象校验规则基类
 * 提供校验规则的基本实现
 */
@Getter
public abstract class AbstractValidationRule implements ValidationRule {
    
    /**
     * 规则ID
     */
    protected final String ruleId;
    
    /**
     * 规则名称
     */
    protected final String ruleName;
    
    /**
     * 规则描述
     */
    protected final String description;
    
    /**
     * 字段名
     */
    protected final String fieldName;
    
    /**
     * 严重程度
     */
    protected final ValidationSeverity severity;
    
    /**
     * 规则类型
     */
    protected final ValidationRuleType ruleType;
    
    /**
     * 构造函数
     * 
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @param description 规则描述
     * @param fieldName 字段名
     * @param severity 严重程度
     * @param ruleType 规则类型
     */
    protected AbstractValidationRule(String ruleId, String ruleName, String description,
                                    String fieldName, ValidationSeverity severity,
                                    ValidationRuleType ruleType) {
        this.ruleId = ruleId;
        this.ruleName = ruleName;
        this.description = description;
        this.fieldName = fieldName;
        this.severity = severity;
        this.ruleType = ruleType;
    }
    
    /**
     * 创建校验通过的结果
     * 
     * @return 校验结果
     */
    protected ValidationResult createValidResult() {
        return ValidationResult.valid(ruleId, ruleName);
    }
    
    /**
     * 创建校验失败的结果
     * 
     * @param value 字段值
     * @param message 错误消息
     * @param context 校验上下文
     * @return 校验结果
     */
    protected ValidationResult createInvalidResult(String value, String message, ValidationContext context) {
        return ValidationResult.invalid(
                ruleId,
                ruleName,
                fieldName,
                value,
                message,
                context.getRowNumber(),
                severity
        );
    }
} 