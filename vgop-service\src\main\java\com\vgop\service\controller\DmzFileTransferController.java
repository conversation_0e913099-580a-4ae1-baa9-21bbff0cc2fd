package com.vgop.service.controller;

import com.vgop.service.dto.ApiResponse;
import com.vgop.service.dto.FileTransferRequest;
import com.vgop.service.dto.FileTransferResponse;
import com.vgop.service.service.DmzFileTransferService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;

/**
 * DMZ文件中转控制器
 * 提供REST接口供内网调用，处理文件中转请求
 */
@Slf4j
@RestController
@RequestMapping("/api/dmz/file-transfer")
@RequiredArgsConstructor
public class DmzFileTransferController {
    
    private final DmzFileTransferService dmzFileTransferService;
    
    /**
     * 接收文件清单通知并执行中转
     * 
     * @param request 文件传输请求
     * @return 传输结果
     */
    @PostMapping("/notify")
    public ResponseEntity<ApiResponse<FileTransferResponse>> notifyFileTransfer(
            @Valid @RequestBody FileTransferRequest request) {
        
        log.info("收到文件中转通知 - 任务ID: {}, 接口ID: {}, 日期: {}, 文件数: {}", 
                 request.getTaskId(), request.getInterfaceId(), request.getDateId(),
                 request.getFileList() != null ? request.getFileList().size() : 0);
        
        try {
            // 执行文件中转处理
            FileTransferResponse response = dmzFileTransferService.processFileTransfer(request);
            
            // 根据处理结果确定API响应状态和HTTP状态码
            ApiResponse<FileTransferResponse> apiResponse;
            ResponseEntity<ApiResponse<FileTransferResponse>> responseEntity;
            
            switch (response.getStatus()) {
                case "SUCCESS":
                    apiResponse = ApiResponse.success("文件中转处理完成", response);
                    responseEntity = ResponseEntity.ok(apiResponse);
                    log.info("文件中转处理成功 - 任务ID: {}, 成功文件: {}, 失败文件: {}", 
                             request.getTaskId(), response.getSuccessFiles(), response.getFailedFiles());
                    break;
                    
                case "PARTIAL":
                    apiResponse = ApiResponse.success("文件中转部分成功", response);
                    responseEntity = ResponseEntity.ok(apiResponse);
                    log.warn("文件中转部分成功 - 任务ID: {}, 成功文件: {}, 失败文件: {}", 
                             request.getTaskId(), response.getSuccessFiles(), response.getFailedFiles());
                    break;
                    
                case "FAILED":
                    ApiResponse<FileTransferResponse> errorResponse = new ApiResponse<>(500, "文件中转处理失败");
                    errorResponse.setData(response);
                    responseEntity = ResponseEntity.status(500).body(errorResponse);
                    log.error("文件中转处理失败 - 任务ID: {}, 错误: {}", 
                              request.getTaskId(), response.getMessage());
                    break;
                    
                default:
                    ApiResponse<FileTransferResponse> unknownResponse = new ApiResponse<>(500, "文件中转返回未知状态: " + response.getStatus());
                    unknownResponse.setData(response);
                    responseEntity = ResponseEntity.status(500).body(unknownResponse);
                    log.error("文件中转返回未知状态 - 任务ID: {}, 状态: {}, 错误: {}", 
                              request.getTaskId(), response.getStatus(), response.getMessage());
                    break;
            }
            
            return responseEntity;
            
        } catch (Exception e) {
            log.error("文件中转处理异常 - 任务ID: {}", request.getTaskId(), e);
            
            // 创建错误响应
            FileTransferResponse errorResponse = FileTransferResponse.builder()
                    .taskId(request.getTaskId())
                    .status("FAILED")
                    .responseTime(LocalDateTime.now())
                    .totalFiles(request.getFileList() != null ? request.getFileList().size() : 0)
                    .successFiles(0)
                    .failedFiles(0)
                    .notFoundFiles(0)
                    .message("系统异常: " + e.getMessage())
                    .errorDetail(e.getMessage())
                    .build();
            
            // 创建API错误响应
            ApiResponse<FileTransferResponse> apiResponse = new ApiResponse<>(500, "文件中转处理异常");
            apiResponse.setData(errorResponse);
            
            return ResponseEntity.status(500).body(apiResponse);
        }
    }
    
    /**
     * 查询文件中转状态（可选功能）
     * 
     * @param taskId 任务ID
     * @return 状态信息
     */
    @GetMapping("/status/{taskId}")
    public ResponseEntity<ApiResponse<String>> getTransferStatus(@PathVariable String taskId) {
        log.info("查询文件中转状态 - 任务ID: {}", taskId);
        
        // 使用ApiResponse的静态方法创建响应
        ApiResponse<String> response = ApiResponse.success(
            "状态查询功能待实现",
            "任务ID: " + taskId + " 的状态查询功能待实现"
        );
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> healthCheck() {
        // 使用ApiResponse的静态方法创建响应
        ApiResponse<String> response = ApiResponse.success(
            "DMZ文件中转服务运行正常",
            "健康状态: 正常"
        );
        
        return ResponseEntity.ok(response);
    }
} 