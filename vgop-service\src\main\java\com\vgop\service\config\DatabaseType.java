package com.vgop.service.config;

/**
 * 数据库类型枚举
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public enum DatabaseType {
    /**
     * Informix数据库
     */
    INFORMIX("informix"),
    
    /**
     * GBase数据库
     */
    GBASE("gbase"),
    
    /**
     * H2数据库（开发测试用）
     */
    H2("h2"),
    
    /**
     * 未知类型
     */
    UNKNOWN("unknown");
    
    private final String name;
    
    DatabaseType(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据JDBC URL判断数据库类型
     */
    public static DatabaseType fromJdbcUrl(String jdbcUrl) {
        if (jdbcUrl == null) {
            return UNKNOWN;
        }
        
        String lowerUrl = jdbcUrl.toLowerCase();
        if (lowerUrl.contains("informix")) {
            return INFORMIX;
        } else if (lowerUrl.contains("gbasedbt") || lowerUrl.contains("gbase")) {
            return GBASE;
        } else if (lowerUrl.contains("h2")) {
            return H2;
        } else {
            return UNKNOWN;
        }
    }
    
    /**
     * 是否支持UNLOAD命令
     */
    public boolean supportsUnload() {
        return this == INFORMIX || this == GBASE;
    }
} 