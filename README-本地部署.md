# VGOP本地部署脚本使用指南

## 概述

`local-deploy.sh` 脚本专门为已有代码目录设计，适用于在服务器上直接使用现有代码进行构建和部署。

## 配置信息

当前脚本配置：
- **源代码目录**: `/home/<USER>/workspaces/vgop-vli`
- **Java环境**: `/home/<USER>/workspaces/jdk`
- **Git仓库**: `ssh://gitlab.e-byte.cn:19222/hdh/website_group/vgop-vli`
- **分支**: `master`
- **服务端口**: `8080`
- **运行环境**: `prod`

## 快速使用

### 1. 基本部署
```bash
# 完整部署：更新代码 → 构建 → 启动
./local-deploy.sh deploy

# 或者使用
./local-deploy.sh start
```

### 2. 不更新代码，直接构建部署
```bash
# 使用现有代码进行构建部署
./local-deploy.sh no-update
```

### 3. 服务管理
```bash
# 停止服务
./local-deploy.sh stop

# 重启服务
./local-deploy.sh restart

# 查看服务状态
./local-deploy.sh status

# 查看服务日志
./local-deploy.sh logs
```

### 4. 其他操作
```bash
# 仅更新代码和构建（不启动）
./local-deploy.sh build

# 仅更新代码
./local-deploy.sh update
```

## 重要特性

### 1. 完全跳过测试
- 脚本已配置为完全跳过所有测试逻辑
- 使用参数：`-DskipTests -Dmaven.test.skip=true -Dmaven.test.skip.exec=true`
- 大大缩短构建时间

### 2. 智能代码更新
- 自动保存当前修改（git stash）
- 拉取最新代码
- 支持选择性跳过更新

### 3. 服务管理
- 优雅停止现有服务
- 自动端口冲突检测
- PID文件管理

## 部署流程

1. **环境检查** - 验证目录、Java、Maven
2. **停止现有服务** - 安全停止运行中的服务
3. **更新代码** - 从Git仓库拉取最新代码（可选）
4. **构建项目** - Maven编译打包（跳过测试）
5. **准备运行环境** - 创建目录、复制文件
6. **启动服务** - 后台启动Spring Boot应用
7. **验证部署** - 检查服务状态和端口

## 目录结构

```
/home/<USER>/workspaces/vgop-vli/
├── vgop-service/                    # Spring Boot项目
│   ├── src/
│   ├── target/
│   ├── pom.xml
│   ├── logs/                        # 日志目录
│   │   └── vgop-service.log
│   ├── data/                        # 数据目录
│   ├── config/                      # 外部配置
│   └── vgop-service.pid            # PID文件
└── vgop-service-1.0.0-SNAPSHOT.jar # 运行JAR文件
```

## 故障排除

### 1. 环境检查失败
```bash
# 检查Java版本
java -version
/home/<USER>/workspaces/jdk/bin/java -version

# 检查Maven
mvn -version

# 检查目录权限
ls -la /home/<USER>/workspaces/vgop-vli/
```

### 2. 构建失败
```bash
# 手动构建测试
cd /home/<USER>/workspaces/vgop-vli/vgop-service
mvn clean package -DskipTests -Dmaven.test.skip=true
```

### 3. 启动失败
```bash
# 查看详细日志
./local-deploy.sh logs

# 检查端口占用
netstat -ln | grep 8080
```

### 4. Git问题
```bash
# 检查Git状态
cd /home/<USER>/workspaces/vgop-vli
git status
git remote -v

# 手动更新代码
git fetch origin
git reset --hard origin/master
```

## 环境变量控制

```bash
# 部署时不更新代码
UPDATE_CODE=false ./local-deploy.sh deploy

# 使用不同的Java版本
JAVA_HOME=/path/to/java ./local-deploy.sh deploy

# 使用不同的端口
SERVICE_PORT=8081 ./local-deploy.sh deploy
```

## 监控命令

```bash
# 实时监控日志
tail -f /home/<USER>/workspaces/vgop-vli/vgop-service/logs/vgop-service.log

# 检查服务进程
ps aux | grep vgop-service

# 检查内存使用
free -h

# 检查磁盘空间
df -h /home/<USER>/workspaces/
```

## 访问地址

部署成功后，可通过以下地址访问：
- **主页**: http://localhost:8080/vgop
- **健康检查**: http://localhost:8080/vgop/actuator/health
- **API文档**: http://localhost:8080/vgop/swagger-ui.html

## 注意事项

1. 确保 `/home/<USER>/workspaces/jdk` 目录存在且为有效的JDK安装
2. 确保具有 `/home/<USER>/workspaces/vgop-vli` 目录的读写权限
3. 首次运行前检查Git SSH密钥配置
4. 生产环境建议定期备份日志和数据目录 