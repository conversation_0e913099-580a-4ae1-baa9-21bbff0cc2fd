package com.vgop.service.service;

import com.vgop.service.config.ValidationRulesProperties;
import com.vgop.service.dao.ValidationAlertsMapper;
import com.vgop.service.entity.ValidationAlert;
import com.vgop.service.util.CharsetUtil;
import com.vgop.service.validation.ValidationEngine;
import com.vgop.service.validation.ValidationResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.scheduling.annotation.Async;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataValidationServiceImpl implements DataValidationService {

    private final ValidationEngine validationEngine;
    private final ValidationRulesProperties validationRulesProperties;
    private final ValidationAlertsMapper validationAlertsMapper;
    
    // 批量处理配置
    private static final int BATCH_SIZE = 200;
    
    // 错误记录缓存列表
    private final List<ValidationAlert> alertsBuffer = new ArrayList<>();

    @Override
    public String validateAndCleanFile(String originalFilePath, String interfaceId, String dataDate, String taskType) throws IOException {
        log.info("开始校验和清理文件: {}, 接口ID: {}", originalFilePath, interfaceId);
        File originalFile = new File(originalFilePath);
        if (!originalFile.exists()) {
            throw new IOException("原始文件不存在: " + originalFilePath);
        }

        String cleanedFilePath = originalFilePath.replace(".unl", ".cleaned.unl");
        Path tempPath = null;
        long lineNumber = 1;

        try {
            try (BufferedReader reader = Files.newBufferedReader(Paths.get(originalFilePath), StandardCharsets.UTF_8)) {
                tempPath = Files.createTempFile("validation_", ".tmp");
                try (BufferedWriter writer = Files.newBufferedWriter(tempPath, StandardCharsets.UTF_8)) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        // 解析行数据
                        Map<String, String> rowData = parseLineToRowData(line, interfaceId);
                        
                        // 执行校验
                        List<ValidationResult> results = validationEngine.validateRow(
                                interfaceId, rowData, lineNumber, originalFile.getName(), dataDate);
                        
                        if (results.isEmpty()) {
                            // 没有校验错误，保留该行
                            writer.write(line);
                            writer.newLine();
                        } else {
                            // 有校验错误，添加到缓存并跳过该行
                            log.warn("文件 {} 第 {} 行校验失败: {} 个错误", originalFile.getName(), lineNumber, results.size());
                            for (ValidationResult result : results) {
                                addValidationAlertToBuffer(result, interfaceId, originalFile.getName(), lineNumber, line);
                            }
                        }
                        lineNumber++;
                    }
                }
            }

            Files.move(tempPath, Paths.get(cleanedFilePath), StandardCopyOption.REPLACE_EXISTING);
            log.info("文件校验和清理完成, 清理后的文件: {}", cleanedFilePath);

            return cleanedFilePath;
        } finally {
            // 无论是否出现异常，都要处理剩余的错误记录
            flushAlertsBuffer();
        }
    }

    /**
     * 解析行数据为字段映射
     * 
     * @param line 数据行
     * @param interfaceId 接口ID
     * @return 字段映射
     */
    private Map<String, String> parseLineToRowData(String line, String interfaceId) {
        Map<String, String> rowData = new HashMap<>();
        
        // 获取接口配置
        ValidationRulesProperties.InterfaceConfig interfaceConfig = 
                validationRulesProperties.getInterfaces().get(interfaceId.replace(".", "-"));
        
        if (interfaceConfig == null) {
            log.warn("未找到接口配置: {}", interfaceId);
            return rowData;
        }
        
        // 获取分隔符
        String delimiter = interfaceConfig.getDelimiter();
        if (delimiter == null) {
            // 使用默认的二进制分隔符
            delimiter = "\\|"; // 对应ASCII 128
        }
        
        // 分割数据行
        String[] values = line.split(delimiter);
        
        // 根据接口配置映射字段
        if (interfaceConfig.getFields() != null && !interfaceConfig.getFields().isEmpty()) {
            // 使用字段配置
            for (ValidationRulesProperties.FieldConfig fieldConfig : interfaceConfig.getFields()) {
                int index = fieldConfig.getFieldIndex();
                if (index >= 0 && index < values.length) {
                    rowData.put(fieldConfig.getFieldName(), values[index]);
                }
            }
        } else {
            // 使用默认字段名（适用于VGOP1-R2.10-24201接口）
            String[] defaultFieldNames = getDefaultFieldNames(interfaceId);
            for (int i = 0; i < Math.min(values.length, defaultFieldNames.length); i++) {
                rowData.put(defaultFieldNames[i], values[i]);
            }
        }
        
        return rowData;
    }
    
    /**
     * 获取接口的默认字段名
     * 
     * @param interfaceId 接口ID
     * @return 字段名数组
     */
    private String[] getDefaultFieldNames(String interfaceId) {
        switch (interfaceId) {
            case "VGOP1-R2.10-24201":
                return new String[]{
                    "line_number",    // 行号
                    "phonenumber",    // 电话号码
                    "phonestate",     // 电话状态
                    "phoneimsi",      // IMSI
                    "phoneimei",      // IMEI
                    "locationid",     // 位置ID
                    "provinceid",     // 省份ID
                    "openingtime",    // 开通时间
                    "Optime",         // 操作时间
                    "sex"             // 性别
                };
            // 其他接口的字段配置可以在这里添加
            default:
                log.warn("未知的接口ID: {}, 使用通用字段名", interfaceId);
                return new String[]{"field1", "field2", "field3", "field4", "field5", 
                                  "field6", "field7", "field8", "field9", "field10"};
        }
    }

    /**
     * 添加校验告警到缓存中
     * 增加字符编码安全处理
     */
    private void addValidationAlertToBuffer(ValidationResult result, String interfaceId, String fileName, long lineNumber, String errorData) {
        try {
            ValidationRulesProperties.InterfaceConfig interfaceConfig = validationRulesProperties.getInterfaces().get(interfaceId);
            String interfaceName = interfaceId;

            // 对所有字符串字段进行编码安全处理
            ValidationAlert alert = ValidationAlert.builder()
                    .alertTime(ValidationAlert.getCurrentTimeString())
                    .interfaceName(CharsetUtil.prepareDatabaseString(interfaceName, 100))
                    .alertType(CharsetUtil.prepareDatabaseString("数据校验失败", 50))
                    .alertLevel(ValidationAlert.AlertLevel.ERROR)
                    .alertMessage(CharsetUtil.prepareDatabaseString(result.getMessage(), 500))
                    .fileName(CharsetUtil.prepareDatabaseString(fileName, 255))
                    .lineNumber(lineNumber)
                    .errorData(CharsetUtil.prepareDatabaseString(errorData, 1000)) // 限制错误数据长度
                    .status(ValidationAlert.AlertStatus.NEW)
                    .build();
            
            synchronized (alertsBuffer) {
                alertsBuffer.add(alert);
                
                // 当缓存达到批次大小时，批量入库（每200条）
                if (alertsBuffer.size() >= BATCH_SIZE) {
                    batchInsertAlerts();
                }
            }
        } catch (Exception e) {
            log.error("添加校验告警到缓存失败", e);
            
            // 记录原始错误信息到日志（防止字符编码问题导致完全丢失错误信息）
            log.error("原始校验错误 - 接口: {}, 文件: {}, 行号: {}, 消息: {}", 
                    interfaceId, fileName, lineNumber, result.getMessage());
        }
    }

    /**
     * 异步批量插入告警记录
     */
    @Async("dbAsyncExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertAlertsAsync(List<ValidationAlert> batchAlerts) {
        if (batchAlerts == null || batchAlerts.isEmpty()) {
            return;
        }
        
        try {
            // 使用事务内逐条插入，兼容GBase/Informix数据库
            int insertedCount = 0;
            for (ValidationAlert alert : batchAlerts) {
                try {
                    validationAlertsMapper.insert(alert);
                    insertedCount++;
                } catch (Exception e) {
                    log.warn("插入单条校验告警记录失败: {}", e.getMessage());
                }
            }
            log.info("异步批量插入校验告警记录完成: {} 条", insertedCount);
        } catch (Exception e) {
            log.error("异步批量插入校验告警记录失败", e);
        }
    }

    /**
     * 同步触发批量插入（从缓存中取数据并异步处理）
     */
    private void batchInsertAlerts() {
        if (alertsBuffer.isEmpty()) {
            return;
        }
        
        try {
            List<ValidationAlert> batchAlerts = new ArrayList<>(alertsBuffer);
            alertsBuffer.clear();
            
            // 异步处理插入操作
            batchInsertAlertsAsync(batchAlerts);
            log.debug("已提交 {} 条告警记录到异步处理队列", batchAlerts.size());
        } catch (Exception e) {
            log.error("提交批量插入任务失败", e);
        }
    }

    /**
     * 刷新告警缓存，将剩余记录入库
     */
    private void flushAlertsBuffer() {
        synchronized (alertsBuffer) {
            if (!alertsBuffer.isEmpty()) {
                log.info("处理剩余的校验告警记录: {} 条", alertsBuffer.size());
                batchInsertAlerts();
                log.info("已提交所有剩余告警记录到异步处理队列");
            }
        }
    }
}