-- 数据库表结构验证SQL查询
-- 用于开发环境调试和测试

-- 1. 检查systables系统表（Informix数据库）
SELECT tabname FROM systables WHERE tabtype = 'T' AND tabname LIKE '%mcn%' OR tabname LIKE '%vgop%';

-- 2. 检查mcn_user_major表是否存在及其结构
SELECT FIRST 1 * FROM mcn_user_major;

-- 3. 检查vgop_metrics_history表是否存在及其结构
SELECT FIRST 1 * FROM vgop_metrics_history;

-- 4. 查看mcn_user_major表的列信息
SELECT colname, coltype FROM syscolumns 
WHERE tabid = (SELECT tabid FROM systables WHERE tabname = 'mcn_user_major')
ORDER BY colno;

-- 5. 查看vgop_metrics_history表的列信息
SELECT colname, coltype FROM syscolumns 
WHERE tabid = (SELECT tabid FROM systables WHERE tabname = 'vgop_metrics_history')
ORDER BY colno;

-- 6. 测试简单查询 - mcn_user_major
SELECT COUNT(*) as total_users FROM mcn_user_major;

-- 7. 测试简单查询 - vgop_metrics_history
SELECT COUNT(*) as total_metrics FROM vgop_metrics_history;

-- 8. 查看mcn_user_major表的数据示例
SELECT phonenumber, phonestate, locationid 
FROM mcn_user_major 
WHERE phonestate IN ('0','1') 
LIMIT 5;

-- 9. 查看当前数据库名称
SELECT USER AS current_user, SITENAME AS database_name FROM systables WHERE tabid = 1;

-- ===========================================
-- VGOP数据质量检测SQL修复验证
-- ===========================================

-- 1. 验证修复后的findValidationErrorsByInterfaceAndDate查询
-- 测试LENGTH函数对TEXT类型字段的兼容性
SELECT alert_id, alert_time, interface_name, alert_type, alert_level, alert_message,
       file_name, line_number, error_data, field_errors, metric_details, excel_report_path,
       status, handled_by, handled_time
FROM vgop_validation_alerts
WHERE interface_name = 'VGOP1-R2.10-24201'
  AND SUBSTR(alert_time, 1, 8) = '20241201'
  AND alert_type = '数据校验失败'
  AND (field_errors IS NOT NULL AND LENGTH(field_errors) > 0)
ORDER BY alert_time DESC, line_number ASC
LIMIT 10;

-- 2. 对比验证：检查修复前后的逻辑一致性
-- 这个查询应该返回相同的结果集（如果支持TRIM的话）
-- SELECT alert_id, LENGTH(field_errors) as field_length, 
--        (CASE WHEN field_errors IS NULL THEN 'NULL'
--              WHEN LENGTH(field_errors) = 0 THEN 'EMPTY'
--              ELSE 'HAS_CONTENT' END) as content_status
-- FROM vgop_validation_alerts
-- WHERE interface_name = 'VGOP1-R2.10-24201'
-- ORDER BY alert_time DESC LIMIT 5;

-- 3. 验证TEXT字段的基本操作
-- 确保LENGTH函数对TEXT类型字段工作正常
SELECT 
    COUNT(*) as total_records,
    COUNT(field_errors) as non_null_field_errors,
    SUM(CASE WHEN LENGTH(field_errors) > 0 THEN 1 ELSE 0 END) as non_empty_field_errors,
    MIN(LENGTH(field_errors)) as min_length,
    MAX(LENGTH(field_errors)) as max_length
FROM vgop_validation_alerts
WHERE alert_type = '数据校验失败';

-- 4. 测试不同的TEXT字段判空方式
-- 验证LENGTH方式与其他方式的一致性
SELECT 
    'LENGTH > 0' as method,
    COUNT(*) as matching_records
FROM vgop_validation_alerts
WHERE field_errors IS NOT NULL AND LENGTH(field_errors) > 0

UNION ALL

SELECT 
    'NOT EQUALS EMPTY' as method,
    COUNT(*) as matching_records  
FROM vgop_validation_alerts
WHERE field_errors IS NOT NULL AND field_errors != ''

UNION ALL

SELECT 
    'ONLY NOT NULL' as method,
    COUNT(*) as matching_records
FROM vgop_validation_alerts
WHERE field_errors IS NOT NULL;

-- 5. 性能测试查询
-- 验证修复后查询的执行计划和性能
EXPLAIN SELECT alert_id, alert_time, interface_name, field_errors
FROM vgop_validation_alerts
WHERE interface_name = 'VGOP1-R2.10-24201'
  AND alert_type = '数据校验失败'
  AND (field_errors IS NOT NULL AND LENGTH(field_errors) > 0)
ORDER BY alert_time DESC;

-- 6. 数据质量检测相关统计
-- 验证修复对业务逻辑的影响
SELECT 
    interface_name,
    COUNT(*) as total_alerts,
    SUM(CASE WHEN field_errors IS NOT NULL AND LENGTH(field_errors) > 0 THEN 1 ELSE 0 END) as with_field_errors,
    SUM(CASE WHEN error_data IS NOT NULL AND LENGTH(error_data) > 0 THEN 1 ELSE 0 END) as with_error_data
FROM vgop_validation_alerts
WHERE alert_type = '数据校验失败'
  AND SUBSTR(alert_time, 1, 8) >= '20241201'
GROUP BY interface_name
ORDER BY interface_name; 