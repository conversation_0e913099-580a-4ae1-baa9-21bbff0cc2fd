package com.vgop.service.service;

import com.vgop.service.config.VgopAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 目录初始化服务
 * 在应用启动时确保所有必要的目录存在
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Order(2) // 在配置验证之后执行
public class DirectoryInitService implements ApplicationRunner {
    
    private final VgopAppConfig appConfig;
    
    @Autowired
    public DirectoryInitService(VgopAppConfig appConfig) {
        this.appConfig = appConfig;
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化目录结构...");
        
        // 初始化基础目录
        initializeBaseDirectories();
        
        // 初始化数据日期目录（最近几天的）
        initializeDataDirectories();
        
        log.info("目录结构初始化完成");
    }
    
    /**
     * 初始化基础目录
     */
    private void initializeBaseDirectories() {
        String exportRoot = appConfig.getBasePath().getExportRoot();
        String logRoot = appConfig.getBasePath().getLogRoot();
        String backupRoot = appConfig.getBasePath().getBackupRoot();
        
        createDirectoryIfNotExists(exportRoot, "数据导出根目录");
        createDirectoryIfNotExists(logRoot, "日志根目录");
        createDirectoryIfNotExists(backupRoot, "备份根目录");
        
        // 创建告警目录
        if (appConfig.getAlert() != null && 
            appConfig.getAlert().getStorage() != null && 
            appConfig.getAlert().getStorage().getAlertFilePath() != null) {
            createDirectoryIfNotExists(appConfig.getAlert().getStorage().getAlertFilePath(), "告警文件目录");
        }
    }
    
    /**
     * 初始化数据日期目录
     */
    private void initializeDataDirectories() {
        String exportRoot = appConfig.getBasePath().getExportRoot();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        
        // 创建最近3天和未来2天的目录
        for (int i = -3; i <= 2; i++) {
            LocalDate date = LocalDate.now().plusDays(i);
            String dateStr = date.format(formatter);
            
            String dayPath = exportRoot + "/" + dateStr + "/day";
            String monthPath = exportRoot + "/" + dateStr + "/month";
            
            createDirectoryIfNotExists(dayPath, "日数据目录 - " + dateStr);
            createDirectoryIfNotExists(monthPath, "月数据目录 - " + dateStr);
        }
        
        // 创建月度目录
        String monthStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        String monthlyPath = exportRoot + "/" + monthStr;
        createDirectoryIfNotExists(monthlyPath, "月度数据目录 - " + monthStr);
    }
    
    /**
     * 创建目录（如果不存在）
     */
    private void createDirectoryIfNotExists(String path, String description) {
        try {
            File directory = new File(path);
            if (!directory.exists()) {
                boolean created = directory.mkdirs();
                if (created) {
                    log.info("创建目录成功: {} - {}", description, path);
                } else {
                    log.error("创建目录失败: {} - {}", description, path);
                }
            } else {
                log.debug("目录已存在: {} - {}", description, path);
            }
        } catch (Exception e) {
            log.error("创建目录时发生异常: {} - {}", description, path, e);
        }
    }
    
    /**
     * 确保特定日期的目录存在
     */
    public void ensureDateDirectoryExists(String dataDate) {
        String exportRoot = appConfig.getBasePath().getExportRoot();
        
        String dayPath = exportRoot + "/" + dataDate + "/day";
        String monthPath = exportRoot + "/" + dataDate + "/month";
        
        createDirectoryIfNotExists(dayPath, "日数据目录 - " + dataDate);
        createDirectoryIfNotExists(monthPath, "月数据目录 - " + dataDate);
    }
} 