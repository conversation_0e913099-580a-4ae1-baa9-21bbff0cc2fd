# 脚本 `VGOP1-R2.10-24201month.sh` SQL逻辑分析

-   **脚本用途**: 抽取截止到**上月末**的**全量**用户主号码信息。
-   **数据来源**: `mcn_user_major` (用户主号码信息表), `bossprovince` (省份代码表)。
-   **核心逻辑**:
    -   `mum.openingtime < '${endtime}'`: 脚本计算的 `${endtime}` 是执行月的第一天 `00:00:00`。此条件筛选出所有在此之前开户的用户，实现全量抽取。
    -   `left join bossprovince`: 关联省份代码表，转换省份ID。
-   **输出内容**: 全量的用户主号码详细信息。
-   **说明**: 该脚本生成的是**全量快照**数据，用于月度的数据核对或归档。文件名中的日期是**上一个月份**。 