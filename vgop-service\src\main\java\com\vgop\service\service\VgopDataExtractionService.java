package com.vgop.service.service;

import com.vgop.service.config.VgopProperties;
import com.vgop.service.dao.DataExportMapper;
import com.vgop.service.exception.DatabaseException;
import com.vgop.service.exception.FileProcessingException;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * VGOP数据抽取服务
 * 实现Shell脚本中的数据提取逻辑，对应各个VGOP脚本的数据查询和导出功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VgopDataExtractionService {
    
    private final DataExportMapper dataExportMapper;
    private final VgopProperties vgopProperties;
    
    /**
     * 根据脚本ID提取数据
     * 
     * @param scriptId 脚本标识
     * @param actionInstanceId 任务实例ID
     * @param imagePath 镜像路径
     * @param dateId 日期ID
     * @param traceFlag 跟踪标志
     * @param sourceDbName 源数据库名
     * @return 生成的临时文件路径，如果没有数据则返回null
     */
    public String extractData(String scriptId, String actionInstanceId, String imagePath, 
                             String dateId, String traceFlag, String sourceDbName) {
        log.info("开始提取数据: scriptId={}, actionInstanceId={}, dateId={}", 
                scriptId, actionInstanceId, dateId);
        
        try {
            switch (scriptId) {
                // 日统计脚本
                case "VGOP1-R2.11-24101":
                    return extractBanalyseData(actionInstanceId, imagePath, dateId, traceFlag, sourceDbName);
                case "VGOP1-R2.10-24201day":
                    return extractDailyNewMajorUsers(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.10-24202day":
                    return extractMinorUsers(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.10-24203":
                    return extractDailyUserActivity(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.10-24205":
                    return extractDailyCallLogs(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.10-24206":
                    return extractDailySmsLogs(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.10-24207day":
                    return extractDailyNewSecUsers(actionInstanceId, imagePath, dateId);
                    
                // 维表数据
                case "VGOP1-R2.13-24301":
                    return extractServiceTypes(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.13-24302":
                    return extractChannelData(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.13-24303":
                    return extractShutdownData(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.13-24304":
                    return extractMcnTypeData(actionInstanceId, imagePath, dateId);
                    
                // 月统计脚本
                case "VGOP1-R2.10-24201month":
                    return extractMajorUsersSnapshot(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.10-24202month":
                    return extractMinorUsersSnapshot(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.10-24204":
                    return extractMonthlyOpLogs(actionInstanceId, imagePath, dateId);
                case "VGOP1-R2.10-24207month":
                    return extractSecUsersSnapshot(actionInstanceId, imagePath, dateId);
                    
                default:
                    log.warn("未支持的脚本ID: {}", scriptId);
                    return null;
            }
        } catch (Exception e) {
            log.error("数据提取失败: scriptId={}, error={}", scriptId, e.getMessage(), e);
            throw new DatabaseException("数据提取失败: " + scriptId, e);
        }
    }
    
    /**
     * 提取业务分析数据
     * 对应VGOP1-R2.11-24101.sh脚本
     */
    private String extractBanalyseData(String actionInstanceId, String imagePath, String dateId,
                                     String traceFlag, String sourceDbName) {
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("执行业务分析存储过程: beforeDay={}", beforeDay);
        
        // 调用存储过程
        dataExportMapper.callBmsSpVgopBanalyse(beforeDay, traceFlag, actionInstanceId);
        
        // 查询分析结果
        List<Map<String, Object>> data = dataExportMapper.selectBanalyseData(beforeDay);
        
        return writeDataToFile(data, "i_10000", beforeDay, "VGOP1-R2.11-24101", imagePath, "day");
    }
    
    /**
     * 提取每日新增主号用户信息
     * 对应VGOP1-R2.10-24201day.sh脚本
     */
    private String extractDailyNewMajorUsers(String actionInstanceId, String imagePath, String dateId) {
        String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
        String startTime = timeRange[0];
        String endTime = timeRange[1];
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出每日新增主号用户: startTime={}, endTime={}", startTime, endTime);
        
        List<Map<String, Object>> data = dataExportMapper.selectDailyNewMajorUsers(startTime, endTime);
        
        return writeDataToFile(data, "a_10000", beforeDay, "VGOP1-R2.10-24201", imagePath, "day");
    }
    
    /**
     * 提取副号用户信息
     * 对应VGOP1-R2.10-24202day.sh脚本
     */
    private String extractMinorUsers(String actionInstanceId, String imagePath, String dateId) {
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出副号用户信息: beforeDay={}", beforeDay);
        
        // mcnnum为1,2,3的副号记录
        List<Map<String, Object>> data = dataExportMapper.selectMinorUsers("1,2,3");
        
        return writeDataToFile(data, "i_10000", beforeDay, "VGOP1-R2.10-24202", imagePath, "day");
    }
    
    /**
     * 提取每日用户活动日志
     * 对应VGOP1-R2.10-24203.sh脚本
     */
    private String extractDailyUserActivity(String actionInstanceId, String imagePath, String dateId) {
        String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
        String startTime = timeRange[0];
        String endTime = timeRange[1];
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出每日用户活动日志: startTime={}, endTime={}", startTime, endTime);
        
        List<Map<String, Object>> data = dataExportMapper.selectDailyUserActivity(startTime, endTime);
        
        return writeDataToFile(data, "a_10000", beforeDay, "VGOP1-R2.10-24203", imagePath, "day");
    }
    
    /**
     * 提取每日通话话单记录
     * 对应VGOP1-R2.10-24205.sh脚本
     */
    private String extractDailyCallLogs(String actionInstanceId, String imagePath, String dateId) {
        String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
        String startTime = timeRange[0];
        String endTime = timeRange[1];
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出每日通话话单: startTime={}, endTime={}", startTime, endTime);
        
        List<Map<String, Object>> data = dataExportMapper.selectDailyCallLogs(startTime, endTime);
        
        return writeDataToFile(data, "a_10000", beforeDay, "VGOP1-R2.10-24205", imagePath, "day");
    }
    
    /**
     * 提取每日短信日志
     * 对应VGOP1-R2.10-24206.sh脚本
     */
    private String extractDailySmsLogs(String actionInstanceId, String imagePath, String dateId) {
        String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
        String startTime = timeRange[0];
        String endTime = timeRange[1];
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出每日短信日志: startTime={}, endTime={}", startTime, endTime);
        
        List<Map<String, Object>> data = dataExportMapper.selectDailySmsLogs(startTime, endTime);
        
        return writeDataToFile(data, "a_10000", beforeDay, "VGOP1-R2.10-24206", imagePath, "day");
    }
    
    /**
     * 提取每日新增实体副号用户信息
     * 对应VGOP1-R2.10-24207day.sh脚本
     */
    private String extractDailyNewSecUsers(String actionInstanceId, String imagePath, String dateId) {
        String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
        String startTime = timeRange[0];
        String endTime = timeRange[1];
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出每日新增实体副号用户: startTime={}, endTime={}", startTime, endTime);
        
        List<Map<String, Object>> data = dataExportMapper.selectDailyNewSecUsers(startTime, endTime);
        
        return writeDataToFile(data, "a_10000", beforeDay, "VGOP1-R2.10-24207", imagePath, "day");
    }
    
    /**
     * 提取维表数据 - 服务类型
     * 对应VGOP1-R2.13-24301.sh脚本
     */
    private String extractServiceTypes(String actionInstanceId, String imagePath, String dateId) {
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出维表 - 服务类型: beforeDay={}", beforeDay);
        
        List<Map<String, Object>> data = dataExportMapper.selectServTypeData();
        
        return writeDataToFile(data, "i_10000", beforeDay, "VGOP1-R2.13-24301", imagePath, "day");
    }
    
    /**
     * 提取维表数据 - 渠道信息
     * 对应VGOP1-R2.13-24302.sh脚本
     */
    private String extractChannelData(String actionInstanceId, String imagePath, String dateId) {
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出维表 - 渠道信息: beforeDay={}", beforeDay);
        
        List<Map<String, Object>> data = dataExportMapper.selectChannelData();
        
        return writeDataToFile(data, "i_10000", beforeDay, "VGOP1-R2.13-24302", imagePath, "day");
    }
    
    /**
     * 提取维表数据 - 关机原因
     * 对应VGOP1-R2.13-24303.sh脚本
     */
    private String extractShutdownData(String actionInstanceId, String imagePath, String dateId) {
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出维表 - 关机原因: beforeDay={}", beforeDay);
        
        List<Map<String, Object>> data = dataExportMapper.selectShutdownData();
        
        return writeDataToFile(data, "i_10000", beforeDay, "VGOP1-R2.13-24303", imagePath, "day");
    }
    
    /**
     * 提取维表数据 - MCN类型
     * 对应VGOP1-R2.13-24304.sh脚本
     */
    private String extractMcnTypeData(String actionInstanceId, String imagePath, String dateId) {
        String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
        
        log.info("导出维表 - MCN类型: beforeDay={}", beforeDay);
        
        List<Map<String, Object>> data = dataExportMapper.selectMcnTypeData();
        
        return writeDataToFile(data, "i_10000", beforeDay, "VGOP1-R2.13-24304", imagePath, "day");
    }
    
    /**
     * 提取主号用户全量快照（月统计）
     * 对应VGOP1-R2.10-24201month.sh脚本
     */
    private String extractMajorUsersSnapshot(String actionInstanceId, String imagePath, String dateId) {
        String beforeMonth = DateTimeUtil.calculateBeforeMonth(dateId);
        String endDate = DateTimeUtil.getLastDayOfMonth(beforeMonth);
        
        log.info("导出主号用户全量快照: endDate={}", endDate);
        
        List<Map<String, Object>> data = dataExportMapper.selectMajorUsersSnapshot(endDate);
        
        return writeDataToFile(data, "i_10000", beforeMonth, "VGOP1-R2.10-24201", imagePath, "month");
    }
    
    /**
     * 提取副号用户全量快照（月统计）
     * 对应VGOP1-R2.10-24202month.sh脚本
     */
    private String extractMinorUsersSnapshot(String actionInstanceId, String imagePath, String dateId) {
        String beforeMonth = DateTimeUtil.calculateBeforeMonth(dateId);
        String endDate = DateTimeUtil.getLastDayOfMonth(beforeMonth);
        
        log.info("导出副号用户全量快照: endDate={}", endDate);
        
        List<Map<String, Object>> data = dataExportMapper.selectMinorUsersSnapshot(endDate);
        
        return writeDataToFile(data, "i_10000", beforeMonth, "VGOP1-R2.10-24202", imagePath, "month");
    }
    
    /**
     * 提取月度操作日志
     * 对应VGOP1-R2.10-24204.sh脚本
     */
    private String extractMonthlyOpLogs(String actionInstanceId, String imagePath, String dateId) {
        String beforeMonth = DateTimeUtil.calculateBeforeMonth(dateId);
        String[] monthTimeRange = DateTimeUtil.calculateMonthTimeRange(beforeMonth);
        String startTime = monthTimeRange[0];
        String endTime = monthTimeRange[1];
        
        log.info("导出月度操作日志: startTime={}, endTime={}", startTime, endTime);
        
        List<Map<String, Object>> data = dataExportMapper.selectMonthlyOpLogs(startTime, endTime);
        
        return writeDataToFile(data, "i_10000", beforeMonth, "VGOP1-R2.10-24204", imagePath, "month");
    }
    
    /**
     * 提取特定状态实体副号用户全量快照
     * 对应VGOP1-R2.10-24207month.sh脚本
     */
    private String extractSecUsersSnapshot(String actionInstanceId, String imagePath, String dateId) {
        String beforeMonth = DateTimeUtil.calculateBeforeMonth(dateId);
        
        log.info("导出实体副号用户全量快照: beforeMonth={}", beforeMonth);
        
        // businessState为'X'的用户记录
        List<Map<String, Object>> data = dataExportMapper.selectSecUsersSnapshot("X");
        
        return writeDataToFile(data, "i_10000", beforeMonth, "VGOP1-R2.10-24207", imagePath, "month");
    }
    
    /**
     * 将数据写入文件
     * 
     * @param data 数据列表
     * @param filePrefix 文件前缀
     * @param dataDate 数据日期
     * @param scriptId 脚本ID
     * @param imagePath 镜像路径
     * @param subPath 子路径（day/month）
     * @return 生成的文件路径
     */
    private String writeDataToFile(List<Map<String, Object>> data, String filePrefix, 
                                  String dataDate, String scriptId, String imagePath, String subPath) {
        if (data == null || data.isEmpty()) {
            log.info("没有数据需要导出: {}", scriptId);
            return null;
        }
        
        String tmpFileName = String.format("%s_%s_%s.unl", filePrefix, dataDate, scriptId);
        String dataPath = String.format("%s/VGOPdata/datafile/%s/%s/", imagePath, dataDate, subPath);
        String unloadFileName = dataPath + tmpFileName;
        
        try {
            // 确保目录存在
            Path dataDir = Paths.get(dataPath);
            Files.createDirectories(dataDir);
            
            // 写入数据到.unl文件
            try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(unloadFileName), StandardCharsets.UTF_8)) {
                for (Map<String, Object> row : data) {
                    StringBuilder line = new StringBuilder();
                    boolean first = true;
                    
                    for (Object value : row.values()) {
                        if (!first) {
                            line.append(vgopProperties.getDatabase().getDefaultColumnSeparator());
                        }
                        line.append(value != null ? value.toString() : "");
                        first = false;
                    }
                    
                    writer.write(line.toString());
                    writer.newLine();
                }
            }
            
            log.info("数据导出完成: {} -> {}, 记录数: {}", scriptId, unloadFileName, data.size());
            return unloadFileName;
            
        } catch (IOException e) {
            log.error("数据文件写入失败: {}", unloadFileName, e);
            throw new FileProcessingException("数据文件写入失败: " + unloadFileName, e);
        }
    }
} 