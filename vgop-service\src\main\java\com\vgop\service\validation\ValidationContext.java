package com.vgop.service.validation;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 校验上下文类
 * 包含校验过程中需要的上下文信息
 */
@Data
@Builder
public class ValidationContext {
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 数据日期
     */
    private String dataDate;
    
    /**
     * 行号
     */
    private long rowNumber;
    
    /**
     * 当前行数据（字段名 -> 字段值）
     */
    private Map<String, String> rowData;
    
    /**
     * 字段元数据（字段名 -> 字段元数据）
     */
    private Map<String, FieldMetadata> fieldMetadata;
    
    /**
     * 获取指定字段的值
     * 
     * @param fieldName 字段名
     * @return 字段值
     */
    public String getFieldValue(String fieldName) {
        return rowData != null ? rowData.get(fieldName) : null;
    }
    
    /**
     * 获取指定字段的元数据
     * 
     * @param fieldName 字段名
     * @return 字段元数据
     */
    public FieldMetadata getFieldMetadata(String fieldName) {
        return fieldMetadata != null ? fieldMetadata.get(fieldName) : null;
    }
} 