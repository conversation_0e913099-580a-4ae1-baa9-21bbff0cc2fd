package com.vgop.service.dto;

import com.vgop.service.entity.TaskConfig.TaskType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务执行请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TaskExecutionRequest {
    
    /**
     * 动作实例ID
     */
    @NotBlank(message = "动作实例ID不能为空")
    private String actionInstanceId;
    
    /**
     * 接口ID (对应配置文件中的interfaceId)
     */
    @NotBlank(message = "接口ID不能为空")
    private String interfaceId;
    
    /**
     * 镜像路径
     */
    @NotBlank(message = "镜像路径不能为空")
    private String imagePath;
    
    /**
     * 日期ID (格式: YYYYMMDDHHMMSS)
     */
    @NotBlank(message = "日期ID不能为空")
    private String dateId;
    
    /**
     * 跟踪标志
     */
    private String traceFlag;
    
    /**
     * 源数据库名
     */
    @NotBlank(message = "源数据库名不能为空")
    private String sourceDbName;
    
    /**
     * 列分隔符 (默认为 "|")
     */
    private String columnSeparator;
    
    /**
     * 任务类型
     */
    @NotNull(message = "任务类型不能为空")
    private TaskType taskType;
    
    /**
     * 是否异步执行 (默认为true)
     */
    private Boolean async = true;
} 