package com.vgop.service.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 */
@Configuration
@EnableAsync
@RequiredArgsConstructor
public class ThreadPoolConfig {
    
    private final VgopProperties vgopProperties;
    
    /**
     * 任务执行线程池
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        VgopProperties.Scheduler scheduler = vgopProperties.getScheduler();
        executor.setCorePoolSize(scheduler.getCorePoolSize());
        executor.setMaxPoolSize(scheduler.getMaxPoolSize());
        executor.setQueueCapacity(scheduler.getQueueCapacity());
        executor.setKeepAliveSeconds(scheduler.getKeepAliveSeconds());
        executor.setThreadNamePrefix("vgop-task-");
        
        // 拒绝策略：由调用者线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 线程池关闭时等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }
    
    /**
     * 文件处理线程池
     */
    @Bean("fileProcessExecutor")
    public Executor fileProcessExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 文件处理使用较少的线程数，避免IO竞争
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(50);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("vgop-file-");
        
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }
    
    /**
     * 数据库异步操作线程池
     */
    @Bean("dbAsyncExecutor")
    public Executor dbAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 数据库异步操作使用独立的线程池
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(1000);  // 较大的队列容量，缓存更多数据库操作
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("vgop-db-async-");
        
        // 拒绝策略：由调用者线程处理，确保数据不丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);  // 数据库操作需要更长的等待时间
        
        executor.initialize();
        return executor;
    }
} 