package com.vgop.service.service;

import com.vgop.service.dao.primary.RevisionTimesMapper;
import com.vgop.service.entity.RevisionTimes;
import com.vgop.service.exception.VgopException;
import com.vgop.service.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 版本控制服务
 * 负责管理文件的修订版本号
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class RevisionTimesService {
    
    private final RevisionTimesMapper revisionTimesMapper;
    
    @Autowired
    public RevisionTimesService(RevisionTimesMapper revisionTimesMapper) {
        this.revisionTimesMapper = revisionTimesMapper;
    }
    
    /**
     * 获取并更新版本号
     * 如果记录不存在，创建新记录并返回版本号0
     * 如果记录存在，版本号加1并返回新版本号
     * 
     * @param dataDate 数据日期（YYYYMMDD）
     * @param tmpFileName 临时文件名
     * @return 版本信息
     */
    @Transactional(transactionManager = "primaryTransactionManager")
    public RevisionTimes getAndUpdateRevisionTimes(String dataDate, String tmpFileName) {
        log.debug("获取并更新版本号 - 日期: {}, 文件: {}", dataDate, tmpFileName);
        
        try {
            // 使用行锁查询当前版本信息
            RevisionTimes current = revisionTimesMapper.selectForUpdate(dataDate, tmpFileName);
            String currentTime = DateUtil.formatDateTime(LocalDateTime.now());
            
            if (current == null) {
                // 记录不存在，创建新记录
                log.info("版本记录不存在，创建新记录 - 日期: {}, 文件: {}", dataDate, tmpFileName);
                
                RevisionTimes newRevision = RevisionTimes.builder()
                        .datatime(dataDate)
                        .times(0)
                        .tmpfilename(tmpFileName)
                        .optime(currentTime)
                        .build();
                
                int rows = revisionTimesMapper.insert(newRevision);
                if (rows != 1) {
                    throw new VgopException("创建版本记录失败");
                }
                
                return newRevision;
            } else {
                // 记录存在，更新版本号
                int newTimes = current.getTimes() + 1;
                log.info("更新版本号 - 日期: {}, 文件: {}, 旧版本: {}, 新版本: {}", 
                        dataDate, tmpFileName, current.getTimes(), newTimes);
                
                int rows = revisionTimesMapper.updateTimes(dataDate, tmpFileName, newTimes, currentTime);
                if (rows != 1) {
                    throw new VgopException("更新版本记录失败");
                }
                
                // 返回更新后的版本信息
                current.setTimes(newTimes);
                current.setOptime(currentTime);
                return current;
            }
        } catch (Exception e) {
            log.error("获取并更新版本号失败 - 日期: {}, 文件: {}", dataDate, tmpFileName, e);
            throw new VgopException("版本控制操作失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询当前版本信息（不更新）
     * 
     * @param dataDate 数据日期
     * @param tmpFileName 临时文件名
     * @return 版本信息，如果不存在返回null
     */
    public RevisionTimes getCurrentRevision(String dataDate, String tmpFileName) {
        try {
            return revisionTimesMapper.findByDatatimeAndFilename(dataDate, tmpFileName);
        } catch (Exception e) {
            log.error("查询版本信息失败 - 日期: {}, 文件: {}", dataDate, tmpFileName, e);
            return null;
        }
    }
    
    /**
     * 重置版本号（用于特殊场景，如数据重新生成）
     * 
     * @param dataDate 数据日期
     * @param tmpFileName 临时文件名
     * @return 是否重置成功
     */
    @Transactional(transactionManager = "primaryTransactionManager")
    public boolean resetRevision(String dataDate, String tmpFileName) {
        try {
            String currentTime = DateUtil.formatDateTime(LocalDateTime.now());
            int rows = revisionTimesMapper.updateTimes(dataDate, tmpFileName, 0, currentTime);
            
            if (rows == 0) {
                // 记录不存在，创建新记录
                RevisionTimes newRevision = RevisionTimes.builder()
                        .datatime(dataDate)
                        .times(0)
                        .tmpfilename(tmpFileName)
                        .optime(currentTime)
                        .build();
                rows = revisionTimesMapper.insert(newRevision);
            }
            
            return rows > 0;
        } catch (Exception e) {
            log.error("重置版本号失败 - 日期: {}, 文件: {}", dataDate, tmpFileName, e);
            return false;
        }
    }
    
    /**
     * 获取指定日期和周期类型的版本信息
     * 
     * @param dataDate 数据日期
     * @param cycleType 周期类型（daily/monthly）
     * @return 版本信息
     */
    public RevisionTimes getRevisionTimes(String dataDate, String cycleType) {
        String tmpFileName = generateTmpFileName(dataDate, cycleType);
        return getCurrentRevision(dataDate, tmpFileName);
    }
    
    /**
     * 获取或创建版本信息
     * 如果不存在则创建新记录
     * 
     * @param dataDate 数据日期
     * @param cycleType 周期类型（daily/monthly）
     * @return 版本信息
     */
    @Transactional(transactionManager = "primaryTransactionManager")
    public RevisionTimes getOrCreateRevisionTimes(String dataDate, String cycleType) {
        String tmpFileName = generateTmpFileName(dataDate, cycleType);
        RevisionTimes revision = getCurrentRevision(dataDate, tmpFileName);
        
        if (revision == null) {
            log.info("版本记录不存在，创建新记录 - 日期: {}, 周期: {}", dataDate, cycleType);
            String currentTime = DateUtil.formatDateTime(LocalDateTime.now());
            
            RevisionTimes newRevision = RevisionTimes.builder()
                    .datatime(dataDate)
                    .times(0)
                    .tmpfilename(tmpFileName)
                    .optime(currentTime)
                    .build();
            
            int rows = revisionTimesMapper.insert(newRevision);
            if (rows != 1) {
                throw new VgopException("创建版本记录失败");
            }
            
            return newRevision;
        }
        
        return revision;
    }
    
    // 注意：由于当前表结构不支持 exported、transferred、validated 字段，
    // 相关的状态更新方法已移除。如需此功能，建议：
    // 1. 使用单独的状态表来记录这些信息
    // 2. 或考虑在现有字段中编码状态信息
    
    /**
     * 生成临时文件名
     * 
     * @param dataDate 数据日期
     * @param cycleType 周期类型
     * @return 临时文件名
     */
    private String generateTmpFileName(String dataDate, String cycleType) {
        return "vgop_" + cycleType + "_" + dataDate + ".tmp";
    }
} 