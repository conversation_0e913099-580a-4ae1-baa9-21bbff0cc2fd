package com.vgop.service.validation;

import lombok.Builder;
import lombok.Data;

/**
 * 校验结果类
 * 包含校验是否通过、错误信息等
 */
@Data
@Builder
public class ValidationResult {
    /**
     * 是否通过校验
     */
    private boolean valid;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 字段名
     */
    private String fieldName;
    
    /**
     * 字段值
     */
    private String fieldValue;
    
    /**
     * 行号
     */
    private long rowNumber;
    
    /**
     * 严重程度
     */
    private ValidationSeverity severity;
    
    /**
     * 创建一个通过校验的结果
     * 
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @return 校验结果
     */
    public static ValidationResult valid(String ruleId, String ruleName) {
        return ValidationResult.builder()
                .valid(true)
                .ruleId(ruleId)
                .ruleName(ruleName)
                .build();
    }
    
    /**
     * 创建一个未通过校验的结果
     * 
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param message 错误消息
     * @param rowNumber 行号
     * @param severity 严重程度
     * @return 校验结果
     */
    public static ValidationResult invalid(String ruleId, String ruleName, String fieldName,
                                          String fieldValue, String message, long rowNumber,
                                          ValidationSeverity severity) {
        return ValidationResult.builder()
                .valid(false)
                .ruleId(ruleId)
                .ruleName(ruleName)
                .fieldName(fieldName)
                .fieldValue(fieldValue)
                .message(message)
                .rowNumber(rowNumber)
                .severity(severity)
                .build();
    }
} 