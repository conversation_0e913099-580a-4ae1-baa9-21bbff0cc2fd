
1.128.  bms_vgop_revtimes

 Table	bms_vgop_revtimes
 chinese Name	
 instruction	
 purpose	vgop重传序号表
 relatedModules	
 primary Key	datatime,tmpfilename
 foreign Key	
 index	
 partition	
No	Column	NULL	isIncrement	Default	special	Illumination
	Name	Type	Length					
1	datatime	varchar	14	yes	not		无增长	数据日期
2	times	INTEGER	4	yes	not		无增长	重传次数
3	tmpfilename	varchar	50	yes	not		无增长	临时文件名
4	optime	varchar	14	yes	not		无增长	操作时间


 bossprovince

 Table	bossprovince
 chinese Name	
 instruction	
 purpose	boss机构代码与省份id对应关系
 relatedModules	
 primary Key	bossid
 foreign Key	
 index	
 partition	
No	Column	NULL	isIncrement	Default	special	Illumination
	Name	Type	Length					
1	bossid	char	4	yes	not		无增长	4位BOSS机构编码(BOSS省份编码可取该字段前三位)
2	Provinceid	smallint		yes	not		无增长	省份ID

 vgop_channel

 Table	vgop_channel
 chinese Name	
 instruction	
 purpose	vgop用户开户媒介表
 relatedModules	
 primary Key	
 foreign Key	
 index	
 partition	
No	Column	NULL	isIncrement	Default	special	Illumination
	Name	Type	Length					
1	channelcode	varchar	2	yes	not		无增长	开户渠道：
‘0’:客服网站
’1’:短信
’2’：手机网站
’3’：用户网站（PC）
’4’：客户端
’5’：一致性稽核
’6’:BOSS正向(订购关系同步)
’7’：割接
’8’: 批量开户
’9’: 第三方平台
2	channelname	varchar	50	yes	not		无增长	渠道名称

 vgop_shutdown

 Table	vgop_shutdown
 chinese Name	
 instruction	
 purpose	vgop副号停机标识表
 relatedModules	
 primary Key	
 foreign Key	
 index	
 partition	
No	Column	NULL	isIncrement	Default	special	Illumination
	Name	Type	Length					
1	shutdown	varchar	1	yes	not		无增长	副号码停机标识：
’0’: 正常
’1’: 停机
’2’: 销号（空号）
‘4’:实体副号码销号异常（用户取消实体副号码平台操作失败）
‘5’：使用不恰当，限制使用（限制接收短信、呼叫）
‘6’：续订失败，限制使用（限制接收短信、呼叫）
'7':app、网站、短信渠道下用户不可见
'8'：省boss删除数据，用户补录后重新发送开户消息
2	shutdownname	varchar	50	yes	not		无增长	标识名称

vgop_mcntype

 Table	vgop_mcntype
 chinese Name	
 instruction	
 purpose	vgop用户类型表
 relatedModules	
 primary Key	
 foreign Key	
 index	
 partition	
No	Column	NULL	isIncrement	Default	special	Illumination
	Name	Type	Length					
1	mcntype	varchar	1	yes	not		无增长	用户类型：
‘0’：普通个人用户;
2	mcntypename	varchar	50	yes	not		无增长	类型名称

table	mcn_user_major
chinese name	用户主号码信息表
instruction	
purpose	用户主号码信息表
relatedModules	业务受理子系统,网站,和多号-核心子系统,和多号-appserver,统计分析
primary key	phonenumber
foreign key	
index	
partition	
NO	字段名	类型	长度	NULL	自增	默认值	属性特点	说明
1	phonenumber	char	16	yes	not		空	
用户真实手机号码（主号码）
2	phonestate	char	1	yes	not		空	
主号码业务状态：
’0’为正常；
’1’为暂停；
3	phoneimsi	char	16	yes	not		空	
主号码的IMSI号码
4	phoneimei	char	15	yes	not		空	
主号码的IMEI号（手机串号）
5	locationid	char	5	yes	not		空	
主号码归属区号（不带0）
6	provinceid	smallint		yes	not		空	
主号码省ID
7	openingtime	Char	14	yes	not		空	
开户时间
8	Optime	Char	14	yes	not		空	
操作时间
说明：稽核使用
9	sex	char	1	yes	not		空	
网站用户头像性别：
’0’男
’1’女
10	Voicenumber1	smallint		yes	not		空	
情景语音编号1；
当用户副号码设置为‘关机’时，用户选择的情景语音编号值
11	Voicenumber2	smallint		yes	not		空	
情景语音编号2；
当用户副号码设置为 ‘限制语音呼入’时，用户选择的情景语音编号值
12	portInflag	char	1	yes	not		空	
是否是携入用户
‘0’或空 ：非携入
‘1’：联通携入
‘2’：电信携入


table	bossprovince
chinese name	boss机构代码与省份id对应关系
instruction	
purpose	boss机构代码与省份id对应关系
relatedModules	网站、和多号-核心子系统,业务受理子系统
primary key	bossid
foreign key	
index	
partition	
NO	字段名	类型	长度	NULL	自增	默认值	属性特点	说明
1	bossid	char	4	yes	not		空	
4位BOSS机构编码(BOSS省份编码可取该字段前三位)
2	Provinceid	smallint		yes	not		空	
省份ID


table	mcn_user_minor
chinese name	用户副号码信息表
instruction	
purpose	用户副号码信息表，保存用户副号码信息以及主副号码绑定关系信息
relatedModules	业务受理子系统,网站,和多号-核心子系统,和多号-appserver
primary key	mcnnumber
foreign key	
index	mcnimsi,phonenumber
partition	
NO	字段名	类型	长度	NULL	自增	默认值	属性特点	说明
1	mcnnumber	char	16	yes	not		空	
副号号码
2	phonenumber	char	16	yes	not		空	
主号号码
3	business	Char	1	yes	not		空	
副号码业务状态：
’0’为正常
’1’为预开户（对用户统一显示为申请中）
’2’申请中（boss返回超时）
’3’取消中（boss返回超时）
4	shutdown	char	1	yes	not		空	
副号码停机标识：
’0’: 正常
’1’: 停机
’2’: 销号（空号）
‘4’: 实体副号码销号异常（用户取消实体副号码平台操作失败）
‘5’：使用不恰当，限制使用（限制接收短信、呼叫）
‘6’：续订失败，限制使用（限制接收短信、呼叫）
  '7'   :   广东权益包对用户不可见
‘8’:省boss删除数据，用户补录后重新发送开户消息
‘9’半停，限制呼出/上行短信
5	mcnimsi	char	16	yes	not		空	
副号码的IMSI号（实体副号码和虚拟副号，虚拟副号码IMSI由SC接口开户时写入）
6	mcnlocationid	char	5	yes	not		空	
副号码归属区号（不带0）
7	numstate	char	7	yes	not		空	
副号功能状态，形如‘1000000’，其中：
第一位表示逻辑开关机状态：
     ’0’为逻辑开机（托管）
 ’1’为逻辑关机和取消托管
’2’（逻辑开机）限制语音呼入
’3’（逻辑开机）限制短、彩信接收
’4’（逻辑开机）同时限制语音呼入和短、彩信接收
默认为’0’；
第二位表示个人语音黑白名单开启状态（该功能暂时不用，不提供用户设置入口，开户时值默认为0）：
     ’0’为黑白名单均不开启
     ’1’为黑名单开启
     ’2’为白名单开启
     暂为’0’；
第三位个人短/彩信黑白名单开启状态（该功能暂时不用，不提供用户设置入口，开户时值默认为0）：
     ’0’为黑白名单均不开启状态
     ’1’为黑名单开启
     ’2’为白名单开启
 暂为’0’；
第四位表示副号关机来电提醒开关：
’0’：关闭
’1’：打开
默认为’1’；
其它位保留，暂为0
8	mcnnature	Char	1	yes	not		空	
副号码类型：
’0’: 虚拟副号码;
’1’: 实体副号码;
9	mcnnum	smallint		yes	not		空	
副号码序号
个人用户（支持3个副号）：1~3；
10	channel	char	1	yes	not		空	
副号码开户渠道：
‘0’:客服网站
’1’:短信
’2’：手机网站
’3’：用户网站（PC）
’4’：客户端
’5’： 一致性稽核
’6’:BOSS正向(订购关系同步)
’7’：割接
’8’: 批量开户
’9’: 第三方平台
11	anothername	char	20	yes	not		空	
副号码别名
12	openingtime	Char	14	yes	not		空	
开户时间
13	Optime	Char	14	yes	not		空	
操作时间
说明：稽核使用
14	mcimsitime	Char	14	yes	not		空	
实体副号码IMSI号码获取时间(开户或定时)
15	atitime	Char	14	yes	not		空	
发送ati消息查询HLR中副号码状态的时间
16	mcnstate	Char	2	yes	not		空	
实体副号码状态（只用于MAP信令操作）：
‘0’：取消托管状态；
‘1’：托管状态；
默认值‘0’；
17	mcntype	char	2	yes	not		空	
用户类别：
‘0’：普通个人用户;
18	Begintime	Char	14	yes	not		空	
套餐生效时间
19	Endtime	Char	14	yes	not		空	
套餐失效时间
20	ServID	char	10	yes	not		空	
业务代码：
201000：免费1月
202000：免费2月
203000：免费3月
204000：免费4月
205000：免费5月
206000：免费6月
207000：免费7月
208000：免费8月
209000	：免费9月
210000：免费10月
211000：免费11月
212000：免费12月
200000：免功能费
500000：免费包月用于微信支付
203001：副号3月套餐
206001：副号6月套餐
212001：副号12月套餐
100000:包月
21	defaultop	char	2	yes	not		空	
到期默认操作：
‘0’：取消套餐（正向）；
‘1’：续订套餐（反向）；
‘2’：转月套餐（反向）；
默认值‘0’；
22	bosstype1	char	2	yes	not		空	
销户BOSS操作类型： （改套餐、月底到期取消、默认转月套餐时判断）
‘0’：不用到BOSS销户
‘1’：需要到BOSS销户
默认值‘1’；
23	bosstype2	char	2	yes	not		空	
开户BOSS操作类型：（续订、默认转月套餐、改套餐时判断）
‘0’：不用到BOSS开户
‘1’：需要到BOSS开户
默认值‘1’；
24	srvEndFlag	Char	1	yes	not		空	
套餐到期用户是否已扫描插入mcn_expuser表：
NULL ：否；
‘0’：否；
‘1’：是；
每月一号由smp接口根据mcn_expuser表数据将该字段置为为‘0’.
25	oCyId	Char	5	yes	not		空	
主叫文本彩印ID，根据ID在彩印列表mcn_cyTextList（用户自主设置）或彩印包表mcn_cyBox（平台提供）索引彩印内容，ID按照下述规则编号：
首字符‘0’:彩印包表内彩印包ID；
首字符非‘0’：客户设置的彩印内容的彩印ID，其中‘10000’为用户未设置彩印时的默认彩印。
未开通彩印或开通后用户未设置彩印内容时填空；
26	tCyId	Char	5	yes	not		空	
被叫文本彩印ID，根据ID在彩印列表mcn_cyTextList（用户自主设置）或彩印包表mcn_cyBox（平台提供）索引彩印内容，ID按照下述规则编号：
首字符‘0’:彩印包表内彩印包ID；
首字符非‘0’：客户设置的彩印内容的彩印ID，其中‘10000’为用户未设置彩印时的默认彩印。
未开通彩印或开通后用户未设置彩印内容时填空；
27	Updatetime	char	8	yes	not		空	
副号码位置更新操作日期（年月日）；
说明：用户开户或批量位置更新
28	Updatestate	char	1	yes	not		空	
副号码位置更新结果： 
1：位置更新成功； 
2:位置更新失败
说明：用户开户或批量位置更新
29	activityid	char	20	yes	not		空	
活动编号
30	takeStartTime	Char	14	yes	not		空	
实体副号码托管开始时间：
14位YYYYMMDDHHMMSS格式
31	truenamestate	char	1	yes	not		空	
实名认证的状态
‘0’:待验证
‘2’:验证成功
‘3’:验证失败
32	Bossflag	Char	1	yes	not		空	
是否是boss主动开户
1：是boss主动开户
33	interNumFlag	Char	1	yes	not		空	
国际版用户标志：
‘0’或 null: 非国际版用户；
‘1’：国际版用户
34	voltetype	Char	1	yes	not		空	
副号码volte状态
‘0’或 null: 非volte用户；

1：副号为volte用户
35	transHLY	Char	1	yes	not		空	
用户逻辑关机或设置不接电话情况下是否转接到和留言平台：
‘0’：不转接
‘1’：转接
默认为‘0’，不转接
36	endpaytime	Char	14	yes	not		空	
第三方支付到期时间
37	taketype	char	1	yes	not		空	
用户托管托管过程中做过的信令
	‘0’：未托管
	‘1’：location update
	‘2’：registerSS
	‘3’：location update + registers
38	forwardnum	char	24	yes	not		空	
前传号码
仅在interNumFlag=‘1’时有效，代表家乡号的实际接续号码
39	Countrycode	char	4	yes	not		空	
主号码归属国家码
仅在interNumFlag=‘1’时有效
40	autorenew	Char	1	yes	not		空	
自动续费标识
1：开通微信自动续费协议
41	payactivityid	char	20	yes	not		空	
第三方支付活动id，通过第三方支付续费后更新该字段。
42	reservetype	char	1	yes	not		空	
副号码停机保号标识：
 “0”：正常；
 “1”：停机保号；   
 “2”：未支付；
43	reserveexptime	char	14	yes	not		空	
停机保号到期时间
44	harassflag	char	1	yes	not	0	空	
和多号智能防骚扰标识：
 “0”：关闭；
 “1”：打开；
   默认值“0”，关闭
45	callLimit	char	1	yes	not	0	空	
副号码是否为打电话高频副号码
‘0’或 空： 正常， 
 ‘1’：限制高频副号发起呼叫、发送短信（允许接听呼叫、接收短信）
46	egroupflag	char	1	yes	not		无增长	
企业增值版标记
1：是企业增值版用户；
‘0’或者空：非企业增值版用户
47	egroupid	char	40	yes	not		空	
企业增值版用户对应的企业ID，由大写字母和数字构成
48	record	char	1	yes	not		空	
录音标识


table多表头	mcn_oplog
chinese name	sc接口用户操作成功记录表
instruction	
purpose	sc接口用户操作成功记录表，用户业务办理、号码管理操作
relatedModules	业务受理子系统,网站,和多号-appserver
primary key	streamnumber
foreign key	
index	phonenumber+Locationid
partition	
NO	字段名	类型	长度	NULL	自增	默认值	属性特点	说明
1	streamnumber	serial		not	yes		空	
流水帐号
2	phonenumber	char	16	yes	not		空	
手机真实号码
3	optype	char	2	yes	not		空	
针对副号的操作类型:
’s’申请副号；
’z’注销副号；
‘am’申请第一个副号时，添加主号码
‘dm’删除最后一个副号时，删除主号码
‘xd’用户续订或变更套餐
’kj’设置副号为开机状态；
’gj’设置副号为关机状态；
’bv’设置副号开启个人语音黑名单状态；
’wv’设置副号开启个人语音白名单状态；
’gv’设置副号关闭个人语音黑、白名单状态
’bs’设置副号开启个人短信黑名单状态；
’ws’设置副号开启个人短信白名单状态；
’gs’设置副号关闭个人短信黑、白名单状态
针对黑名单和白名单的操作：
’ab’增加黑名单；
’db’删除黑名单；
’aw’增加白名单；
’dw’删除白名单；
’lv’设置副号限制语音呼入
’ls’设置限制短彩信接收
’sl’设置来电提醒
’ql’取消来电提醒
’gh’主号码过户
’hh’主号换号
’xh’主号销户（删除所有副号）
’tj’主号停机
’fj’主号复机
’sn’设置副号码别名
’ds’设置虚拟副号定时开关机
’dg’取消虚拟副号定时开关机
’ss’设置性别
‘tn’用户选定副号下发实名认证短信
‘TS’实名认证成功
‘TF’实名认证失败
‘TP’实名认证已提交人工审核
‘TE’：实名认证超出时间限制
‘f’虚拟副号开户boss返回失败
‘bl’虚拟副号实名制boss补录成功
‘bf’虚拟副号实名制boss补录失败
‘uv’:实体副号升级为volte
‘dv’:实体副号降级为2、3G
‘ib’:收到家乡号绑定请求
‘iq’：收到家乡号取消请求
‘is’:家乡号用户开户成功
‘if’:家乡号开户boss返回实名信息不一致
‘iz’:家乡号销户成功
‘zt’:副号暂停
‘hf’:副号恢复
‘sd’：副号锁定
   'op’：停机保号办理成功
   'tr'：停机保号第三方恢复成功
   'xr'：停机保号包月恢复成功
   'ns':支付后移未支付状态

4	optime	char	14	yes	not		空	
操作时间
5	qmcnnumber	char	16	yes	not		空	
被修改的副号码
（仅修改副号时填写）；被修改的默认副号码（仅修改默认副号的序号时填写）
6	hmcnnumber	char	16	yes	not		空	
修改后的副号码或新申请的副号码、或注销的副号码、开关机状态设置的副号码、黑白名单开启设置的副号码、或黑、白名单操作对应的副号码、修改默认副号后当前生效的默认副号码
7	blacknumber	char	16	yes	not		空	
增加或删除的黑名单号码
8	whitenumber	char	16	yes	not		空	
增加或删除的白名单号码
9	qphonenumber	char	16	yes	not		空	
过户或换号前的主号码
10	opmanner	smallint		yes	not		空	
操作方式:
‘0’:客服网站
’1’:短信
’2’：手机网站
’3’：用户网站（PC）
’4’：客户端
’5’： 一致性稽核 
’6’：BOSS正向，订购关系同步
’7 ’：定时开销户
‘8’:批量开户
’9’：第三方平台
‘10’在线公司实名认证
‘11’：一线客服
‘12’：第三方渠道换号
11	Locationid	char	10	yes	not		空	
用户归属地区号（不带0）
12	Provinceid	smallint		yes	not		空	
用户省ID
13	mcnnature	char	1	yes	not		空	
副号码类型：
‘0‘: 虚拟副号码;
‘1‘: 实体副号码;
14	ServID	char	10	yes	not		空	
业务代码：
201000：免费1月
202000：免费2月
203000：免费3月
204000：免费4月
205000：免费5月
206000：免费6月
207000：免费7月
208000：免费8月
209000	：免费9月
210000：免费10月
211000：免费11月
212000：免费12月
200000：免功能费
500000：免费包月用于微信支付
203001：副号3月套餐
206001：副号6月套餐
212001：副号12月套餐
100000:包月
15	activityid	char	20	yes	not		空	
活动编号
16	AdSource	char	10	yes	not		空	
广告来源
17	Media	Char	10	yes	not		空	
媒体,网站电子渠道标识
18	processflag	Char	1	yes	not		空	
实名认证新老流程标识
‘0’或空：执行老流程在线实名
‘1’：执行实名认证简化流程
19	tailtype	char	1	yes	not		空	
尾号标示：
0：非尾号选号
1：尾号选号
2：新鲜号
3：尾号新鲜号


table	mcn_sec_major
chinese name	
instruction	
purpose	用户主、副卡号码信息
relatedModules	业务受理子系统,和多号-核心子系统,和多号-appserver
primary key	phonenumber
foreign key	
index	mcnnumber,mcnImsi
partition	
NO	字段名	类型	长度	NULL	自增	默认值	属性特点	说明
1	phonenumber	char	16	yes	not		空	
用户主卡号码
2	mcnnumber	Char	16	yes	not		空	
用户副卡号码
3	mcnImsi	Char	20	yes	not		空	
副卡IMSI号
4	businessState	char	1	yes	not		空	
业务状态：
’0’为正常；
’1’为暂停；
5	Numstate	char	7	yes	not		空	
副卡功能状态，形如‘1000000’，其中：
第一位表示逻辑开关机状态：
     ’0’为逻辑开机
 ’1’为逻辑关机
默认为’0’（暂不使用）；
其它位保留，暂为0
6	Locationid	Char	5	yes	not		空	
主号码归属区号（不带0）
7	BossProvinceid	smallint		yes	not		空	
主号码省ID，BOSS数据格式，例如北京省ID为100
8	openingtime	Char	14	yes	not		空	
开户时间
9	flag	char	1	yes	not		空	
屏蔽用户标识，
默认为空
“s” 代表屏蔽号码

table	mcn_contralog
chinese name	正常话单表
instruction	
purpose	正常话单表
relatedModules	网站,和多号-核心子系统,和多号-appserver
primary key	streamNumber
foreign key	
index	CallBeginTime,callType,callingPartyNumber,CallBeginTime,callType,calledPartyNumber
partition	
NO	字段名	类型	长度	NULL	自增	默认值	属性特点	说明
1	streamNumber	Serial		not	yes		空	
话单流水号
2	serviceKey	Integer		yes	not		空	
业务键
3	callType	CHAR	1	yes	not		空	
呼叫类型
0:主叫
1:被叫
4	chargeType	CHAR	1	yes	not		空	
计费类型
0本地电话
1省内长途
2省际长途
5	roamFlag	CHAR	1	yes	not		空	
漫游标志
0:不漫游
1：省内漫游
2:省际漫游
6	callingPartyNumber	Char	16	yes	not		空	
主叫话单：表示副号的主号码；
被叫话单:表示主叫号码；
7	calledPartyNumber	Char	24	yes	not		空	
主叫话单：表示被叫号码；
被叫话单：表示副号的主号码
8	mcnnumber	char	16	yes	not		空	
本次呼叫的副号码
9	roamAreaNumber	CHAR	6	yes	not		空	
副号主号码的漫游地区号（不带0）
10	MSCAddress	CHAR	16	yes	not		空	
触发智能业务的MSC编号
11	callReferenceNum	CHAR	16	yes	not		空	
呼叫参考号码
12	MSInfoVLRNumber	CHAR	20	yes	not		空	
副号主号码的vlr号；
13	MSInfoLAI	CHAR	10	yes	not		空	
用户的LAI
14	MSInfoCellId	CHAR	14	yes	not		空	
小区号码
15	MSInfoIMSI	CHAR	16	yes	not		空	
副号的IMSI号码
16	CallBeginTime	CHAR	14	yes	not		空	
通话开始时间
17	CallEndTime	CHAR	14	yes	not		空	
通话结束时间
18	CallDuration	Integer		yes	not		空	
通话时长
19	chargepartyindicat	CHAR	1	yes	not		空	
计费方
1:对主叫计费
2:对被叫计费
20	extbearercapabilit	CHAR	27	yes	not		空	
承载能力
21	TeleServiceCode	CHAR	10	yes	not		空	
使用的基本电信业务
22	BearerServiceCode	CHAR	10	yes	not		空	
使用的基本承载业务
23	SCFID	CHAR	10	yes	not		空	
用户所在SCP的GT地址
24	CallingvAreaNumber	CHAR	6	yes	not		空	
主叫拜访地区号（不带0）
25	CalledvAreaNumber	CHAR	6	yes	not		空	
主叫话单中：为副号的归属地区号（不带0）；
被叫话单中：为副号主号码的漫游地区号
26	CallingHlrNumber	CHAR	6	yes	not		空	
主叫归属地区号（不带0）
27	CalledHlrNumber	CHAR	6	yes	not		空	
主叫话单中：为被叫号码的归属地区号（不带0）；
被叫话单中：为副号真实主号码的归属地区号（不带0）；
28	callingpartyscateg	Smallint		yes	not		空	
主叫用户类别
29	RedirectingPartyID	CHAR	16	yes	not		空	
改向用户号码
30	Redirectioninfor	CHAR	4	yes	not		空	
前转信息
31	SubScriberstate	Smallint		yes	not		空	
用户状态
32	Cause	CHAR	5	yes	not		空	
释放呼叫的原因
33	Reason	char	2	yes	not		空	
呼叫未接通原因:
‘0’:呼叫接通;
‘1’：非和多号用户;
‘2’：主号码业务状态异常；
‘3’：副号码业务状态异常；
‘4’：副号码停机；
‘5’：副号码设置关机或呼入限制；
‘6’：主叫流程，用户拨打方式错误；
‘7’：主叫流程，被叫号码在禁拨号码表；
‘8’：被叫流程，主叫号码在公共黑名单；
‘9’：被叫流程，主叫号码在用户黑名单；
‘10’：用户忙；
‘11’：用户无应答。
‘12’：转接到和留言
‘13’: 或路由选择失败；
‘14’: 当前虚拟副号码状态为停机保号；
   '15’: 和多号个人版主、被叫流程下，
             和多号业务下发connect后主叫放弃；
 ‘16’: 非国内手机号进行主叫，呼叫被拦截；
 ‘17’: 限制和多号个人版高频副号发起呼叫（允许接听呼叫）
34	business	Char	1	yes	not		空	
副号码业务状态：
’0’为正常
’1’为预开户（对用户统一显示为申请中）
’2’申请中（boss返回超时）
’3’取消中（boss返回超时）
35	shutdown	char	1	yes	not		空	
副号码停机标识：
’0’: 正常
’1’: 停机
’2’: 销号（空号）
‘4’: 实体副号码销号异常（用户取消实体副号码平台操作失败）
‘5’：使用不恰当，限制使用（限制接收短信、呼叫）
‘6’：续订失败，限制使用（限制接收短信、呼叫）
‘8’:省boss删除数据，用户补录后重新发送开户消息
36	numstate	char	7	yes	not		空	
副号功能状态，形如‘1000000’，其中：
第一位表示逻辑开关机状态：
     ’0’为逻辑开机（托管）
 ’1’为逻辑关机和取消托管
’2’（逻辑开机）限制语音呼入
’3’（逻辑开机）限制短、彩信接收
’4’（逻辑开机）同时限制语音呼入和短、彩信接收
默认为’0’；
第二位表示个人语音黑白名单开启状态（该功能暂时不用，不提供用户设置入口，开户时值默认为0）：
     ’0’为黑白名单均不开启
     ’1’为黑名单开启
     ’2’为白名单开启
     暂为’0’；
第三位个人短/彩信黑白名单开启状态（该功能暂时不用，不提供用户设置入口，开户时值默认为0）：
     ’0’为黑白名单均不开启状态
     ’1’为黑名单开启
     ’2’为白名单开启
 暂为’0’；
第四位表示副号关机来电提醒开关：
’0’：关闭
’1’：打开
默认为’1’；
其它位保留，暂为0
37	displayNumber	char	16	yes	not		空	
本次呼叫的来显号码
38	HostName	char	40	yes	not		空	
本次呼叫触发主机对应的IP和主机名
39	egroupflag	char	1	yes	not	0	空	
企业增值版用户标识
’0’：非企业增值版用户
’1’：是企业增值版用户
默认为’0’；


table	mcn_smslog
chinese name	
instruction	
purpose	使用副号收发短信、接收彩信话单
relatedModules	网站、和多号-核心子系统
primary key	streamnumber
foreign key	
index	phonenumber,mcnnumber,sendorreceNum,chargetype,mcnnumber,optime
partition	
NO	字段名	类型	长度	NULL	自增	默认值	属性特点	说明
1	streamnumber	serial		not	yes		空	
流水帐号
2	smsSeqNo	Char	20	yes	not		空	
短信消息会话号
3	Msgid	Varchar	50	yes	not		空	
消息流水号
4	chargetype	smallint		yes	not		空	
话单类型：
0：使用副号发短信话单；
1:使用副号收短信话单；
2：使用副号码接收彩信话单；
3:非业务用户使用业务接入码发送短信；
5	phonenumber	char	16	yes	not		空	
主号码，不带’86’；
6	mcnnumber	char	16	yes	not		空	
副号码，不带’86’；
7	sendorreceNum	char	24	yes	not		空	
对端号码：
使用副号发短信时为被叫号码；
使用副号收短信时为主叫号码；
8	orginalnum	char	24	yes	not		空	
副号收短信、彩信流程中原始被叫号码：
未经处理的SMSC发来的被叫号码，即副号码的IMSI号
9	optime	char	14	yes	not		空	
收发短、彩信的时间
10	msgResult	char	2	yes	not		空	
转发状态：
‘0’:使用副号发短、彩信或接收短信时，SCP转发失败；
‘1’: 使用副号发短、彩信或接收短信时，SCP转发成功；
‘2’: 使用副号发短信或接收短信时，根据公共短信黑名单拦截短信；
‘3’: 使用副号发短信或接收短信时，根据公共短信白名单拦截短信；
‘4’: 使用副号发短信或接收短信时，根据个人短信黑名单拦截短信；
‘5’: 使用副号发短信或接收短信时，根据个人短信白名单拦截短信；
 ‘6’：使用副号码发送、接收短信或接收彩信时主号码处于暂停状态，短、彩信丢弃
‘7’：使用副号码发送、接收短信或接收彩信时副号码处于停机状态，短、彩信丢弃；
‘8’：非业务用户使用业务接入码发送短信拦截；
‘9’：使用不存在的副号码序号发送短信；
‘10’：接收短信、彩信副号码处于“（开机）限制接收短彩信状态”。
‘11’：发送短信或接收短彩信时，副号码处于“关机”状态，短彩信丢弃；
‘12’：unisms返回submit响应超时；
‘13’：归属省与主号码不同的虚拟副号码发送网外短信，短信被拦截；
‘14’：禁止副号码向非手机号码发送短信；
‘15’：禁止副号码接收非手机号码；
‘16’: 虚拟副号码接收爱流量短信被拦截。
‘17’：非移动号码接收彩信失败。
‘20’：不支持向端口号码（例如106581234）发送短信。
‘21’：副号码向短信中心下发短信等等响应超时；
‘22’：副号码向短信中心下发短信收到tcap响应；
‘23’：副号码是发短信高频号码；
‘24’: 当前虚拟副号码状态为停机保号；
‘25’：副号码向短信中心下发长短信，部分分片发送失败
‘26’：平台操作失败
‘27’：限制和多号个人版高频副号发送短信
‘28’：往需要检查副号码在网时长端口号下行短信，副号码在网时长不满足要求；
‘29’：和多号副号码shutdown处于半停状态，限制副号码上行短信
11	failReason	Char	10	yes	not		空	
短信发送失败原因（彩信发送失败不填）：
通过短信网关下发失败取值：
‘1’：消息结构错误；
‘4’：消息长度错误；
‘8’：流量控制；
‘9’：此网关不负责服务此计费号码；
‘10’：src_id错误；
‘11’：msg_src错误；
‘12’：Fee_terminal_Id错误；
‘13’：Dest_terminal_Id错误；
‘102’：有效时间格式错误；
‘103’：定时发送时间格式错误；
‘106’：Registered_Delivery错误（非0、1、3的值）
‘114’：短信内容安全检测未通过；
‘200’： 发送MT到smsAgent失败（a、smsAgent没有启动，b、smsAgent队列文件满，c、smsAgent连接数满）；

二.通过飞信平台下发：
‘400’：请求参数错误；
‘402’：IP不在白名单；
‘403’：无访问权限；
‘406’：含有敏感词；
‘409’：超出接口请求频率上限；
‘500’：服务器异常错误；
彩信发送失败原因：
“0”： 平台到彩信中心提取彩信附件失败；
“01”： 发送彩信链接push消息等待unisms返回超时；
1 消息结构错2 命令字错误3 消息序列号重复4 消息长度错5 资费代码错6 超过最大信息长7 业务代码错8 流量控制错9 本网关不负责此计费号码10 Src_ID错11 Msg_src错12 计费地址错13 目的地址错14～49 扩展51 尚未建立连接52 尚未成功登录53 发送消息失败54 超时未接收到响应消息55 等待状态报告超时
60 保留61 有效时间已经过期62 定时发送时间已经过期63 不能识别的FeeType64 发送服务源地址鉴权失败65 发送服务目的地址鉴权失败66 接收服务源地址鉴权失败67 接收服务目的地址鉴权失败68 用户鉴权失败69 此用户为黑名单用户70 网络断连或目的设备关闭接口71 超过最大节点数72 找不到路由73 等待应答超时74 送SCP失败75 送SCP鉴权等待应答超时76 信息安全鉴权失败77 超过最大Submit提交数78 SPID 为空79 业务类型为空80 CPCode错误81 发送接收接口重复82 循环路由83 超过接收侧短消息MTU84 送DSMP重发失败85 DSMP系统忙重发86 DSMP系统忙，且缓存满重发87 DSMP流控重发88 等DSMP应答超时重发89 保留201～240 SCP错误码202 非神州行预付费用户203 数据库操作失败206 移动用户帐户数据异常208 用户余额不足210 超过最高欠费额215 重复发送消息序列号msgid相同的计费请求消息218 SCP互联失败222 未登记的SP232 月消费超额241～255 SMC错误码(其中250~255为康为短消息中心错误应答映射，根据smpp3.4协议)241 未定义250 消息队列满
12	business	Char	1	yes	not		空	
副号表的business，若未查询填空
13	Shutdown	Cahr	1	yes	not		空	
副号表的shutdown字段，若未查询填空。
14	Numstate	Char	10	yes	not		空	
副号表的numstate字段，若未查询填空
15	dcs	integer		yes	not	8	空	
短信内容编码格式
16	totalnum	integer		yes	not	0	空	
长短信标识：一条长短信被拆分的总条数
17	referNum	integer		yes	not	0	空	
长短信标识
18	logtype	char	1	yes	not	0	空	
话单出处：
0：scp；
1：IT短信；
2：SCP-和多号用户发短信到端口号，短信内容命中退订放行关键词。
19	egroupflag	char	1	yes	not	0	无增长	
企业增值版标记
1：是企业增值版用户；
‘0’或者空：非企业增值版用户
20	hostName	varchar	128	yes	not		空	
短信涉及的多个业务模块主机名以“;”分隔，如"MAP主机名;IT短信主机名;submit主机名"
