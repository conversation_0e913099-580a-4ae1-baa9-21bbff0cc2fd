package com.vgop.service.service;

import com.vgop.service.config.VgopProperties;
import com.vgop.service.dto.TaskExecutionRequest;
import com.vgop.service.dto.TaskExecutionResponse;
import com.vgop.service.entity.TaskConfig;
import com.vgop.service.exception.TaskExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/**
 * 任务重试和异常恢复服务
 * 提供任务执行失败时的重试机制和异常恢复功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class TaskRetryService {
    
    private final VgopTaskScheduler taskScheduler;
    private final VgopProperties vgopProperties;
    
    /**
     * 任务重试历史记录
     */
    private final ConcurrentMap<String, RetryRecord> retryRecords = new ConcurrentHashMap<>();
    
    public TaskRetryService(@Qualifier("vgopTaskExecutor") VgopTaskScheduler taskScheduler,
                           VgopProperties vgopProperties) {
        this.taskScheduler = taskScheduler;
        this.vgopProperties = vgopProperties;
    }
    
    /**
     * 带重试机制的日统计任务执行
     */
    @Retryable(
        value = {TaskExecutionException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 30000, multiplier = 2) // 30秒延迟，每次翻倍
    )
    public CompletableFuture<TaskExecutionResponse> executeWithRetry(TaskExecutionRequest request) {
        
        String taskId = request.getActionInstanceId();
        String interfaceId = request.getInterfaceId();
        
        // 记录重试信息
        RetryRecord record = retryRecords.computeIfAbsent(taskId, k -> new RetryRecord());
        record.incrementAttempts();
        record.setLastAttemptTime(LocalDateTime.now());
        
        log.info("执行任务（尝试第{}次）: {}, 接口ID: {}", record.getAttempts(), taskId, interfaceId);
        
        try {
            CompletableFuture<TaskExecutionResponse> future = taskScheduler.executeTask(request);
            
            // 等待任务完成并检查结果
            TaskExecutionResponse response = future.get(
                vgopProperties.getScheduler().getTaskTimeoutMinutes(), TimeUnit.MINUTES);
            
            if (response.getSuccess() == null || !response.getSuccess()) {
                throw new TaskExecutionException("任务执行失败: " + response.getErrorMessage());
            }
            
            // 成功时清理重试记录
            retryRecords.remove(taskId);
            log.info("任务执行成功: {}", taskId);
            
            return CompletableFuture.completedFuture(response);
            
        } catch (Exception e) {
            log.error("任务执行失败（尝试第{}次）: {}, 错误: {}", 
                record.getAttempts(), taskId, e.getMessage());
            throw new TaskExecutionException("任务执行失败", e);
        }
    }
    
    /**
     * 重试失败后的恢复机制
     */
    @Recover
    public CompletableFuture<TaskExecutionResponse> recoverFromFailure(
            TaskExecutionException ex, TaskExecutionRequest request) {
        
        String taskId = request.getActionInstanceId();
        RetryRecord record = retryRecords.get(taskId);
        
        log.error("任务执行最终失败，已达到最大重试次数: {}, 总尝试次数: {}", 
            taskId, record != null ? record.getAttempts() : 0);
        
        // 创建失败响应
        TaskExecutionResponse response = TaskExecutionResponse.builder()
            .taskId(taskId)
            .status(TaskConfig.TaskStatus.FAILED)
            .startTime(record != null ? record.getFirstAttemptTime() : LocalDateTime.now())
            .endTime(LocalDateTime.now())
            .success(false)
            .errorMessage(ex.getMessage())
            .message(String.format("任务执行失败，已重试%d次", 
                record != null ? record.getAttempts() : 0))
            .build();
        
        // 发送告警通知
        sendFailureAlert(request, response, record);
        
        // 清理重试记录
        retryRecords.remove(taskId);
        
        return CompletableFuture.completedFuture(response);
    }
    
    /**
     * 手动重试指定任务
     */
    public CompletableFuture<TaskExecutionResponse> manualRetry(String taskId, TaskExecutionRequest request) {
        log.info("手动重试任务: {}, 接口ID: {}", taskId, request.getInterfaceId());
        
        // 清除之前的重试记录，重新开始
        retryRecords.remove(taskId);
        
        return executeWithRetry(request);
    }
    
    /**
     * 获取任务重试状态
     */
    public RetryStatus getRetryStatus(String taskId) {
        RetryRecord record = retryRecords.get(taskId);
        
        if (record == null) {
            return RetryStatus.builder()
                .taskId(taskId)
                .hasRetryHistory(false)
                .build();
        }
        
        return RetryStatus.builder()
            .taskId(taskId)
            .hasRetryHistory(true)
            .attempts(record.getAttempts())
            .firstAttemptTime(record.getFirstAttemptTime())
            .lastAttemptTime(record.getLastAttemptTime())
            .maxAttempts(3) // 从配置或注解获取
            .build();
    }
    
    /**
     * 清理过期的重试记录
     */
    public void cleanupExpiredRetryRecords() {
        LocalDateTime cutoff = LocalDateTime.now().minusHours(24); // 保留24小时
        
        retryRecords.entrySet().removeIf(entry -> {
            RetryRecord record = entry.getValue();
            return record.getLastAttemptTime().isBefore(cutoff);
        });
        
        log.info("清理过期重试记录完成，剩余记录数: {}", retryRecords.size());
    }
    
    /**
     * 发送失败告警
     */
    private void sendFailureAlert(TaskExecutionRequest request, 
                                 TaskExecutionResponse response, 
                                 RetryRecord record) {
        try {
            // TODO: 实现告警发送逻辑（邮件、短信、钉钉等）
            log.warn("任务失败告警 - 任务ID: {}, 失败原因: {}, 重试次数: {}", 
                request.getActionInstanceId(), 
                response.getErrorMessage(),
                record != null ? record.getAttempts() : 0);
            
        } catch (Exception e) {
            log.error("发送失败告警时出错", e);
        }
    }
    
    /**
     * 重试记录
     */
    public static class RetryRecord {
        private int attempts = 0;
        private LocalDateTime firstAttemptTime;
        private LocalDateTime lastAttemptTime;
        
        public void incrementAttempts() {
            attempts++;
            if (firstAttemptTime == null) {
                firstAttemptTime = LocalDateTime.now();
            }
        }
        
        public int getAttempts() { return attempts; }
        public LocalDateTime getFirstAttemptTime() { return firstAttemptTime; }
        public LocalDateTime getLastAttemptTime() { return lastAttemptTime; }
        public void setLastAttemptTime(LocalDateTime time) { this.lastAttemptTime = time; }
    }
    
    /**
     * 重试状态
     */
    public static class RetryStatus {
        private String taskId;
        private boolean hasRetryHistory;
        private int attempts;
        private int maxAttempts;
        private LocalDateTime firstAttemptTime;
        private LocalDateTime lastAttemptTime;
        
        public static RetryStatusBuilder builder() {
            return new RetryStatusBuilder();
        }
        
        // Builder pattern implementation
        public static class RetryStatusBuilder {
            private RetryStatus status = new RetryStatus();
            
            public RetryStatusBuilder taskId(String taskId) {
                status.taskId = taskId;
                return this;
            }
            
            public RetryStatusBuilder hasRetryHistory(boolean hasRetryHistory) {
                status.hasRetryHistory = hasRetryHistory;
                return this;
            }
            
            public RetryStatusBuilder attempts(int attempts) {
                status.attempts = attempts;
                return this;
            }
            
            public RetryStatusBuilder maxAttempts(int maxAttempts) {
                status.maxAttempts = maxAttempts;
                return this;
            }
            
            public RetryStatusBuilder firstAttemptTime(LocalDateTime firstAttemptTime) {
                status.firstAttemptTime = firstAttemptTime;
                return this;
            }
            
            public RetryStatusBuilder lastAttemptTime(LocalDateTime lastAttemptTime) {
                status.lastAttemptTime = lastAttemptTime;
                return this;
            }
            
            public RetryStatus build() {
                return status;
            }
        }
        
        // Getters
        public String getTaskId() { return taskId; }
        public boolean isHasRetryHistory() { return hasRetryHistory; }
        public int getAttempts() { return attempts; }
        public int getMaxAttempts() { return maxAttempts; }
        public LocalDateTime getFirstAttemptTime() { return firstAttemptTime; }
        public LocalDateTime getLastAttemptTime() { return lastAttemptTime; }
    }
} 