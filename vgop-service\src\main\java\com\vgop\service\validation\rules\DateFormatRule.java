package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * 日期格式校验规则
 * 检查日期格式是否正确
 */
@Component
public class DateFormatRule extends AbstractValidationRule {
    
    /**
     * 日期格式
     */
    private final String dateFormat;
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     * @param dateFormat 日期格式
     */
    public DateFormatRule(String fieldName, String dateFormat) {
        super(
                "common." + fieldName + ".date",
                "日期格式校验",
                "检查日期格式是否正确: " + dateFormat,
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.FORMAT
        );
        this.dateFormat = dateFormat;
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public DateFormatRule() {
        this("date", "yyyyMMdd");
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 如果值为空，则跳过校验（由RequiredFieldRule处理）
        if (StringUtils.isBlank(value)) {
            return createValidResult();
        }
        
        // 获取字段元数据
        FieldMetadata metadata = context.getFieldMetadata(getFieldName());
        
        // 确定日期格式
        String formatToUse = dateFormat;
        
        // 根据字段类型确定日期格式
        if (metadata != null && metadata.getFieldType() != null) {
            switch (metadata.getFieldType()) {
                case DATE:
                    formatToUse = "yyyyMMdd";
                    break;
                case TIMESTAMP:
                    formatToUse = "yyyyMMddHHmmss";
                    break;
                default:
                    break;
            }
        }
        
        // 校验日期格式
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(formatToUse);
            sdf.setLenient(false); // 不使用宽松模式，严格校验日期
            sdf.parse(value);
            return createValidResult();
        } catch (ParseException e) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 的值 (%s) 不是有效的日期格式 (%s)", 
                            getFieldName(), value, formatToUse),
                    context
            );
        }
    }
} 