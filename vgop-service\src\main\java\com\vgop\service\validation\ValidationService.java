package com.vgop.service.validation;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 校验服务接口
 */
public interface ValidationService {
    
    /**
     * 校验文件
     * 
     * @param interfaceName 接口名称
     * @param filePath 文件路径
     * @param dataDate 数据日期
     * @return 校验摘要
     */
    ValidationSummary validateFile(String interfaceName, String filePath, String dataDate);
    
    /**
     * 校验文件（自定义配置）
     * 
     * @param interfaceName 接口名称
     * @param filePath 文件路径
     * @param dataDate 数据日期
     * @param delimiter 分隔符
     * @param headerLine 是否有表头行
     * @param fieldNames 字段名列表
     * @return 校验摘要
     */
    ValidationSummary validateFile(String interfaceName, String filePath, String dataDate,
                                 String delimiter, boolean headerLine, List<String> fieldNames);
    
    /**
     * 校验文件并导出校验通过的数据
     * 
     * @param interfaceName 接口名称
     * @param filePath 输入文件路径
     * @param outputFilePath 输出文件路径（校验通过的数据）
     * @param dataDate 数据日期
     * @return 校验摘要
     */
    ValidationSummary validateFileWithIndexAndExport(String interfaceName, String filePath, 
                                                    String outputFilePath, String dataDate);
    
    /**
     * 注册接口元数据
     * 
     * @param interfaceName 接口名称
     * @param metadata 字段元数据映射
     */
    void registerMetadata(String interfaceName, Map<String, FieldMetadata> metadata);
    
    /**
     * 注册接口字段元数据
     * 
     * @param interfaceName 接口名称
     * @param fieldName 字段名
     * @param metadata 字段元数据
     */
    void registerFieldMetadata(String interfaceName, String fieldName, FieldMetadata metadata);
    
    /**
     * 获取接口的字段元数据
     * 
     * @param interfaceName 接口名称
     * @return 字段元数据映射
     */
    Map<String, FieldMetadata> getMetadata(String interfaceName);
    
    /**
     * 获取所有接口名称
     * 
     * @return 接口名称集合
     */
    Set<String> getAllInterfaces();
    
    /**
     * 获取接口的所有字段名
     * 
     * @param interfaceName 接口名称
     * @return 字段名集合
     */
    Set<String> getFieldsByInterface(String interfaceName);
    
    /**
     * 获取校验规则
     * 
     * @param interfaceName 接口名称
     * @param fieldName 字段名（可选）
     * @return 校验规则列表
     */
    List<ValidationRule> getRules(String interfaceName, String fieldName);
    
    /**
     * 获取校验摘要
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 校验摘要
     */
    ValidationSummary getSummary(String interfaceName, String fileName, String dataDate);
    
    /**
     * 获取错误记录
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @param page 页码
     * @param size 每页大小
     * @return 错误记录列表
     */
    List<ErrorRecord> getErrors(String interfaceName, String fileName, String dataDate, int page, int size);
    
    /**
     * 获取错误记录总数
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 错误记录总数
     */
    long getErrorCount(String interfaceName, String fileName, String dataDate);
} 