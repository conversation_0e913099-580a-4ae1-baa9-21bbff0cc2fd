package com.vgop.service.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务执行记录实体类
 * 对应数据库表：vgop_task_execution
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
public class TaskExecution {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 任务类型
     * daily - 日常任务
     * monthly - 月度任务
     */
    private String taskType;
    
    /**
     * 数据日期（格式：yyyyMMdd）
     */
    private String dataDate;
    
    /**
     * 执行阶段
     * export - 数据导出
     * transfer - 文件传输
     * validate - 数据校验
     */
    private String stage;
    
    /**
     * 版本号
     */
    private Integer revision;
    
    /**
     * 执行状态
     * PENDING - 待执行
     * RUNNING - 执行中
     * SUCCESS - 执行成功
     * FAILED - 执行失败
     */
    private String status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行时长（毫秒）
     */
    private Long duration;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 处理数据量
     */
    private Integer processedCount;
    
    /**
     * 成功数量
     */
    private Integer successCount;
    
    /**
     * 失败数量
     */
    private Integer failCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 附加信息（JSON格式）
     */
    private String extraInfo;
} 