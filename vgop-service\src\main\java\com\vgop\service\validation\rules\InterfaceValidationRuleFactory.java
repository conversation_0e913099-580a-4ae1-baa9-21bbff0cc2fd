package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;

/**
 * 接口校验规则工厂
 * 用于创建接口专用的校验规则
 */
public class InterfaceValidationRuleFactory {
    
    /**
     * 创建接口长度规则
     */
    public static class InterfaceLengthRule extends AbstractValidationRule {
        private final Integer minLength;
        private final Integer maxLength;
        
        public InterfaceLengthRule(String interfaceName, String fieldName, Integer minLength, Integer maxLength) {
            super(
                    interfaceName + "." + fieldName + ".length",
                    "长度校验",
                    "检查字段长度是否符合要求",
                    fieldName,
                    ValidationSeverity.ERROR,
                    ValidationRuleType.LENGTH
            );
            this.minLength = minLength;
            this.maxLength = maxLength;
        }
        
        @Override
        public ValidationResult validate(String value, ValidationContext context) {
            if (value == null || value.trim().isEmpty()) {
                return createValidResult();
            }
            
            int length = value.length();
            
            if (minLength != null && length < minLength) {
                return createInvalidResult(
                        value,
                        String.format("字段 %s 长度不能少于 %d 位，当前: %d", getFieldName(), minLength, length),
                        context
                );
            }
            
            if (maxLength != null && length > maxLength) {
                return createInvalidResult(
                        value,
                        String.format("字段 %s 长度不能超过 %d 位，当前: %d", getFieldName(), maxLength, length),
                        context
                );
            }
            
            return createValidResult();
        }
    }
    
    /**
     * 创建接口格式规则
     */
    public static class InterfaceFormatRule extends AbstractValidationRule {
        private final String pattern;
        
        public InterfaceFormatRule(String interfaceName, String fieldName, String pattern) {
            super(
                    interfaceName + "." + fieldName + ".format",
                    "格式校验",
                    "检查字段格式是否符合要求",
                    fieldName,
                    ValidationSeverity.ERROR,
                    ValidationRuleType.FORMAT
            );
            this.pattern = pattern;
        }
        
        @Override
        public ValidationResult validate(String value, ValidationContext context) {
            if (value == null || value.trim().isEmpty()) {
                return createValidResult();
            }
            
            if (pattern != null && !value.matches(pattern)) {
                return createInvalidResult(
                        value,
                        String.format("字段 %s 格式不正确: %s", getFieldName(), value),
                        context
                );
            }
            
            return createValidResult();
        }
    }
    
    /**
     * 创建接口枚举规则
     */
    public static class InterfaceEnumValueRule extends AbstractValidationRule {
        private final String enumValues;
        
        public InterfaceEnumValueRule(String interfaceName, String fieldName, String enumValues) {
            super(
                    interfaceName + "." + fieldName + ".enum",
                    "枚举值校验",
                    "检查字段值是否在允许的枚举值范围内",
                    fieldName,
                    ValidationSeverity.ERROR,
                    ValidationRuleType.ENUM
            );
            this.enumValues = enumValues;
        }
        
        @Override
        public ValidationResult validate(String value, ValidationContext context) {
            if (value == null || value.trim().isEmpty()) {
                return createValidResult();
            }
            
            if (enumValues != null) {
                String[] allowedValues = enumValues.split(",");
                boolean found = false;
                for (String allowedValue : allowedValues) {
                    if (allowedValue.trim().equals(value.trim())) {
                        found = true;
                        break;
                    }
                }
                
                if (!found) {
                    return createInvalidResult(
                            value,
                            String.format("字段 %s 值不在允许范围内: %s，允许值: %s", getFieldName(), value, enumValues),
                            context
                    );
                }
            }
            
            return createValidResult();
        }
    }
    
    /**
     * 创建接口IMSI规则
     */
    public static class InterfaceImsiRule extends AbstractValidationRule {
        
        public InterfaceImsiRule(String interfaceName, String fieldName) {
            super(
                    interfaceName + "." + fieldName + ".imsi",
                    "IMSI格式校验",
                    "检查IMSI格式是否正确",
                    fieldName,
                    ValidationSeverity.ERROR,
                    ValidationRuleType.FORMAT
            );
        }
        
        @Override
        public ValidationResult validate(String value, ValidationContext context) {
            if (value == null || value.trim().isEmpty()) {
                return createValidResult();
            }
            
            if (!value.matches("^\\d{15,16}$")) {
                return createInvalidResult(
                        value,
                        String.format("IMSI格式不正确: %s，应为15-16位数字", value),
                        context
                );
            }
            
            return createValidResult();
        }
    }
    
    /**
     * 创建接口IMEI规则
     */
    public static class InterfaceImeiRule extends AbstractValidationRule {
        
        public InterfaceImeiRule(String interfaceName, String fieldName) {
            super(
                    interfaceName + "." + fieldName + ".imei",
                    "IMEI格式校验",
                    "检查IMEI格式是否正确",
                    fieldName,
                    ValidationSeverity.ERROR,
                    ValidationRuleType.FORMAT
            );
        }
        
        @Override
        public ValidationResult validate(String value, ValidationContext context) {
            if (value == null || value.trim().isEmpty()) {
                return createValidResult();
            }
            
            if (!value.matches("^\\d{14,15}$")) {
                return createInvalidResult(
                        value,
                        String.format("IMEI格式不正确: %s，应为14-15位数字", value),
                        context
                );
            }
            
            return createValidResult();
        }
    }
    
    /**
     * 创建接口日期格式规则
     */
    public static class InterfaceDateFormatRule extends AbstractValidationRule {
        private final String format;
        
        public InterfaceDateFormatRule(String interfaceName, String fieldName, String format) {
            super(
                    interfaceName + "." + fieldName + ".date",
                    "日期格式校验",
                    "检查日期格式是否正确",
                    fieldName,
                    ValidationSeverity.ERROR,
                    ValidationRuleType.DATE_FORMAT
            );
            this.format = format;
        }
        
        @Override
        public ValidationResult validate(String value, ValidationContext context) {
            if (value == null || value.trim().isEmpty()) {
                return createValidResult();
            }
            
            if ("yyyyMMddHHmmss".equals(format)) {
                if (!value.matches("^\\d{14}$")) {
                    return createInvalidResult(
                            value,
                            String.format("日期格式不正确: %s，应为YYYYMMDDHH24MISS格式", value),
                            context
                    );
                }
            }
            
            return createValidResult();
        }
    }
    
    /**
     * 创建接口范围规则
     */
    public static class InterfaceRangeRule extends AbstractValidationRule {
        private final String minValue;
        private final String maxValue;
        
        public InterfaceRangeRule(String interfaceName, String fieldName, String minValue, String maxValue) {
            super(
                    interfaceName + "." + fieldName + ".range",
                    "范围校验",
                    "检查字段值是否在指定范围内",
                    fieldName,
                    ValidationSeverity.ERROR,
                    ValidationRuleType.RANGE
            );
            this.minValue = minValue;
            this.maxValue = maxValue;
        }
        
        @Override
        public ValidationResult validate(String value, ValidationContext context) {
            if (value == null || value.trim().isEmpty()) {
                return createValidResult();
            }
            
            // 这里可以根据需要实现范围校验逻辑
            return createValidResult();
        }
    }
} 