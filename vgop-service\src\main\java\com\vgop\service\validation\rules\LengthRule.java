package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 长度校验规则
 * 检查字段长度是否在指定范围内
 */
@Component
public class LengthRule extends AbstractValidationRule {
    
    /**
     * 最小长度
     */
    private final Integer minLength;
    
    /**
     * 最大长度
     */
    private final Integer maxLength;
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     * @param minLength 最小长度
     * @param maxLength 最大长度
     */
    public LengthRule(String fieldName, Integer minLength, Integer maxLength) {
        super(
                "common." + fieldName + ".length",
                "长度校验",
                String.format("检查字段长度是否在范围 [%d, %d] 内", 
                        minLength != null ? minLength : 0, 
                        maxLength != null ? maxLength : Integer.MAX_VALUE),
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.FORMAT
        );
        this.minLength = minLength;
        this.maxLength = maxLength;
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public LengthRule() {
        this("*", null, null);
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 如果值为空，则跳过校验（由RequiredFieldRule处理）
        if (StringUtils.isBlank(value)) {
            return createValidResult();
        }
        
        // 获取字段元数据
        FieldMetadata metadata = context.getFieldMetadata(getFieldName());
        
        // 确定最小长度和最大长度
        Integer minLengthToUse = minLength;
        Integer maxLengthToUse = maxLength;
        
        if (metadata != null) {
            if (metadata.getMinLength() != null) {
                minLengthToUse = metadata.getMinLength();
            }
            if (metadata.getMaxLength() != null) {
                maxLengthToUse = metadata.getMaxLength();
            }
        }
        
        // 校验长度
        int length = value.length();
        
        if (minLengthToUse != null && length < minLengthToUse) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 长度 (%d) 小于最小长度 (%d)", 
                            getFieldName(), length, minLengthToUse),
                    context
            );
        }
        
        if (maxLengthToUse != null && length > maxLengthToUse) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 长度 (%d) 大于最大长度 (%d)", 
                            getFieldName(), length, maxLengthToUse),
                    context
            );
        }
        
        return createValidResult();
    }
} 