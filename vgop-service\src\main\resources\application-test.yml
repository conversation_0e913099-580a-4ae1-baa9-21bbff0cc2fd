# 测试环境配置
spring:
  # 数据源配置 - 测试环境使用GBase数据库
  datasource:
    # 主数据源 - GBase数据库 (BMS库)
    primary:
      driver-class-name: com.gbase.jdbc.Driver
      url: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
      username: ismp
      password: 1qaz@WSX
      hikari:
        pool-name: VgopTestGBasePool
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        connection-test-query: SELECT 1 FROM systables WHERE tabid = 1

# 测试环境日志级别
logging:
  level:
    com.vgop.service: INFO
    org.springframework.jdbc: INFO
    com.zaxxer.hikari: INFO

# 测试环境应用配置
app:
  # 测试环境路径配置
  base-path:
    export-root: "/test/VGOPdata/datafile/"
    log-root: "/test/vgop/log/"
    backup-root: "/test/vgop/backup/"
  
  # 测试环境SFTP配置
  sftp:
    host: "**************"  # 测试环境SFTP服务器
    port: 22
    username: "bms_test"
    password: "test@2024"
    remote-base-path: "/home/<USER>/VGOPdata/datafile/"
    connection-timeout: 20000
    retry-times: 2
    max-pool-size: 3
    
  # 测试环境文件处理配置
  file-processing:
    max-rows-per-file: 500000  # 测试环境50万行
    
  # 测试环境告警配置
  alert:
    storage:
      enable-database: true
      enable-file: true  # 测试环境同时保存文件便于调试
      alert-file-path: "/test/vgop/alerts/"
      
    # 测试环境缩短上传时限，便于测试
    upload-deadline:
      daily: 10    # 测试环境10点前
      monthly: 10  # 测试环境每月1日10点前

# 测试环境actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,beans,env 

# VGOP数据校验规则配置 - 测试环境（与开发环境保持一致）
validation:
  interfaces:
    'VGOP1-R2-10-24202':
      delimiter: "|"
      headerLine: false
      fields:
        # 字段配置参考规则文档：.cursor/rules/vgop-script-to-config-generator.mdc
        # 字段清单已在规则文档中详细说明，测试环境与开发环境保持一致 