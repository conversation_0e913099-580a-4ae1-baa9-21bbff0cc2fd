#!/bin/bash
#
# 脚本功能: 为 vgop_channel 维度表生成并导入数据。
#           这是一个维度表，通常只需要一次性填充。
# 使用方法: ./generate_vgop_channel_data.sh <数据库名>
# 示例:     ./generate_vgop_channel_data.sh bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 1 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <数据库名>"
    exit 1
fi

DB_NAME=$1
DELIMITER="|"
DATA_FILE=$(mktemp "vgop_channel_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中填充 'vgop_channel' 表。"
echo "临时数据文件: ${DATA_FILE}"

# --- 生成固定的维度数据 ---
# channelcode | channelname
cat > "${DATA_FILE}" << EOL
0|客服网站
1|短信
2|手机网站
3|用户网站（PC）
4|客户端
5|一致性稽核
6|BOSS正向(订购关系同步)
7|割接
8|批量开户
9|第三方平台
EOL

echo "数据生成完毕."
cat "${DATA_FILE}"
echo "准备将数据导入数据库..."

# --- 准备 LOAD 命令 ---
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO vgop_channel (channelcode, channelname);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
else
  echo "数据导入失败！请检查 dbaccess 的输出和日志。"
  echo "临时数据文件 '${DATA_FILE}' 已保留。"
  exit 1
fi

echo "脚本执行完毕。" 