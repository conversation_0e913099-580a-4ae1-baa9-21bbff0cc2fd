package com.vgop.service.service;

import java.io.IOException;

/**
 * 数据文件校验服务
 */
public interface DataValidationService {

    /**
     * 校验数据文件，并生成一个净化后的版本.
     *
     * @param originalFilePath 原始文件路径
     * @param interfaceId      接口ID，用于查找校验规则
     * @param dataDate         数据日期
     * @param taskType         任务类型 ("daily" or "monthly")
     * @return 净化后文件的路径
     * @throws IOException 如果文件读写出错
     */
    String validateAndCleanFile(String originalFilePath, String interfaceId, String dataDate, String taskType) throws IOException;
} 