-- 告警信息表
CREATE TABLE IF NOT EXISTS vgop_validation_alerts (
    alert_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    alert_time VARCHAR(14) NOT NULL DEFAULT '',
    interface_name VARCHAR(100) NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    alert_level VARCHAR(20) NOT NULL DEFAULT 'WARNING',
    alert_message VARCHAR(1024) NOT NULL,
    file_name VARCHAR(200),
    line_number BIGINT,
    error_data TEXT,
    field_errors CLOB,
    metric_details CLOB,
    excel_report_path VARCHAR(500),
    status VARCHAR(20) NOT NULL DEFAULT 'NEW',
    handled_by <PERSON><PERSON><PERSON><PERSON>(100),
    handled_time VARCHAR(14)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_alerts_time ON vgop_validation_alerts(alert_time);
CREATE INDEX IF NOT EXISTS idx_alerts_interface ON vgop_validation_alerts(interface_name);
CREATE INDEX IF NOT EXISTS idx_alerts_status ON vgop_validation_alerts(status);

-- 历史统计表
CREATE TABLE IF NOT EXISTS vgop_metrics_history (
    history_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    processing_date DATE NOT NULL,
    interface_name VARCHAR(100) NOT NULL,
    total_records BIGINT NOT NULL DEFAULT 0,
    compliant_records BIGINT NOT NULL DEFAULT 0,
    non_compliant_records BIGINT NOT NULL DEFAULT 0,
    export_file_count INT NOT NULL DEFAULT 0,
    export_status VARCHAR(20) NOT NULL,
    transfer_status VARCHAR(20),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_interface_date (interface_name, processing_date)
);

-- 任务执行日志表
CREATE TABLE IF NOT EXISTS vgop_task_execution_log (
    log_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    task_name VARCHAR(100) NOT NULL,
    task_type VARCHAR(20) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    processed_records BIGINT,
    generated_files INT
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_task_log_task_id ON vgop_task_execution_log(task_id);
CREATE INDEX IF NOT EXISTS idx_task_log_start_time ON vgop_task_execution_log(start_time);

-- 文件备份表
CREATE TABLE IF NOT EXISTS vgop_file_backup (
    backup_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    data_date DATE NOT NULL,
    file_type VARCHAR(20) NOT NULL,
    original_path VARCHAR(500) NOT NULL,
    backup_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    backup_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_recovered BOOLEAN NOT NULL DEFAULT FALSE,
    recovery_date DATE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_backup_interface ON vgop_file_backup(interface_name);
CREATE INDEX IF NOT EXISTS idx_backup_date ON vgop_file_backup(data_date);

-- 任务执行记录表
CREATE TABLE IF NOT EXISTS bms_vgop_task_execution (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_type VARCHAR(20) NOT NULL COMMENT '任务类型：daily/monthly',
    data_date VARCHAR(8) NOT NULL COMMENT '数据日期（yyyyMMdd）',
    stage VARCHAR(20) NOT NULL COMMENT '执行阶段：export/transfer/validate',
    revision INT NOT NULL COMMENT '版本号',
    status VARCHAR(20) NOT NULL COMMENT '状态：PENDING/RUNNING/SUCCESS/FAILED',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration BIGINT COMMENT '执行时长（毫秒）',
    error_message VARCHAR(1000) COMMENT '错误信息',
    processed_count INT COMMENT '处理数据量',
    success_count INT COMMENT '成功数量',
    fail_count INT COMMENT '失败数量',
    extra_info CLOB COMMENT '附加信息（JSON格式）',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_task_execution_date ON bms_vgop_task_execution(data_date);
CREATE INDEX IF NOT EXISTS idx_task_execution_type ON bms_vgop_task_execution(task_type);
CREATE INDEX IF NOT EXISTS idx_task_execution_stage ON bms_vgop_task_execution(stage);
CREATE INDEX IF NOT EXISTS idx_task_execution_status ON bms_vgop_task_execution(status);

-- 插入测试数据（可选）
-- INSERT INTO bms_vgop_revtimes (datatime, times, tmpfilename, optime) 
-- VALUES ('20240115', 0, 'a_10000_20240115_VGOP1-R2.10-24201.unl', '20240115120000');

-- 版本控制表
CREATE TABLE IF NOT EXISTS revision_times (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    revision_date VARCHAR(8) NOT NULL,
    revision_time VARCHAR(6) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_interface_date (interface_name, revision_date)
);

-- 任务执行表
CREATE TABLE IF NOT EXISTS task_executions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(36) NOT NULL,
    task_name VARCHAR(100) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration BIGINT,
    parameters TEXT,
    result TEXT,
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 错误记录表
CREATE TABLE IF NOT EXISTS error_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    data_date VARCHAR(8) NOT NULL,
    row_number BIGINT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    field_value TEXT,
    error_message TEXT NOT NULL,
    rule_id VARCHAR(100) NOT NULL,
    rule_name VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_interface_file_date (interface_name, file_name, data_date),
    INDEX idx_row_number (row_number),
    INDEX idx_field_name (field_name),
    INDEX idx_rule_id (rule_id),
    INDEX idx_severity (severity)
);

-- 校验摘要表
CREATE TABLE IF NOT EXISTS validation_summaries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    data_date VARCHAR(8) NOT NULL,
    total_rows BIGINT NOT NULL,
    error_rows BIGINT NOT NULL,
    total_errors BIGINT NOT NULL,
    error_rate DOUBLE NOT NULL,
    severity_counts TEXT NOT NULL,
    rule_counts TEXT NOT NULL,
    field_counts TEXT NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration BIGINT,
    status VARCHAR(20) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_interface_file_date (interface_name, file_name, data_date),
    INDEX idx_interface_name (interface_name),
    INDEX idx_file_name (file_name),
    INDEX idx_data_date (data_date),
    INDEX idx_start_time (start_time)
);

-- 字段元数据表
CREATE TABLE IF NOT EXISTS field_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    interface_name VARCHAR(100) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    field_type VARCHAR(50) NOT NULL,
    required BOOLEAN DEFAULT FALSE,
    max_length INTEGER,
    min_length INTEGER,
    pattern VARCHAR(255),
    min_value VARCHAR(100),
    max_value VARCHAR(100),
    enum_values TEXT,
    default_value VARCHAR(255),
    field_order INTEGER,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_interface_field (interface_name, field_name),
    INDEX idx_interface_name (interface_name),
    INDEX idx_field_name (field_name)
); 