文档名称	升级报告. [CR200268]
部门名称	和多号产品部	编 写 人	张凯	编写日期	2017/03/23
问题编号	XQ200187	审 核 人		审核日期	
[该文档的命名规则为：升级报告.需求编号.doc，如：升级报告.工程需求记录.浙江工程.20050701.doc。
黑色斜体字以及表格内容是需要填写的项目。
本文档必须至少包含‘装载实施步骤’、‘升级验证’、‘倒回步骤’、‘例行测试’这四个部分。
]
软件概述
升级包对应需求编号：	XQ200187
序号	软件模块	升级前版本	欲升级版本	新增功能描述
1.	运营分析子系统	bms-1.4.7	bms-1.4.8	1.根据一级增值业务综合运营平台接口规范，统计数据，向VGOP侧提供统计结果。
可以进行版本跟踪的软件模块，如sc接口、cn底层、RRS_IVR_HN.slp、www用户网站等。‘新增功能描述’为对此版本软件新增功能的描述。]
升级对现网的影响
软件升级环境
环境信息项目	信息内容	备注
数据库版本号	无	
sc接口升级前版本号	无	
cn底层升级前版本号	无	
rn底层升级前版本号	无	
www版本号	无	
		
		
[填写说明：‘信息内容’填写‘环境信息项目’中要求提供的信息。如果对这些项目没有要求或者不在自己职责范围内的项目，在信息内容里填写无。]

装载实施步骤
装载步骤说明
	此次升级的操作步骤如下：
序号	任务	预计操作时长（分钟）	备注
1.	新建表、存储过程，导入数据	6	
2.	备份并升级Master	3	
3.	备份并升级Executor	3	
4.	重启Master、Executor	2	
[填写说明：对管理库和呼叫库分离的省份，对数据库进行的任务要区分管理库、呼叫库；对cn帐户的升级任务要区分管理CN帐户、呼叫CN帐户；若没有分离，则不必区分管理库和呼叫库等。
一般而言，任务包括：修改管理库表、修改呼叫库表、上传语音文件、往管理库加载语音文件信息、加载管理CN帐户业务逻辑、加载呼叫库存储过程、执行存储过程、加载WWW网站、加载SC接口等。]

详细装载步骤
1.新建表、存储过程，导入数据
本次修改所涉及到的表有：详细请参考数据库设计说明
[填写说明：‘操作对象’填写管理库、呼叫库或者管理库及呼叫库（对呼叫库管理库没有分离的省份填写‘管理库及呼叫库）。‘操作行为’填写新增或者修改或者删除。]
修改步骤如下：
执行代码包中 sql文件夹下的bms_VGOP_dactivemcn.sql和bms_VGOP_banalyse.sql，新建两个存储过程。
执行代码包中sql文件夹下的bms_stat_appdactiveuser.sql和bms_stat_subphone.sql，重建两个存储过程。
执行代码包中sql文件夹下的bms.create_table.sql，新建十张表。
执行代码包中sql文件夹下的bms.alter_table.sql，BMS_STAT_SUBPHONE表新增两个字段。
将代码包中的mcn_service.unl数据文件，导入bms库中的mcn_service表中。
将代码包中的vgop_servtype.unl数据文件，导入bms库中的vgop_servtype表中。
将代码包中的vgop_channel.unl数据文件，导入bms库中的vgop_channel表中。
将代码包中的vgop_shutdown.unl数据文件，导入bms库中的vgop_shutdown表中。
将代码包中的vgop_mcntype.unl数据文件，导入bms库中的vgop_mcntype表中。
将代码包中的vgop_location.unl数据文件，导入bms库中的bms_vgop_location表中。
2.备份Master
以下以 [MASTER] 表示master所在的文件夹
  备份以下一个文件夹中的所有内容：
[MASTER] 的 config文件夹
3.升级Master
将代码包中的master/config文件夹下的 mission1_missionadd.xml文件中的内容，复制到[MASTER]/config/ mission1.xml文件中<project id="p0">下<mission id="M1">元素下，并修改<mission id="M1">中<action seqid="24">的next-action-ids=50。
将代码包中 master/config文件夹下的missionM1b.xml文件中的内容，复制到[MASTER]/config/mission1.xml文件中<project id="p0">元素下。
将代码包中 master/config文件夹下的mission14_VGOP.xml文件，复制至到[MASTER]/config目录下。
将代码包中 master/config文件夹下的mme.moduleadd.xml文件中的内容，复制到[MASTER]/config/ mme.module.xml文件中<modulelist>元素下
4.备份Executor
以下以 [EXECUTOR] 表示executor所在的文件夹
  备份以下三个文件夹中的所有内容：
[EXECUTOR] 的 config文件夹
[EXECUTOR] 的 module文件夹
5.升级Executor
将代码包中 executor/config文件夹下的mme.moduleadd.xml文件中的内容，复制到[EXECUTOR]/config/mme.module.xml文件中<modulelist>元素下
将代码包中sql文件夹下的bms_stat_appdactiveuser.sql和bms_stat_subphone.sql两个文件，分别替换[EXECUTOR]/module下bms_stat_appdactiveuser和bms_stat_subphone目录下的两个sql文件。
将代码包中 executor/module文件夹下 7个文件夹，复制到[EXECUTOR]/module下，注意修改其中shell脚本的可执行属性
6.重启Master、Executor
重启master、executor。
升级验证
请根据测试报告来验证此次升级是否成功，测试报告有：
测试报告文档名	测试模块	备注
		
		
		
		
		
		

倒回步骤
1.删除存储过程，新建表
删除升级时新建的存储过程和表。
2.倒回master
清空master的config文件夹
代之以“备份master”时备份的config文件夹
3.倒回executor
清空executor的config文件夹、module文件夹，
代之以“备份executor”时备份的config文件夹、module文件