# VGOP服务部署配置文件
# 请根据实际服务器环境修改以下配置

# ================================
# Git仓库配置
# ================================
GIT_REPO_URL="ssh://gitlab.e-byte.cn:19222/hdh/website_group/vgop-vli"
GIT_BRANCH="master"

# ================================
# 部署路径配置
# ================================
DEPLOY_DIR="/opt/vgop"
SERVICE_PORT="8080"

# ================================
# Java环境配置
# ================================
# Java安装路径（根据实际情况修改）
JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
# 或者其他可能的Java路径：
# JAVA_HOME="/usr/lib/jvm/java-1.8.0-openjdk"
# JAVA_HOME="/opt/jdk1.8.0_291"

# ================================
# Maven环境配置
# ================================
# Maven安装路径（根据实际情况修改）
MAVEN_HOME="/opt/maven"
# 或者其他可能的Maven路径：
# MAVEN_HOME="/usr/share/maven"
# MAVEN_HOME="/opt/apache-maven-3.8.1"

# ================================
# 运行环境配置
# ================================
# Spring Boot运行环境（dev/test/prod）
PROFILE="prod"

# ================================
# JVM参数配置
# ================================
# 内存配置（根据服务器配置调整）
JVM_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:logs/gc.log"

# ================================
# 数据库配置（如果需要）
# ================================
# 这些配置会通过环境变量传递给应用
# DB_HOST="localhost"
# DB_PORT="3306"
# DB_NAME="vgop"
# DB_USERNAME="vgop_user"
# DB_PASSWORD="your_password"

# ================================
# 其他配置
# ================================
# 备份保留天数
BACKUP_RETENTION_DAYS="7"

# 日志级别
LOG_LEVEL="INFO" 