package com.vgop.service.common;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 监控日志工具类
 * 用于记录结构化日志，便于后续监控和分析
 */
@Slf4j
@Component
public class MonitorLogger {

    private static final Logger taskLogger = LoggerFactory.getLogger("com.vgop.service.scheduler");
    private static final Logger dataExportLogger = LoggerFactory.getLogger("com.vgop.service.service.DataExportService");
    private static final Logger fileTransferLogger = LoggerFactory.getLogger("com.vgop.service.service.FileTransferService");

    private static final String TASK_ID = "taskId";
    private static final String TASK_TYPE = "taskType";
    private static final String TASK_STATUS = "taskStatus";
    private static final String EVENT_TYPE = "eventType";

    /**
     * 记录任务开始日志
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param params 任务参数
     */
    public void logTaskStart(String taskId, String taskName, String taskType, Map<String, Object> params) {
        MDC.put(TASK_ID, taskId);
        MDC.put(TASK_TYPE, taskType);
        MDC.put(TASK_STATUS, "STARTED");
        MDC.put(EVENT_TYPE, "TASK_START");
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "TASK_START");
        logData.put("taskId", taskId);
        logData.put("taskName", taskName);
        logData.put("taskType", taskType);
        logData.put("params", params);
        
        taskLogger.info("Task started: {} (ID: {}, Type: {})", taskName, taskId, taskType);
        
        MDC.clear();
    }
    
    /**
     * 记录任务完成日志
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param status 任务状态
     * @param duration 任务持续时间(毫秒)
     * @param result 任务结果
     */
    public void logTaskComplete(String taskId, String taskName, String status, long duration, Map<String, Object> result) {
        MDC.put(TASK_ID, taskId);
        MDC.put(TASK_TYPE, taskName);
        MDC.put(TASK_STATUS, status);
        MDC.put(EVENT_TYPE, "TASK_COMPLETE");
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "TASK_COMPLETE");
        logData.put("taskId", taskId);
        logData.put("taskName", taskName);
        logData.put("status", status);
        logData.put("durationMs", duration);
        logData.put("result", result);
        
        taskLogger.info("Task completed: {} (ID: {}, Status: {}, Duration: {}ms)", 
                taskName, taskId, status, duration);
        
        MDC.clear();
    }
    
    /**
     * 记录任务失败日志
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param error 错误信息
     * @param duration 任务持续时间(毫秒)
     */
    public void logTaskFailure(String taskId, String taskName, Throwable error, long duration) {
        MDC.put(TASK_ID, taskId);
        MDC.put(TASK_TYPE, taskName);
        MDC.put(TASK_STATUS, "FAILED");
        MDC.put(EVENT_TYPE, "TASK_FAILURE");
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "TASK_FAILURE");
        logData.put("taskId", taskId);
        logData.put("taskName", taskName);
        logData.put("errorMessage", error.getMessage());
        logData.put("errorType", error.getClass().getName());
        logData.put("durationMs", duration);
        
        taskLogger.error("Task failed: {} (ID: {}, Error: {}, Duration: {}ms)", 
                taskName, taskId, error.getMessage(), duration, error);
        
        MDC.clear();
    }
    
    /**
     * 记录数据导出开始日志
     * @param exportId 导出ID
     * @param tableName 表名
     * @param sqlTemplate SQL模板
     */
    public void logExportStart(String exportId, String tableName, String sqlTemplate) {
        MDC.put(TASK_ID, exportId);
        MDC.put(TASK_TYPE, "EXPORT");
        MDC.put(TASK_STATUS, "STARTED");
        MDC.put(EVENT_TYPE, "EXPORT_START");
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "EXPORT_START");
        logData.put("exportId", exportId);
        logData.put("tableName", tableName);
        logData.put("sqlTemplate", sqlTemplate);
        
        dataExportLogger.info("Data export started: {} (ID: {})", tableName, exportId);
        
        MDC.clear();
    }
    
    /**
     * 记录数据导出完成日志
     * @param exportId 导出ID
     * @param tableName 表名
     * @param recordCount 记录数
     * @param fileSize 文件大小
     * @param duration 持续时间(毫秒)
     */
    public void logExportComplete(String exportId, String tableName, int recordCount, long fileSize, long duration) {
        MDC.put(TASK_ID, exportId);
        MDC.put(TASK_TYPE, "EXPORT");
        MDC.put(TASK_STATUS, "COMPLETED");
        MDC.put(EVENT_TYPE, "EXPORT_COMPLETE");
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "EXPORT_COMPLETE");
        logData.put("exportId", exportId);
        logData.put("tableName", tableName);
        logData.put("recordCount", recordCount);
        logData.put("fileSizeBytes", fileSize);
        logData.put("durationMs", duration);
        
        dataExportLogger.info("Data export completed: {} (ID: {}, Records: {}, Size: {} bytes, Duration: {}ms)", 
                tableName, exportId, recordCount, fileSize, duration);
        
        MDC.clear();
    }
    
    /**
     * 记录文件传输开始日志
     * @param transferId 传输ID
     * @param sourceFile 源文件
     * @param targetPath 目标路径
     */
    public void logTransferStart(String transferId, String sourceFile, String targetPath) {
        MDC.put(TASK_ID, transferId);
        MDC.put(TASK_TYPE, "TRANSFER");
        MDC.put(TASK_STATUS, "STARTED");
        MDC.put(EVENT_TYPE, "TRANSFER_START");
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "TRANSFER_START");
        logData.put("transferId", transferId);
        logData.put("sourceFile", sourceFile);
        logData.put("targetPath", targetPath);
        
        fileTransferLogger.info("File transfer started: {} -> {} (ID: {})", 
                sourceFile, targetPath, transferId);
        
        MDC.clear();
    }
    
    /**
     * 记录文件传输完成日志
     * @param transferId 传输ID
     * @param sourceFile 源文件
     * @param targetPath 目标路径
     * @param fileSize 文件大小
     * @param duration 持续时间(毫秒)
     */
    public void logTransferComplete(String transferId, String sourceFile, String targetPath, 
                                   long fileSize, long duration) {
        MDC.put(TASK_ID, transferId);
        MDC.put(TASK_TYPE, "TRANSFER");
        MDC.put(TASK_STATUS, "COMPLETED");
        MDC.put(EVENT_TYPE, "TRANSFER_COMPLETE");
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "TRANSFER_COMPLETE");
        logData.put("transferId", transferId);
        logData.put("sourceFile", sourceFile);
        logData.put("targetPath", targetPath);
        logData.put("fileSizeBytes", fileSize);
        logData.put("durationMs", duration);
        logData.put("transferSpeedKBps", calculateTransferSpeed(fileSize, duration));
        
        fileTransferLogger.info("File transfer completed: {} -> {} (ID: {}, Size: {} bytes, Duration: {}ms, Speed: {} KB/s)", 
                sourceFile, targetPath, transferId, fileSize, duration, calculateTransferSpeed(fileSize, duration));
        
        MDC.clear();
    }
    
    /**
     * 记录文件传输失败日志
     * @param transferId 传输ID
     * @param sourceFile 源文件
     * @param targetPath 目标路径
     * @param error 错误信息
     */
    public void logTransferFailure(String transferId, String sourceFile, String targetPath, Throwable error) {
        MDC.put(TASK_ID, transferId);
        MDC.put(TASK_TYPE, "TRANSFER");
        MDC.put(TASK_STATUS, "FAILED");
        MDC.put(EVENT_TYPE, "TRANSFER_FAILURE");
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "TRANSFER_FAILURE");
        logData.put("transferId", transferId);
        logData.put("sourceFile", sourceFile);
        logData.put("targetPath", targetPath);
        logData.put("errorMessage", error.getMessage());
        logData.put("errorType", error.getClass().getName());
        
        fileTransferLogger.error("File transfer failed: {} -> {} (ID: {}, Error: {})", 
                sourceFile, targetPath, transferId, error.getMessage(), error);
        
        MDC.clear();
    }
    
    /**
     * 生成唯一ID
     * @return 唯一ID
     */
    public String generateId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
    
    /**
     * 计算传输速度（KB/s）
     * @param fileSize 文件大小（字节）
     * @param durationMs 持续时间（毫秒）
     * @return 传输速度（KB/s）
     */
    private double calculateTransferSpeed(long fileSize, long durationMs) {
        if (durationMs <= 0) {
            return 0;
        }
        return (fileSize / 1024.0) / (durationMs / 1000.0);
    }

    /**
     * 记录数据校验开始日志
     *
     * @param interfaceName 接口名称
     * @param fileName     文件名
     * @param dataDate     数据日期
     */
    public void logValidationStart(String interfaceName, String fileName, String dataDate) {
        String taskId = UUID.randomUUID().toString();
        MDC.put(TASK_ID, taskId);
        MDC.put(TASK_TYPE, "VALIDATION");
        MDC.put(TASK_STATUS, "STARTED");
        MDC.put(EVENT_TYPE, "VALIDATION_START");

        log.info("数据校验开始: taskId={}, interfaceName={}, fileName={}, dataDate={}", 
                taskId, interfaceName, fileName, dataDate);

        MDC.clear();
    }

    /**
     * 记录数据校验完成日志
     *
     * @param interfaceName 接口名称
     * @param fileName     文件名
     * @param dataDate     数据日期
     * @param totalRows    总行数
     * @param errorRows    错误行数
     * @param totalErrors  错误总数
     */
    public void logValidationComplete(String interfaceName, String fileName, String dataDate, 
                                    long totalRows, long errorRows, long totalErrors) {
        MDC.put(TASK_TYPE, "VALIDATION");
        MDC.put(TASK_STATUS, "COMPLETED");
        MDC.put(EVENT_TYPE, "VALIDATION_COMPLETE");

        log.info("数据校验完成: interfaceName={}, fileName={}, dataDate={}, totalRows={}, errorRows={}, totalErrors={}", 
                interfaceName, fileName, dataDate, totalRows, errorRows, totalErrors);

        MDC.clear();
    }

    /**
     * 记录数据校验失败日志
     *
     * @param interfaceName 接口名称
     * @param fileName     文件名
     * @param dataDate     数据日期
     * @param errorMsg     错误信息
     */
    public void logValidationFailed(String interfaceName, String fileName, String dataDate, String errorMsg) {
        MDC.put(TASK_TYPE, "VALIDATION");
        MDC.put(TASK_STATUS, "FAILED");
        MDC.put(EVENT_TYPE, "VALIDATION_FAILED");

        log.error("数据校验失败: interfaceName={}, fileName={}, dataDate={}, errorMsg={}", 
                interfaceName, fileName, dataDate, errorMsg);

        MDC.clear();
    }
} 