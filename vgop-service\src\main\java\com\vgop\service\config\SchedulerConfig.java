package com.vgop.service.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

/**
 * 调度配置类
 * 配置Spring Scheduler的线程池和执行策略
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Configuration
@EnableScheduling
public class SchedulerConfig implements SchedulingConfigurer {
    
    private static final int POOL_SIZE = 5;
    
    /**
     * 配置调度器
     * 使用自定义线程池以支持并发任务执行
     */
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        
        scheduler.setPoolSize(POOL_SIZE);
        scheduler.setThreadNamePrefix("vgop-scheduler-");
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setErrorHandler(throwable -> {
            // 记录调度任务执行异常
            throw new RuntimeException("调度任务执行异常", throwable);
        });
        scheduler.initialize();
        
        taskRegistrar.setTaskScheduler(scheduler);
    }
} 