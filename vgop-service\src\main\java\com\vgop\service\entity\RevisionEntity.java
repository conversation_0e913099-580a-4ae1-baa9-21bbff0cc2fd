package com.vgop.service.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 版本控制实体类
 * 对应数据库表 bms_vgop_revtimes
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RevisionEntity {
    
    /**
     * 数据时间 (datatime)
     */
    private String dataTime;
    
    /**
     * 版本次数 (times)
     */
    private Integer times;
    
    /**
     * 临时文件名 (tmpfilename)
     */
    private String tmpFileName;
    
    /**
     * 操作时间 (optime)
     */
    private String opTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 