package com.vgop.service.controller;

import com.vgop.service.common.ApiResponse;
import com.vgop.service.entity.TaskExecution;
import com.vgop.service.scheduler.VgopTaskScheduler;
import com.vgop.service.service.TaskExecutionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 任务控制器
 * 提供任务触发和查询API
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/tasks")
public class TaskController {
    
    private final VgopTaskScheduler taskScheduler;
    private final TaskExecutionService taskExecutionService;
    
    @Autowired
    public TaskController(VgopTaskScheduler taskScheduler, TaskExecutionService taskExecutionService) {
        this.taskScheduler = taskScheduler;
        this.taskExecutionService = taskExecutionService;
    }
    
    /**
     * 手动触发日常任务
     * 
     * @param date 数据日期（格式：yyyy-MM-dd）
     * @return API响应
     */
    @PostMapping("/trigger/daily")
    public ApiResponse<Map<String, Object>> triggerDailyTask(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        String dataDate = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        log.info("手动触发日常任务 - 数据日期: {}", dataDate);
        
        Map<String, Object> result = taskScheduler.triggerDailyTask(dataDate);
        boolean success = (boolean) result.getOrDefault("success", false);
        
        if (success) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.error(500, (String) result.getOrDefault("message", "未知错误"), result);
        }
    }
    
    /**
     * 手动触发月度任务
     * 
     * @param date 数据日期（格式：yyyy-MM-dd）
     * @return API响应
     */
    @PostMapping("/trigger/monthly")
    public ApiResponse<Map<String, Object>> triggerMonthlyTask(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        String dataDate = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        log.info("手动触发月度任务 - 数据日期: {}", dataDate);
        
        Map<String, Object> result = taskScheduler.triggerMonthlyTask(dataDate);
        boolean success = (boolean) result.getOrDefault("success", false);
        
        if (success) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.error(500, (String) result.getOrDefault("message", "未知错误"), result);
        }
    }
    
    /**
     * 查询任务执行记录
     * 
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return API响应
     */
    @GetMapping("/executions")
    public ApiResponse<List<TaskExecution>> getTaskExecutions(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        String start = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String end = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        List<TaskExecution> executions = taskExecutionService.findByDateRange(start, end);
        return ApiResponse.success(executions);
    }
    
    /**
     * 查询失败的任务执行记录
     * 
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return API响应
     */
    @GetMapping("/executions/failed")
    public ApiResponse<List<TaskExecution>> getFailedTaskExecutions(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        String start = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String end = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        List<TaskExecution> executions = taskExecutionService.findFailedExecutions(start, end);
        return ApiResponse.success(executions);
    }
    
    /**
     * 查询正在运行的任务
     * 
     * @return API响应
     */
    @GetMapping("/executions/running")
    public ApiResponse<List<TaskExecution>> getRunningTasks() {
        List<TaskExecution> executions = taskExecutionService.findRunningTasks();
        return ApiResponse.success(executions);
    }
    
    /**
     * 查询任务执行详情
     * 
     * @param id 任务执行ID
     * @return API响应
     */
    @GetMapping("/executions/{id}")
    public ApiResponse<TaskExecution> getTaskExecution(@PathVariable Long id) {
        TaskExecution execution = taskExecutionService.findById(id);
        if (execution == null) {
            return ApiResponse.error(404, "任务执行记录不存在", null);
        }
        return ApiResponse.success(execution);
    }
} 