package com.vgop.service.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 日期字符串处理工具类
 * 用于处理YYYYMMDDHHMMSS格式的日期字符串
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class DateStringUtil {
    
    private static final Logger log = LoggerFactory.getLogger(DateStringUtil.class);
    
    /**
     * 日期时间格式化器（14位：YYYYMMDDHHMMSS）
     */
    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 日期格式化器（8位：YYYYMMDD）
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    /**
     * 获取当前时间字符串（14位格式）
     * 
     * @return YYYYMMDDHHMMSS格式的当前时间
     */
    public static String getCurrentDateTimeString() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }
    
    /**
     * 获取当前日期字符串（8位格式）
     * 
     * @return YYYYMMDD格式的当前日期
     */
    public static String getCurrentDateString() {
        return LocalDateTime.now().format(DATE_FORMATTER);
    }
    
    /**
     * 验证日期时间字符串格式（14位）
     * 
     * @param dateTimeString 待验证的日期时间字符串
     * @return true 如果格式正确
     */
    public static boolean isValidDateTimeString(String dateTimeString) {
        if (StringUtils.isBlank(dateTimeString)) {
            return false;
        }
        
        if (dateTimeString.length() != 14) {
            return false;
        }
        
        if (!dateTimeString.matches("\\d{14}")) {
            return false;
        }
        
        try {
            LocalDateTime.parse(dateTimeString, DATETIME_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            log.debug("日期时间字符串格式无效: {}", dateTimeString);
            return false;
        }
    }
    
    /**
     * 验证日期字符串格式（8位）
     * 
     * @param dateString 待验证的日期字符串
     * @return true 如果格式正确
     */
    public static boolean isValidDateString(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return false;
        }
        
        if (dateString.length() != 8) {
            return false;
        }
        
        if (!dateString.matches("\\d{8}")) {
            return false;
        }
        
        try {
            LocalDateTime.parse(dateString + "000000", DATETIME_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            log.debug("日期字符串格式无效: {}", dateString);
            return false;
        }
    }
    
    /**
     * 从日期时间字符串中提取日期部分
     * 
     * @param dateTimeString 日期时间字符串（14位）
     * @return 日期字符串（8位），如果输入无效则返回null
     */
    public static String extractDatePart(String dateTimeString) {
        if (StringUtils.isBlank(dateTimeString) || dateTimeString.length() < 8) {
            return null;
        }
        return dateTimeString.substring(0, 8);
    }
    
    /**
     * 比较两个日期时间字符串
     * 
     * @param dateTime1 第一个日期时间字符串
     * @param dateTime2 第二个日期时间字符串
     * @return 比较结果：负数表示dateTime1早于dateTime2，0表示相等，正数表示dateTime1晚于dateTime2
     */
    public static int compareDateTimeStrings(String dateTime1, String dateTime2) {
        if (StringUtils.isBlank(dateTime1) && StringUtils.isBlank(dateTime2)) {
            return 0;
        }
        if (StringUtils.isBlank(dateTime1)) {
            return -1;
        }
        if (StringUtils.isBlank(dateTime2)) {
            return 1;
        }
        return dateTime1.compareTo(dateTime2);
    }
    
    /**
     * 生成日期范围的开始时间（某日的00:00:00）
     * 
     * @param dateString 日期字符串（8位：YYYYMMDD）
     * @return 开始时间字符串（14位：YYYYMMDD000000）
     */
    public static String getDateRangeStart(String dateString) {
        if (!isValidDateString(dateString)) {
            throw new IllegalArgumentException("无效的日期字符串: " + dateString);
        }
        return dateString + "000000";
    }
    
    /**
     * 生成日期范围的结束时间（某日的23:59:59）
     * 
     * @param dateString 日期字符串（8位：YYYYMMDD）
     * @return 结束时间字符串（14位：YYYYMMDD235959）
     */
    public static String getDateRangeEnd(String dateString) {
        if (!isValidDateString(dateString)) {
            throw new IllegalArgumentException("无效的日期字符串: " + dateString);
        }
        return dateString + "235959";
    }
    
    /**
     * 将LocalDateTime转换为日期时间字符串
     * 
     * @param dateTime LocalDateTime对象
     * @return 日期时间字符串（14位：YYYYMMDDHHMMSS）
     */
    public static String fromLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATETIME_FORMATTER);
    }
    
    /**
     * 将日期时间字符串转换为LocalDateTime
     * 
     * @param dateTimeString 日期时间字符串（14位：YYYYMMDDHHMMSS）
     * @return LocalDateTime对象，如果转换失败则返回null
     */
    public static LocalDateTime toLocalDateTime(String dateTimeString) {
        if (!isValidDateTimeString(dateTimeString)) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeString, DATETIME_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("日期时间字符串转换失败: {}", dateTimeString, e);
            return null;
        }
    }
    
    /**
     * 格式化日期时间字符串为可读格式
     * 
     * @param dateTimeString 日期时间字符串（14位：YYYYMMDDHHMMSS）
     * @return 格式化后的字符串（yyyy-MM-dd HH:mm:ss）
     */
    public static String formatForDisplay(String dateTimeString) {
        LocalDateTime dateTime = toLocalDateTime(dateTimeString);
        if (dateTime == null) {
            return dateTimeString; // 如果转换失败，返回原字符串
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
} 