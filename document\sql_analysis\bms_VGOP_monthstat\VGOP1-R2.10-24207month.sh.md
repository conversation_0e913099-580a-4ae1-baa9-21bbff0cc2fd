# 脚本 `VGOP1-R2.10-24207month.sh` SQL逻辑分析

-   **脚本用途**: 抽取截止到**上月末**的、业务状态为**销户('X')**的所有"第二号码"用户信息。
-   **数据来源**: `mcn_sec_major`, `mcn_sec_major2`。
-   **核心逻辑**:
    -   `UNION ALL` 合并 `mcn_sec_major` 和 `mcn_sec_major2` 两张表的数据。
    -   `openingtime < '${endtime}'`: 筛选出执行月之前开户的所有记录。
    -   `businessState = 'X'`: 核心过滤条件，只选择业务状态为 'X' 的记录。根据业务通常的定义，'X'很可能代表"销户"。
-   **输出内容**: 全量的、已销户的"第二号码"用户信息。
-   **说明**: 这是一个**全量快照**脚本，专门用于在月底归档所有已销户的第二号码用户数据。 