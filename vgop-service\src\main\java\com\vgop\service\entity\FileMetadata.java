package com.vgop.service.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 文件元数据实体类
 * 对应.verf校验文件的内容
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileMetadata {
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 行数
     */
    private Long lineCount;
    
    /**
     * 数据日期
     */
    private String dataDate;
    
    /**
     * 生成时间
     */
    private String generateTime;
    
    /**
     * 文件完整路径
     */
    private String filePath;
    
    /**
     * 文件类型前缀 (a_, i_)
     */
    private String filePrefix;
    
    /**
     * 版本号
     */
    private String revision;
    
    /**
     * 分片序号
     */
    private String partNumber;
    
    /**
     * 脚本标识符
     */
    private String scriptId;
    
    /**
     * 校验文件路径
     */
    private String verfFilePath;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 状态
     */
    private FileStatus status;
    
    public enum FileStatus {
        GENERATING("生成中"),
        COMPLETED("已完成"),
        FAILED("失败");
        
        private final String description;
        
        FileStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 