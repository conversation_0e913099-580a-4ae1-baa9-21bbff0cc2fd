#!/bin/bash
#
# 脚本功能: 为 bossprovince 维度表生成并导入数据.
#           这是一个维度表，通常只需要一次性填充。
# 使用方法: ./generate_bossprovince_data.sh <数据库名>
# 示例:     ./generate_bossprovince_data.sh bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 1 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <数据库名>"
    echo "示例: $0 bms"
    exit 1
fi

DB_NAME=$1
DELIMITER="|"
DATA_FILE=$(mktemp "bossprovince_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中填充 'bossprovince' 表。"
echo "临时数据文件: ${DATA_FILE}"

# --- 生成固定的维度数据 ---
# provinceid | bossid | provincename
cat > "${DATA_FILE}" << EOL
100|10001|北京
200|20001|广东
210|21001|上海
220|22001|天津
230|23001|重庆
240|24001|河北
250|25001|山西
270|27001|辽宁
280|28001|吉林
290|29001|黑龙江
310|31001|江苏
350|35001|浙江
370|37001|安徽
390|39001|福建
430|43001|江西
450|45001|山东
470|47001|河南
490|49001|湖北
530|53001|湖南
550|55001|海南
590|59001|四川
730|73001|贵州
750|75001|云南
770|77001|陕西
790|79001|甘肃
810|81001|青海
830|83001|内蒙古
850|85001|广西
870|87001|西藏
890|89001|宁夏
910|91001|新疆
EOL

echo "数据生成完毕."
cat "${DATA_FILE}"
echo "准备将数据导入数据库..."

# --- 准备 LOAD 命令 ---
# 假设表 bossprovince 的列顺序是: provinceid, bossid, provincename
# 注意: 实际列名和顺序需要根据您的表结构进行调整
COLUMNS="provinceid, bossid, provincename"
# 先删除旧数据，再插入新数据，确保幂等性
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO bossprovince (provinceid, bossid, provincename);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
  echo "临时数据文件 '${DATA_FILE}' 已被删除。"
else
  echo "数据导入失败！请检查 dbaccess 的输出和日志。"
  echo "临时数据文件 '${DATA_FILE}' 已保留，以供调试。"
  exit 1
fi

echo "脚本执行完毕。" 