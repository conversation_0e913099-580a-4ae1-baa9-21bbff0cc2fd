-- 删除现有表的脚本
-- 注意：这将删除所有数据，请谨慎使用

-- 删除索引（如果存在）
DROP INDEX IF EXISTS idx_alerts_time;
DROP INDEX IF EXISTS idx_alerts_interface;
DROP INDEX IF EXISTS idx_alerts_status;

DROP INDEX IF EXISTS idx_task_log_task_id;
DROP INDEX IF EXISTS idx_task_log_start_time;

DROP INDEX IF EXISTS idx_backup_interface;
DROP INDEX IF EXISTS idx_backup_date;

DROP INDEX IF EXISTS idx_task_execution_date;
DROP INDEX IF EXISTS idx_task_execution_type;
DROP INDEX IF EXISTS idx_task_execution_stage;
DROP INDEX IF EXISTS idx_task_execution_status;

DROP INDEX IF EXISTS idx_interface_file_date;
DROP INDEX IF EXISTS idx_row_number;
DROP INDEX IF EXISTS idx_field_name;
DROP INDEX IF EXISTS idx_rule_id;
DROP INDEX IF EXISTS idx_severity;

DROP INDEX IF EXISTS idx_interface_name_val;
DROP INDEX IF EXISTS idx_file_name_val;
DROP INDEX IF EXISTS idx_data_date_val;
DROP INDEX IF EXISTS idx_start_time_val;

DROP INDEX IF EXISTS idx_interface_name_meta;
DROP INDEX IF EXISTS idx_field_name_meta;

-- 删除表（如果存在）
DROP TABLE IF EXISTS vgop_validation_alerts;
DROP TABLE IF EXISTS vgop_metrics_history;
DROP TABLE IF EXISTS vgop_task_execution_log;
DROP TABLE IF EXISTS vgop_file_backup;
DROP TABLE IF EXISTS vgop_task_execution;
DROP TABLE IF EXISTS vgop_task_executions;
DROP TABLE IF EXISTS vgop_error_records;
DROP TABLE IF EXISTS vgop_validation_summaries;
DROP TABLE IF EXISTS vgop_field_metadata; 