package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 枚举值校验规则
 * 检查字段值是否在指定的枚举值列表中
 */
@Component
public class EnumValueRule extends AbstractValidationRule {
    
    /**
     * 枚举值集合
     */
    private final Set<String> enumValues;
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     * @param enumValues 枚举值（逗号分隔）
     */
    public EnumValueRule(String fieldName, String enumValues) {
        super(
                "common." + fieldName + ".enum",
                "枚举值校验",
                "检查字段值是否在指定的枚举值列表中: " + enumValues,
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.FORMAT
        );
        this.enumValues = new HashSet<>(Arrays.asList(enumValues.split(",")));
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public EnumValueRule() {
        this("*", "");
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 如果值为空，则跳过校验（由RequiredFieldRule处理）
        if (StringUtils.isBlank(value)) {
            return createValidResult();
        }
        
        // 获取字段元数据
        FieldMetadata metadata = context.getFieldMetadata(getFieldName());
        
        // 确定枚举值列表
        Set<String> valuesToUse = enumValues;
        
        if (metadata != null && StringUtils.isNotBlank(metadata.getEnumValues())) {
            valuesToUse = new HashSet<>(Arrays.asList(metadata.getEnumValues().split(",")));
        }
        
        // 如果没有枚举值列表，则直接通过
        if (valuesToUse.isEmpty()) {
            return createValidResult();
        }
        
        // 校验枚举值
        if (!valuesToUse.contains(value)) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 的值 (%s) 不在允许的枚举值列表中: %s", 
                            getFieldName(), value, String.join(", ", valuesToUse)),
                    context
            );
        }
        
        return createValidResult();
    }
} 