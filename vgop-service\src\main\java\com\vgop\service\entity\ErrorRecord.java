package com.vgop.service.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 错误记录实体类
 * 对应数据库表：vgop_error_records
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ErrorRecord {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 数据日期
     */
    private String dataDate;
    
    /**
     * 行号
     */
    private Long rowNumber;
    
    /**
     * 字段名
     */
    private String fieldName;
    
    /**
     * 字段值
     */
    private String fieldValue;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 严重程度
     */
    private Severity severity;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 严重程度枚举
     */
    public enum Severity {
        LOW("低"),
        MEDIUM("中"),
        HIGH("高"),
        CRITICAL("严重");
        
        private final String description;
        
        Severity(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 