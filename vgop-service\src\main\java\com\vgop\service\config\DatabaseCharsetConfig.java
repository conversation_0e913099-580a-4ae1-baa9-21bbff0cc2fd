package com.vgop.service.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 数据库字符编码配置类
 * 专门处理GBase/Informix数据库的字符编码问题
 * 
 * 注意：GBase数据库的字符编码主要通过JDBC URL中的参数配置：
 * DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class DatabaseCharsetConfig {
    
    /**
     * 为主数据源创建字符编码安全的JdbcTemplate
     * 注意：不使用@Primary，避免与DataSourceConfig中的primaryJdbcTemplate冲突
     */
    @Bean
    public JdbcTemplate primaryCharsetSafeJdbcTemplate(@Qualifier("primaryDataSource") DataSource dataSource) {
        JdbcTemplate template = new JdbcTemplate(dataSource);
        
        // 配置查询超时时间
        template.setQueryTimeout(30);
        
        // 检查并配置字符编码相关的数据库会话参数
        configureCharsetSettings(template);
        
        log.info("已配置主数据源字符编码安全的JdbcTemplate");
        return template;
    }
    
    /**
     * 为次数据源创建字符编码安全的JdbcTemplate
     */
    @Bean
    @ConditionalOnProperty(name = "spring.datasource.secondary.url")
    public JdbcTemplate secondaryCharsetSafeJdbcTemplate(@Qualifier("secondaryDataSource") DataSource dataSource) {
        JdbcTemplate template = new JdbcTemplate(dataSource);
        
        // 配置查询超时时间
        template.setQueryTimeout(30);
        
        // 检查并配置字符编码相关的数据库会话参数
        configureCharsetSettings(template);
        
        log.info("已配置次数据源字符编码安全的JdbcTemplate");
        return template;
    }
    
    /**
     * 配置数据库字符编码设置
     * 采用非侵入式方法，主要依赖JDBC URL中的字符编码配置
     */
    private void configureCharsetSettings(JdbcTemplate jdbcTemplate) {
        try {
            // 检测数据库类型
            String databaseProductName = detectDatabaseType(jdbcTemplate);
            log.info("检测到数据库类型: {}", databaseProductName);
            
            if (databaseProductName.contains("Informix")) {
                configureInformixCharset(jdbcTemplate);
            } else if (databaseProductName.contains("GBase")) {
                configureGBaseCharset(jdbcTemplate);
            } else {
                log.info("数据库类型: {}, 跳过特殊字符编码配置", databaseProductName);
            }
            
            // 验证字符编码设置（非阻塞）
            verifyCharsetSettings(jdbcTemplate);
            
        } catch (Exception e) {
            // 字符编码配置失败不应阻止应用启动
            log.warn("配置数据库字符编码设置时出现异常，使用默认设置: {}", e.getMessage());
            log.debug("字符编码配置异常详情", e);
        }
    }
    
    /**
     * 检测数据库类型
     */
    private String detectDatabaseType(JdbcTemplate jdbcTemplate) {
        try {
            return jdbcTemplate.execute((Connection conn) -> {
                String productName = conn.getMetaData().getDatabaseProductName();
                String productVersion = conn.getMetaData().getDatabaseProductVersion();
                log.debug("数据库产品名称: {}, 版本: {}", productName, productVersion);
                return productName;
            });
        } catch (Exception e) {
            log.warn("检测数据库类型失败: {}", e.getMessage());
            return "UNKNOWN";
        }
    }
    
    /**
     * 配置Informix数据库的字符编码设置
     */
    private void configureInformixCharset(JdbcTemplate jdbcTemplate) {
        log.info("配置Informix数据库字符编码设置");
        try {
            // Informix专用的字符编码命令
            String[] informixCommands = {
                "SET ENVIRONMENT CLIENT_LOCALE 'zh_CN.8859-1'",
                "SET ENVIRONMENT DB_LOCALE 'zh_CN.8859-1'",
                "SET ENVIRONMENT GL_USEGLU 1"
            };
            
            for (String command : informixCommands) {
                try {
                    jdbcTemplate.execute(command);
                    log.debug("Informix字符编码设置成功: {}", command);
                } catch (Exception e) {
                    log.warn("Informix字符编码设置命令执行失败: {}, 错误: {}", command, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.warn("配置Informix字符编码失败: {}", e.getMessage());
        }
    }
    
    /**
     * 配置GBase数据库的字符编码设置
     * GBase数据库主要依赖JDBC URL中的字符编码参数，不需要执行特殊的SQL命令
     */
    private void configureGBaseCharset(JdbcTemplate jdbcTemplate) {
        log.info("配置GBase数据库字符编码设置");
        
        // GBase数据库的字符编码主要通过JDBC URL配置
        // 例如: DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
        // 这里只做验证和日志记录，不执行可能不兼容的SQL命令
        
        try {
            // 查询GBase数据库的字符编码信息
            String locale = jdbcTemplate.queryForObject(
                "SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1", String.class);
            log.info("GBase数据库当前字符编码: {}", locale);
            
            // 验证连接的字符编码配置是否正确
            verifyGBaseCharsetConfig(jdbcTemplate);
            
        } catch (Exception e) {
            log.debug("查询GBase数据库字符编码信息失败: {}", e.getMessage());
            // 不抛出异常，允许应用正常启动
        }
        
        log.info("GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET");
    }
    
    /**
     * 验证GBase数据库的字符编码配置
     */
    private void verifyGBaseCharsetConfig(JdbcTemplate jdbcTemplate) {
        try {
            // 测试中文字符处理能力
            String testResult = jdbcTemplate.queryForObject(
                "SELECT '测试GBase字符编码' FROM systables WHERE tabid = 1", String.class);
            
            if (testResult != null && testResult.contains("测试")) {
                log.info("GBase数据库中文字符编码测试通过");
            } else {
                log.warn("GBase数据库中文字符编码测试异常，结果: {}", testResult);
            }
            
        } catch (Exception e) {
            log.debug("GBase数据库字符编码验证失败: {}", e.getMessage());
        }
    }
    
    /**
     * 验证字符编码设置（非阻塞）
     */
    private void verifyCharsetSettings(JdbcTemplate jdbcTemplate) {
        try {
            // 基础连接测试
            String hostname = jdbcTemplate.queryForObject(
                "SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1", String.class);
            log.debug("数据库主机名: {}", hostname);
            
            // 字符编码兼容性测试
            testCharsetCompatibility(jdbcTemplate);
            
        } catch (Exception e) {
            log.debug("验证字符编码设置时出现问题: {}", e.getMessage());
            // 不抛出异常，允许应用正常启动
        }
    }
    
    /**
     * 测试字符编码兼容性（非阻塞）
     */
    private void testCharsetCompatibility(JdbcTemplate jdbcTemplate) {
        try {
            // 测试中文字符长度计算
            String testSql = "SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1";
            Integer result = jdbcTemplate.queryForObject(testSql, Integer.class);
            
            if (result != null && result > 0) {
                log.info("字符编码兼容性测试通过，中文字符长度: {}", result);
            } else {
                log.warn("字符编码兼容性测试异常，结果: {}", result);
            }
            
        } catch (Exception e) {
            log.debug("字符编码兼容性测试失败: {}", e.getMessage());
            // 即使测试失败也不影响应用启动
        }
    }
    
    /**
     * 创建数据库字符编码监控Bean
     */
    @Bean
    public DatabaseCharsetMonitor databaseCharsetMonitor(@Qualifier("primaryJdbcTemplate") JdbcTemplate jdbcTemplate) {
        return new DatabaseCharsetMonitor(jdbcTemplate);
    }
    
    /**
     * 数据库字符编码监控器
     */
    public static class DatabaseCharsetMonitor {
        private final JdbcTemplate jdbcTemplate;
        
        public DatabaseCharsetMonitor(JdbcTemplate jdbcTemplate) {
            this.jdbcTemplate = jdbcTemplate;
        }
        
        /**
         * 检查数据库连接的字符编码状态
         */
        public boolean checkCharsetHealth() {
            try {
                // 执行一个包含中文的查询，检查是否会出现编码错误
                String testQuery = "SELECT '字符编码健康检查' AS test_result FROM systables WHERE tabid = 1";
                String result = jdbcTemplate.queryForObject(testQuery, String.class);
                
                // 检查结果是否包含乱码
                boolean isHealthy = result != null && !result.contains("?") && !result.contains("");
                
                if (isHealthy) {
                    log.debug("数据库字符编码健康检查通过");
                } else {
                    log.warn("数据库字符编码健康检查失败，结果: {}", result);
                }
                
                return isHealthy;
                
            } catch (Exception e) {
                log.warn("字符编码健康检查失败: {}", e.getMessage());
                return false;
            }
        }
        
        /**
         * 获取当前数据库的字符编码信息
         */
        public String getCharsetInfo() {
            try {
                StringBuilder info = new StringBuilder();
                
                // 查询数据库版本信息
                try {
                    String version = jdbcTemplate.queryForObject(
                        "SELECT DBINFO('version', 'server-type') FROM systables WHERE tabid = 1", String.class);
                    info.append("数据库版本: ").append(version).append("; ");
                } catch (Exception e) {
                    info.append("数据库版本: 查询失败; ");
                }
                
                // 查询字符集信息
                try {
                    String charset = jdbcTemplate.queryForObject(
                        "SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1", String.class);
                    info.append("数据库字符集: ").append(charset).append("; ");
                } catch (Exception e) {
                    info.append("数据库字符集: 查询失败; ");
                }
                
                // 查询服务器编码信息
                try {
                    String serverCharset = jdbcTemplate.queryForObject(
                        "SELECT DBINFO('locale') FROM systables WHERE tabid = 1", String.class);
                    info.append("服务器字符集: ").append(serverCharset).append("; ");
                } catch (Exception e) {
                    info.append("服务器字符集: 查询失败; ");
                }
                
                return info.toString();
                
            } catch (Exception e) {
                return "获取字符编码信息失败: " + e.getMessage();
            }
        }
        
        /**
         * 获取JDBC连接的字符编码配置信息
         */
        public String getJdbcCharsetInfo() {
            try {
                return jdbcTemplate.execute((Connection conn) -> {
                    StringBuilder info = new StringBuilder();
                    String url = conn.getMetaData().getURL();
                    
                    // 提取URL中的字符编码配置
                    if (url.contains("DB_LOCALE=")) {
                        String dbLocale = extractParameter(url, "DB_LOCALE");
                        info.append("JDBC DB_LOCALE: ").append(dbLocale).append("; ");
                    }
                    
                    if (url.contains("NEWCODESET=")) {
                        String newCodeset = extractParameter(url, "NEWCODESET");
                        info.append("JDBC NEWCODESET: ").append(newCodeset).append("; ");
                    }
                    
                    if (url.contains("CLIENT_LOCALE=")) {
                        String clientLocale = extractParameter(url, "CLIENT_LOCALE");
                        info.append("JDBC CLIENT_LOCALE: ").append(clientLocale).append("; ");
                    }
                    
                    return info.length() > 0 ? info.toString() : "未找到JDBC字符编码配置";
                });
            } catch (Exception e) {
                return "获取JDBC字符编码配置失败: " + e.getMessage();
            }
        }
        
        /**
         * 从URL中提取参数值
         */
        private String extractParameter(String url, String paramName) {
            try {
                int startIndex = url.indexOf(paramName + "=");
                if (startIndex == -1) {
                    return null;
                }
                
                startIndex += paramName.length() + 1;
                int endIndex = url.indexOf(";", startIndex);
                if (endIndex == -1) {
                    endIndex = url.length();
                }
                
                return url.substring(startIndex, endIndex);
            } catch (Exception e) {
                return "解析失败";
            }
        }
    }
} 