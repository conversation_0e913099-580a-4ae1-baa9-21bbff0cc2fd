package com.vgop.service.controller;

import com.vgop.service.common.ApiResponse;
import com.vgop.service.config.ValidationRulesProperties;
import com.vgop.service.validation.ValidationEngine;
import com.vgop.service.validation.ValidationRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 校验规则调试控制器
 */
@Slf4j
@RestController
@RequestMapping("/debug/validation")
public class ValidationDebugController {

    @Autowired
    private ValidationRulesProperties validationRulesProperties;

    @Autowired
    private ValidationEngine validationEngine;

    @GetMapping("/interfaces")
    public ApiResponse<Set<String>> getAllInterfaces() {
        return ApiResponse.success(validationEngine.getAllInterfaces());
    }

    @GetMapping("/interfaces/{interfaceName}")
    public ApiResponse<List<ValidationRule>> getInterfaceRules(@PathVariable String interfaceName) {
        List<ValidationRule> rules = validationEngine.getRulesByInterface(interfaceName);
        if (rules.isEmpty()) {
            return ApiResponse.error(404, "未找到接口 " + interfaceName + " 的规则");
        }
        return ApiResponse.success(rules);
    }

    @GetMapping("/interfaces/{interfaceName}/fields")
    public ApiResponse<Set<String>> getInterfaceFields(@PathVariable String interfaceName) {
        Set<String> fields = validationEngine.getFieldsByInterface(interfaceName);
        if (fields.isEmpty()) {
            return ApiResponse.error(404, "未找到接口 " + interfaceName + " 或该接口下没有配置字段");
        }
        return ApiResponse.success(fields);
    }

    @GetMapping("/interfaces/{interfaceName}/fields/{fieldName}")
    public ApiResponse<List<ValidationRule>> getFieldRules(@PathVariable String interfaceName, @PathVariable String fieldName) {
        List<ValidationRule> rules = validationEngine.getRulesByField(interfaceName, fieldName);
        if (rules.isEmpty()) {
            return ApiResponse.error(404, String.format("未找到接口 %s 中字段 %s 的规则", interfaceName, fieldName));
        }
        return ApiResponse.success(rules);
    }
} 