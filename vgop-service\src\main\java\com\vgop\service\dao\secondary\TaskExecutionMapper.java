package com.vgop.service.dao.secondary;

import com.vgop.service.entity.TaskExecution;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 任务执行记录Mapper接口
 * 操作vgop_task_execution表
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface TaskExecutionMapper {
    
    /**
     * 插入任务执行记录
     */
    @Insert({
        "INSERT INTO vgop_task_execution (",
        "  task_type, data_date, stage, revision, status,",
        "  start_time, end_time, duration, error_message,",
        "  processed_count, success_count, fail_count, extra_info",
        ") VALUES (",
        "  #{taskType}, #{dataDate}, #{stage}, #{revision}, #{status},",
        "  #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{duration}, #{errorMessage},",
        "  #{processedCount}, #{successCount}, #{failCount}, #{extraInfo}",
        ")"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TaskExecution execution);
    
    /**
     * 更新任务执行状态
     */
    @Update({
        "UPDATE vgop_task_execution SET",
        "  status = #{status},",
        "  end_time = #{endTime,jdbcType=TIMESTAMP},",
        "  duration = #{duration},",
        "  error_message = #{errorMessage},",
        "  processed_count = #{processedCount},",
        "  success_count = #{successCount},",
        "  fail_count = #{failCount},",
        "  extra_info = #{extraInfo},",
        "  update_time = #{updateTime,jdbcType=TIMESTAMP}",
        "WHERE id = #{id}"
    })
    int update(TaskExecution execution);
    
    /**
     * 根据ID查询任务执行记录
     */
    @Select("SELECT * FROM vgop_task_execution WHERE id = #{id}")
    TaskExecution findById(Long id);
    
    /**
     * 查询指定日期和类型的最新任务执行记录
     */
    @Select({
        "SELECT * FROM vgop_task_execution",
        "WHERE data_date = #{dataDate}",
        "  AND task_type = #{taskType}",
        "  AND stage = #{stage}",
        "ORDER BY revision DESC, id DESC",
        "LIMIT 1"
    })
    TaskExecution findLatestExecution(
            @Param("dataDate") String dataDate,
            @Param("taskType") String taskType,
            @Param("stage") String stage);
    
    /**
     * 查询指定日期范围内的任务执行记录
     */
    @Select({
        "SELECT * FROM vgop_task_execution",
        "WHERE data_date BETWEEN #{startDate} AND #{endDate}",
        "ORDER BY data_date DESC, task_type, stage, revision DESC"
    })
    List<TaskExecution> findByDateRange(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);
    
    /**
     * 查询失败的任务执行记录
     */
    @Select({
        "SELECT * FROM vgop_task_execution",
        "WHERE status = 'FAILED'",
        "  AND data_date BETWEEN #{startDate} AND #{endDate}",
        "ORDER BY data_date DESC, task_type, stage"
    })
    List<TaskExecution> findFailedExecutions(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);
    
    /**
     * 查询正在运行的任务
     */
    @Select("SELECT * FROM vgop_task_execution WHERE status = 'RUNNING'")
    List<TaskExecution> findRunningTasks();
    
    /**
     * 获取指定任务类型的最近一次执行记录
     */
    @Select({
        "SELECT * FROM vgop_task_execution",
        "WHERE task_type = #{taskType}",
        "ORDER BY data_date DESC, revision DESC, id DESC",
        "LIMIT 1"
    })
    TaskExecution getLastExecutionByType(@Param("taskType") String taskType);
    
    /**
     * 获取指定任务类型的执行历史
     */
    @Select({
        "SELECT * FROM vgop_task_execution",
        "WHERE task_type = #{taskType}",
        "ORDER BY data_date DESC, revision DESC, id DESC",
        "LIMIT #{limit}"
    })
    List<TaskExecution> getTaskExecutionHistory(
            @Param("taskType") String taskType,
            @Param("limit") int limit);
    
    /**
     * 获取指定日期和类型的任务执行记录
     */
    @Select({
        "SELECT * FROM vgop_task_execution",
        "WHERE data_date = #{dataDate}",
        "  AND task_type = #{taskType}",
        "ORDER BY revision DESC, id DESC",
        "LIMIT 1"
    })
    TaskExecution getExecutionByDateAndType(
            @Param("dataDate") String dataDate,
            @Param("taskType") String taskType);
} 