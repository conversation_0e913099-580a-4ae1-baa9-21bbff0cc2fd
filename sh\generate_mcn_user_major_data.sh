#!/bin/bash
#
# 脚本功能: 为 mcn_user_major 表生成并导入测试数据。
#           已优化唯一键（如手机号）的生成逻辑，避免导入时冲突。
# 使用方法: ./generate_mcn_user_major_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_user_major_data.sh 20250611 1000 bms
#

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    echo "示例: $0 20250611 1000 bms"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"
# 使用 mktemp 创建一个安全的临时文件
DATA_FILE=$(mktemp "mcn_user_major_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 生成 '${DATA_COUNT}' 条测试数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- 为本次运行生成唯一的ID前缀，以避免主键冲突 ---
# 使用高精度时间戳作为前缀，确保每次运行生成不同的序列
PHONE_PREFIX="19$(date +%N | cut -c 1-5)" # 19 + 5位纳秒 = 7位前缀. 手机号共11位
IMSI_PREFIX="4600$(date +%N | cut -c 1-7)"   # 4600 + 7位纳秒 = 11位前缀. IMSI共15位
IMEI_PREFIX="860$(date +%N | cut -c 1-8)"    # 860 + 8位纳秒 = 11位前缀. IMEI共15位

# --- 检查请求的数据量是否会超出唯一ID的生成范围 ---
MAX_RECORDS=9999 # 手机号/IMSI/IMEI 的后缀使用4位序列号 (0001-9999)

if [ ${DATA_COUNT} -gt ${MAX_RECORDS} ]; then
    echo "错误: 请求的数据量 (${DATA_COUNT}) 过大，超出了当前脚本单次最大生成能力 (${MAX_RECORDS})。"
    rm "${DATA_FILE}"
    exit 1
fi

echo "正在生成数据..."
for i in $(seq 1 ${DATA_COUNT})
do
  # 1. 生成各字段的随机/唯一数据
  PHONENUMBER=$(printf "%s%04d" "${PHONE_PREFIX}" "${i}")
  PHONESTATE=$(($RANDOM % 2)) # 0 或 1
  PHONEIMSI=$(printf "%s%04d" "${IMSI_PREFIX}" "${i}")
  PHONEIMEI=$(printf "%s%04d" "${IMEI_PREFIX}" "${i}")
  LOCATIONID=$(($RANDOM % 90000000 + 10000000)) # 8位随机 location id
  PROVINCEID=$(($RANDOM % 900 + 100)) # 默认使用随机3位数ID

  # 2. 在指定日期内生成随机时间
  HOUR=$(printf "%02d" $(($RANDOM % 24)))
  MINUTE=$(printf "%02d" $(($RANDOM % 60)))
  SECOND=$(printf "%02d" $(($RANDOM % 60)))
  OPENINGTIME="${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
  
  OPTIME=${OPENINGTIME} # Optime 使用与 openingtime 相同的时间
  SEX=$(($RANDOM % 3))  # 0, 1, 2 分别代表 未知, 男, 女

  # 3. 将生成的数据行追加到临时文件，使用'|'作为分隔符
  echo "${PHONENUMBER}${DELIMITER}${PHONESTATE}${DELIMITER}${PHONEIMSI}${DELIMITER}${PHONEIMEI}${DELIMITER}${LOCATIONID}${DELIMITER}${PROVINCEID}${DELIMITER}${OPENINGTIME}${DELIMITER}${OPTIME}${DELIMITER}${SEX}" >> "${DATA_FILE}"
  
  # 打印进度
  if [ $(($i % 200)) -eq 0 ]; then
    printf "已生成 %d / %d 条记录\r" "$i" "$DATA_COUNT"
  fi
done
echo -e "\n数据生成完毕."
echo "准备将数据导入数据库..."

# 4. 准备 LOAD 命令
# 列的顺序必须与临时文件中的数据列顺序完全一致
COLUMNS="phonenumber, phonestate, phoneimsi, phoneimei, locationid, provinceid, openingtime, optime, sex"
SQL="set lock mode to wait 10; LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO mcn_user_major (${COLUMNS});"

echo "将要执行以下SQL命令:"
echo "${SQL}"

# 5. 使用 dbaccess 执行 LOAD 命令
# 添加错误处理，以便在命令失败时得到通知
if echo "${SQL}" | dbaccess "${DB_NAME}" -; then
  echo "数据成功导入!"
  # 6. 清理临时文件
  rm "${DATA_FILE}"
  echo "临时数据文件 '${DATA_FILE}' 已被删除。"
else
  echo "数据导入失败！请检查 dbaccess 的输出和日志。"
  echo "临时数据文件 '${DATA_FILE}' 已保留，以供调试。"
  exit 1
fi

echo "脚本执行完毕。" 