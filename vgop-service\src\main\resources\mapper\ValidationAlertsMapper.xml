<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vgop.service.dao.ValidationAlertsMapper">

    <resultMap id="ValidationAlertsResultMap" type="com.vgop.service.entity.ValidationAlert">
        <id column="alert_id" property="alertId"/>
        <result column="alert_time" property="alertTime"/>
        <result column="interface_name" property="interfaceName"/>
        <result column="alert_type" property="alertType"/>
        <result column="alert_level" property="alertLevel" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="alert_message" property="alertMessage"/>
        <result column="file_name" property="fileName"/>
        <result column="line_number" property="lineNumber"/>
        <result column="error_data" property="errorData"/>
        <result column="field_errors" property="fieldErrors"/>
        <result column="metric_details" property="metricDetails"/>
        <result column="excel_report_path" property="excelReportPath"/>
        <result column="status" property="status" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="handled_by" property="handledBy"/>
        <result column="handled_time" property="handledTime"/>
    </resultMap>

    <select id="findById" resultMap="ValidationAlertsResultMap">
        SELECT alert_id, alert_time, interface_name, alert_type, alert_level, alert_message,
               file_name, line_number, error_data, field_errors, metric_details, excel_report_path,
               status, handled_by, handled_time
        FROM vgop_validation_alerts
        WHERE alert_id = #{alertId}
    </select>

    <select id="findByInterfaceAndDateRange" resultMap="ValidationAlertsResultMap">
        SELECT alert_id, alert_time, interface_name, alert_type, alert_level, alert_message,
               file_name, line_number, error_data, field_errors, metric_details, excel_report_path,
               status, handled_by, handled_time
        FROM vgop_validation_alerts
        WHERE interface_name = #{interfaceName}
          AND alert_time >= #{startTime}
          AND alert_time &lt; #{endTime}
        ORDER BY alert_time DESC
    </select>

    <!-- 根据接口名称和数据日期字符串查询 -->
    <select id="findByInterfaceAndDate" resultMap="ValidationAlertsResultMap">
        SELECT alert_id, alert_time, interface_name, alert_type, alert_level, alert_message,
               file_name, line_number, error_data, field_errors, metric_details, excel_report_path,
               status, handled_by, handled_time
        FROM vgop_validation_alerts
        WHERE interface_name = #{interfaceName}
          AND SUBSTR(alert_time, 1, 8) = #{dataDate}
        ORDER BY alert_time DESC
    </select>

    <!-- 查询包含详细错误信息的数据校验失败记录 -->
    <select id="findValidationErrorsByInterfaceAndDate" resultMap="ValidationAlertsResultMap">
        SELECT alert_id, alert_time, interface_name, alert_type, alert_level, alert_message,
               file_name, line_number, error_data, field_errors, metric_details, excel_report_path,
               status, handled_by, handled_time
        FROM vgop_validation_alerts
        WHERE interface_name = #{interfaceName}
          AND SUBSTR(alert_time, 1, 8) = #{dataDate}
          AND alert_type = '数据校验失败'
          AND (field_errors IS NOT NULL AND LENGTH(field_errors) > 0)
        ORDER BY alert_time DESC, line_number ASC
    </select>

    <select id="findByStatus" resultMap="ValidationAlertsResultMap">
        SELECT alert_id, alert_time, interface_name, alert_type, alert_level, alert_message,
               file_name, line_number, error_data, field_errors, metric_details, excel_report_path,
               status, handled_by, handled_time
        FROM vgop_validation_alerts
        WHERE status = #{status}
        ORDER BY alert_time DESC
    </select>

    <insert id="insert" parameterType="com.vgop.service.entity.ValidationAlert" useGeneratedKeys="true" keyProperty="alertId">
        INSERT INTO vgop_validation_alerts (alert_time, interface_name, alert_type, alert_level, alert_message,
                                           file_name, line_number, error_data, field_errors, metric_details,
                                           excel_report_path, status, handled_by, handled_time)
        VALUES (#{alertTime}, 
                #{interfaceName}, 
                #{alertType}, 
                #{alertLevel,typeHandler=org.apache.ibatis.type.EnumTypeHandler}, 
                #{alertMessage}, 
                #{fileName}, 
                #{lineNumber}, 
                #{errorData}, 
                #{fieldErrors}, 
                #{metricDetails}, 
                #{excelReportPath}, 
                #{status,typeHandler=org.apache.ibatis.type.EnumTypeHandler}, 
                #{handledBy}, 
                #{handledTime})
    </insert>

    <update id="updateStatus">
        UPDATE vgop_validation_alerts
        SET status = #{status}, 
            handled_by = #{handledBy}, 
            handled_time = #{handledTime}
        WHERE alert_id = #{alertId}
    </update>

    <delete id="deleteByAlertId">
        DELETE FROM vgop_validation_alerts
        WHERE alert_id = #{alertId}
    </delete>

</mapper> 