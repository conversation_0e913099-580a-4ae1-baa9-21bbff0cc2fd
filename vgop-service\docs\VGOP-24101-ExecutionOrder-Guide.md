# VGOP1-R2.11-24101 任务执行顺序实现指南

## 概述

VGOP1-R2.11-24101是业务分析统计任务，根据原始Shell脚本的逻辑，该任务在执行数据导出前必须先调用`bmssp_VGOP_banalyse`存储过程。

## 执行顺序要求

根据原始脚本`VGOP1-R2.11-24101.sh`，正确的执行顺序为：

1. **存储过程调用**: 调用`bmssp_VGOP_banalyse`存储过程
2. **数据生成验证**: 检查`bms_vgop_banalyse`表是否生成数据
3. **数据导出**: 从`bms_vgop_banalyse`表中查询数据并使用UNLOAD导出
4. **文件处理**: 文件分割、重命名等处理
5. **SFTP传输**: 上传生成的.dat和.verf文件

## 🔧 关键问题解决：存储过程执行方式

### 问题描述

原始通过JDBC调用存储过程的方式导致`bms_vgop_banalyse`表没有数据生成，这是因为：

1. **执行环境差异**: 原始Shell脚本通过`dbaccess`命令行工具直接连接数据库服务器
2. **权限差异**: Java应用通过网络连接访问数据库，可能缺少必要的权限
3. **事务处理差异**: 存储过程可能需要在特定的事务环境中执行

### 解决方案

**关键修改**: 改用`dbaccess`命令行方式执行存储过程，确保与原始Shell脚本执行环境完全一致。

```java
// 原始JDBC方式（问题方式）
dataExportMapper.callBmsSpVgopBanalyse(debugFile, traceFlag, taskId);

// 修改后的dbaccess方式（解决方案）
boolean storedProcSuccess = executeStoredProcedureViaDbAccess(debugFile, traceFlag, taskId);
```

## 代码实现

### 1. DataExportService.exportData() 修改

在`DataExportService.exportData()`方法中添加了特殊处理逻辑：

```java
// **特殊处理：VGOP1-R2.11-24101业务分析任务需要先调用存储过程**
if ("VGOP1-R2.11-24101".equals(interfaceId)) {
    log.info("检测到VGOP业务分析任务，执行存储过程调用: {}", interfaceId);
    
    // **关键修改：使用dbaccess命令行方式执行存储过程**
    boolean storedProcSuccess = executeStoredProcedureViaDbAccess(debugFile, traceFlag, taskId);
    
    if (!storedProcSuccess) {
        log.error("存储过程 bmssp_VGOP_banalyse 执行失败");
        return new ExportResult(interfaceId, false, "存储过程执行失败");
    }
    
    // 验证存储过程执行结果：检查bms_vgop_banalyse表是否有数据
    boolean hasData = verifyBanalyseTableData(beforeDay);
    
    if (!hasData) {
        log.warn("存储过程执行完成，但bms_vgop_banalyse表中没有生成数据");
        // 继续执行，但记录警告
    } else {
        log.info("存储过程执行成功，bms_vgop_banalyse表已生成数据");
    }
}
```

### 2. executeStoredProcedureViaDbAccess() 方法

```java
private boolean executeStoredProcedureViaDbAccess(String debugFile, String traceFlag, String taskId) {
    try {
        // 获取数据库名称
        String databaseName = databaseUtil.getDatabaseName();
        
        // 构建存储过程调用SQL，与原始脚本保持一致
        String sqlCommand = String.format(
            "set lock mode to wait 10;call bmssp_VGOP_banalyse(\"%s\",\"%s\",\"%s\");",
            debugFile, traceFlag, taskId
        );
        
        // 构建dbaccess命令
        ProcessBuilder processBuilder = new ProcessBuilder("dbaccess", databaseName);
        processBuilder.redirectErrorStream(true);
        
        Process process = processBuilder.start();
        
        // 向进程输入SQL命令
        try (var outputStream = process.getOutputStream()) {
            outputStream.write(sqlCommand.getBytes());
            outputStream.flush();
        }
        
        // 等待进程完成并检查结果
        int exitCode = process.waitFor();
        return exitCode == 0;
        
    } catch (Exception e) {
        log.error("通过dbaccess执行存储过程时发生异常", e);
        return false;
    }
}
```

### 3. verifyBanalyseTableData() 方法

```java
private boolean verifyBanalyseTableData(String statTime) {
    try {
        List<Map<String, Object>> results = dataExportMapper.selectBanalyseData(statTime);
        boolean hasData = results != null && !results.isEmpty();
        
        if (hasData) {
            log.info("验证成功：bms_vgop_banalyse表中找到 {} 条记录", results.size());
        } else {
            log.warn("验证结果：bms_vgop_banalyse表中没有找到数据");
        }
        
        return hasData;
        
    } catch (Exception e) {
        log.error("验证bms_vgop_banalyse表数据时发生异常", e);
        return false;
    }
}
```

## 测试验证

### 1. 使用REST API测试

```bash
# 测试特定日期的任务执行顺序
curl -X POST "http://localhost:8080/api/vgop/tasks/test-vgop-24101-execution-order?dataDate=20241225" \
     -H "Content-Type: application/json"
```

### 2. 使用任务执行API测试

```bash
# 执行完整的VGOP1-R2.11-24101任务
curl -X POST "http://localhost:8080/api/vgop/tasks/execute" \
     -H "Content-Type: application/json" \
     -d '{
       "actionInstanceId": "TEST_24101_20241225",
       "interfaceId": "VGOP1-R2.11-24101",
       "dateId": "20241225",
       "imagePath": "/opt/vgop"
     }'
```

## 日志验证步骤

执行任务后，检查应用日志，应该包含以下关键信息：

```
[INFO] 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
[INFO] 通过dbaccess执行存储过程，数据库: bms
[INFO] 存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse("","0","20241225");
[INFO] dbaccess存储过程执行结果 - 退出码: 0, 输出: ...
[INFO] 存储过程通过dbaccess执行成功
[INFO] 验证成功：bms_vgop_banalyse表中找到 X 条记录，stattime=20241224
[INFO] 存储过程执行成功，bms_vgop_banalyse表已生成数据，stattime=20241224
```

## 环境要求

1. **服务器环境**: 确保Java应用运行的服务器上安装了Informix/GBase客户端
2. **dbaccess命令**: 确保`dbaccess`命令在系统PATH中可用
3. **数据库权限**: 确保应用使用的数据库用户具有执行存储过程的权限

## 故障排除

### 1. dbaccess命令不可用

```bash
# 检查dbaccess是否可用
which dbaccess

# 如果不可用，需要安装数据库客户端或设置环境变量
export PATH=$PATH:/opt/informix/bin
```

### 2. 存储过程执行失败

检查以下项目：
- 数据库连接参数是否正确
- 用户是否有执行存储过程的权限
- 存储过程是否存在
- 传入参数是否正确

### 3. 表中仍然没有数据

如果存储过程执行成功但表中没有数据：
- 检查存储过程内部逻辑
- 确认统计时间参数是否正确
- 检查源数据表是否有对应时间的数据

## 总结

通过改用`dbaccess`命令行方式执行存储过程，解决了原有JDBC调用方式导致的数据生成问题，确保了VGOP1-R2.11-24101任务能够按照与原始Shell脚本完全一致的方式执行，生成正确的统计数据。

## 配置要求

### 任务配置

在`application-dev.yml`中，VGOP1-R2.11-24101任务配置如下：

```yaml
- interfaceId: "VGOP1-R2.11-24101"
  interfaceName: "VGOP1-R2.11-24101"
  enabled: true
  taskType: "daily"
  export:
    # SQL查询：业务分析统计查询，参照脚本逻辑，先调用存储过程再查询结果表
    sqlTemplate: "select provinceid,locationid,phonenum,mcnnum,appactivenum,mcnactivenum,paynum,feenum,secphonenum,secmcnnum,amcnnum from bms_vgop_banalyse where stattime='{previousDay}'"
    tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-24101.unl"
    outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-24101_{revTimes}_{fileNum}.dat"
    verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-24101_{revTimes}.verf"
    maxLinesPerFile: 2000000
```

注意：配置中的`sqlTemplate`查询的是`bms_vgop_banalyse`表，这个表的数据是由存储过程`bmssp_VGOP_banalyse`生成的。

## 测试方法

### 1. 单元测试

可以使用VgopTaskController中的测试方法：

```bash
POST /api/vgop/tasks/test-vgop-24101-execution-order?dataDate=20241225
```

### 2. 日志验证

执行任务时，查看日志输出，应该能看到以下关键日志：

```
[INFO] 开始执行数据导出任务: VGOP1-R2.11-24101
[INFO] VGOP业务分析任务执行顺序 - 步骤1: 即将调用存储过程 bmssp_VGOP_banalyse
[INFO] 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
[INFO] 调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20241225
[INFO] 存储过程 bmssp_VGOP_banalyse 执行完成
[INFO] VGOP业务分析任务执行顺序 - 步骤2: 存储过程执行完成，数据导出成功
```

### 3. 数据库验证

在执行任务前后，可以查询`bms_vgop_banalyse`表的数据变化：

```sql
-- 查看存储过程执行后的数据
SELECT COUNT(*) FROM bms_vgop_banalyse WHERE stattime = '20241224';
```

## 关键注意事项

1. **执行顺序严格性**: 必须先执行存储过程，再进行数据导出
2. **参数正确性**: 存储过程的参数必须与原始脚本保持一致
3. **错误处理**: 如果存储过程执行失败，整个任务应该停止执行
4. **日志记录**: 详细记录执行步骤，便于问题排查

## 原始脚本对比

原始Shell脚本中的关键部分：

```bash
# 调用存储过程
echo `date +"%Y%m%d %H%M%S:"`"bms bmssp_VGOP_banalyse Start" >>${LogName}
sqlstr="set lock mode to wait 10;call bmssp_VGOP_banalyse("\"${DebugFile}\",\"${TraceFlag}\",\"${TaskId}\"")"
echo "$sqlstr;"| dbaccess $SrcDBName  1>>${LogName} 2>&1
echo `date +"%Y%m%d %H%M%S:"`"bms bmssp_VGOP_banalyse END." >>${LogName}

# 数据导出
sql="select provinceid,locationid,phonenum,mcnnum,appactivenum,mcnactivenum,paynum,feenum,secphonenum,secmcnnum,amcnnum from bms_vgop_banalyse where stattime='${BeforeDay}'"
UnloadCmd="set lock mode to wait 10;unload to ${UnloadFileName} delimiter '$ColSep' ${sql};"
echo "${UnloadCmd}" | dbaccess $SrcDBName  1>>${LogName} 2>&1
```

Java代码完全遵循了这个执行顺序：先调用存储过程，再执行UNLOAD导出数据。

## 故障排查

如果任务执行失败，按以下顺序检查：

1. **存储过程是否存在**: 确认数据库中存在`bmssp_VGOP_banalyse`存储过程
2. **参数是否正确**: 检查传入存储过程的参数
3. **表是否存在**: 确认`bms_vgop_banalyse`表存在
4. **权限问题**: 确认应用有调用存储过程和查询表的权限
5. **数据库连接**: 确认数据库连接正常

## 版本信息

- **实现版本**: VGOP v1.0.0
- **最后更新**: 2024-12-25
- **责任人**: VGOP开发团队 