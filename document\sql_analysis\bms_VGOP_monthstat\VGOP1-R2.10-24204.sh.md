# 脚本 `VGOP1-R2.10-24204.sh` SQL逻辑分析

-   **脚本用途**: 抽取**上一个月份**内，通过特定渠道办理的**申请(s)**和**注销(z)**业务操作日志。
-   **数据来源**: `mcn_oplog` (用户操作成功记录表)。
-   **核心逻辑**:
    -   `optime < '${endtime}' and optime >= '${starttime}'`: 按操作时间，筛选出上一个自然月内的所有记录。
    -   `opmanner = '9'`: 筛选出操作渠道为 '9' 的记录。
    -   `optype='s' or optype='z'`: 只关心操作类型为 's'(申请) 或 'z'(注销) 的记录。
    -   `case optype ... end`: 将 `optype` 的 's' 和 'z' 分别转换为 '01' 和 '02'。
-   **输出内容**: 上一个月指定渠道的申请和注销记录，包含月份、主号码、转换后的操作类型和操作时间。
-   **说明**: 这是一个**月度增量**抽取脚本，用于统计特定渠道的业务办理情况。 