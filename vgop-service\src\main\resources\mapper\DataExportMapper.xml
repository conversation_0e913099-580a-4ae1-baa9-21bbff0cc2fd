<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vgop.service.dao.DataExportMapper">

    <!-- 调用业务分析统计存储过程 -->
    <select id="callBmsSpVgopBanalyse" statementType="CALLABLE">
        {call bmssp_VGOP_banalyse(#{debugFile}, #{traceFlag}, #{taskId})}
    </select>

    <!-- 查询业务分析统计结果 -->
    <select id="selectBanalyseData" resultType="map">
        SELECT provinceid, locationid, phonenum, mcnnum, appactivenum, mcnactivenum, 
               paynum, feenum, secphonenum, secmcnnum, amcnnum
        FROM vgop_metrics_history
        WHERE processing_date = #{statTime}
    </select>

    <!-- 查询每日新增主号用户信息 -->
    <select id="selectDailyNewMajorUsers" resultType="map">
        SELECT mum.phonenumber as phonenumber, 
               mum.phonestate as phonestate,
               mum.phoneimsi as phoneimsi,
               mum.phoneimei as phoneimei,
               mum.locationid as locationid,
               substr(bp.bossid, 1, 3) as provinceid,
               mum.openingtime as openingtime,
               mum.Optime as Optime,
               mum.sex as sex
        FROM mcn_user_major mum 
        LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid
        WHERE mum.openingtime >= #{startTime} 
          AND mum.openingtime &lt; #{endTime} 
          AND mum.phonestate IN ('0','1')
    </select>

    <!-- 查询主号用户全量快照（月统计） -->
    <select id="selectMajorUsersSnapshot" resultType="map">
        SELECT mum.phonenumber as phonenumber, 
               mum.phonestate as phonestate,
               mum.phoneimsi as phoneimsi,
               mum.phoneimei as phoneimei,
               mum.locationid as locationid,
               substr(bp.bossid, 1, 3) as provinceid,
               mum.openingtime as openingtime,
               mum.Optime as Optime,
               mum.sex as sex
        FROM mcn_user_major mum 
        LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid
        WHERE mum.openingtime &lt;= #{endDate}
          AND mum.phonestate IN ('0','1')
        ORDER BY mum.phonenumber
    </select>

    <!-- 查询副号用户全量快照（月统计） -->
    <select id="selectMinorUsersSnapshot" resultType="map">
        SELECT mum.phonenumber as phonenumber,
               mum.mcnnum as mcnnum,
               mum.openingtime as openingtime,
               mum.childnumber as childnumber,
               mum.childimsi as childimsi,
               mum.childimei as childimei,
               mum.childstate as childstate,
               mum.locationid as locationid,
               substr(bp.bossid, 1, 3) as provinceid,
               mum.Optime as Optime
        FROM mcn_user_minor mum 
        LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid
        WHERE mum.mcnnum IN (1, 2, 3)
          AND mum.openingtime &lt;= #{endDate}
          AND mum.childstate IN ('0','1')
        ORDER BY mum.phonenumber, mum.mcnnum
    </select>

    <!-- 查询月度操作日志 -->
    <select id="selectMonthlyOpLogs" resultType="map">
        SELECT mol.phonenumber as phonenumber,
               mol.mcnnum as mcnnum,
               mol.optime as optime,
               mol.opmanner as opmanner,
               mol.optype as optype,
               mol.opresult as opresult,
               mol.opoper as opoper,
               mol.locationid as locationid,
               substr(bp.bossid, 1, 3) as provinceid
        FROM mcn_oplog mol
        LEFT JOIN bossprovince bp ON mol.provinceid = bp.provinceid
        WHERE mol.optime >= #{startTime}
          AND mol.optime &lt; #{endTime}
          AND mol.opmanner = 9
          AND mol.optype IN ('s', 'z')
        ORDER BY mol.optime
    </select>

    <!-- 查询实体副号用户全量快照 -->
    <select id="selectSecUsersSnapshot" resultType="map">
        <![CDATA[
        (SELECT msm.phonenumber as phonenumber,
                msm.mcnnum as mcnnum,
                msm.openingtime as openingtime,
                msm.childnumber as childnumber,
                msm.childimsi as childimsi,
                msm.childimei as childimei,
                msm.businessState as businessState,
                msm.locationid as locationid,
                substr(bp.bossid, 1, 3) as provinceid,
                msm.Optime as Optime
         FROM mcn_sec_major msm
         LEFT JOIN bossprovince bp ON msm.provinceid = bp.provinceid
         WHERE msm.businessState = #{businessState})
        UNION ALL
        (SELECT msm2.phonenumber as phonenumber,
                msm2.mcnnum as mcnnum,
                msm2.openingtime as openingtime,
                msm2.childnumber as childnumber,
                msm2.childimsi as childimsi,
                msm2.childimei as childimei,
                msm2.businessState as businessState,
                msm2.locationid as locationid,
                substr(bp2.bossid, 1, 3) as provinceid,
                msm2.Optime as Optime
         FROM mcn_sec_major2 msm2
         LEFT JOIN bossprovince bp2 ON msm2.provinceid = bp2.provinceid
         WHERE msm2.businessState = #{businessState})
        ORDER BY phonenumber, mcnnum
        ]]>
    </select>

    <!-- 调用业务分析存储过程 -->
    <select id="callBanalyseStoredProcedure" statementType="CALLABLE">
        {call bmssp_VGOP_banalyse(#{beforeDay}, #{traceFlag}, #{sourceDbName})}
    </select>

</mapper> 