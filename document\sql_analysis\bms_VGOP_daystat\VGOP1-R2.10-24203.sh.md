# 脚本 `VGOP1-R2.10-24203.sh` SQL逻辑分析

-   **脚本用途**: 抽取前一天所有通过**客户端**进行操作的用户（可视为客户端日活用户）。
-   **数据来源**:
    -   `mcn_apploginlog`: App登录日志表。
    -   `mcn_oplog`: 用户操作成功记录表。
-   **核心逻辑**:
    -   使用 `UNION ALL` 合并两部分数据。
    -   **部分一**: 从 `mcn_apploginlog` 中选取前一天 (`logintime >= '${starttime}' and logintime < '${endtime}'`) 的所有登录记录，并标记 `type` 为 `1`。
    -   **部分二**: 从 `mcn_oplog` 中选取前一天 (`optime >= '${starttime}' and optime < '${endtime}'`) 并且操作渠道为客户端 (`opmanner=4`) 的所有操作记录，并标记 `type` 为 `2`。
-   **输出内容**: 活跃用户的主号码 (`phonenumber`)、活跃类型 (`type`)、操作时间 (`optime`) 和客户端版本 (`version`，仅登录日志提供)。
-   **说明**: 此脚本聚合了两种定义为"客户端活跃"的行为：**登录App** 和 **在App内成功操作业务**。 