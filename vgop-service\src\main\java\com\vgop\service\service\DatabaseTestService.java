package com.vgop.service.service;

import com.vgop.service.config.DatabaseType;
import com.vgop.service.util.DatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库连接测试服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class DatabaseTestService {
    
    private final DataSource primaryDataSource;
    private final DataSource secondaryDataSource;
    private final JdbcTemplate primaryJdbcTemplate;
    private final JdbcTemplate secondaryJdbcTemplate;
    private final DatabaseUtil databaseUtil;
    
    @Autowired
    public DatabaseTestService(@Qualifier("primaryDataSource") DataSource primaryDataSource,
                             @Qualifier("secondaryDataSource") DataSource secondaryDataSource,
                             @Qualifier("primaryJdbcTemplate") JdbcTemplate primaryJdbcTemplate,
                             @Qualifier("secondaryJdbcTemplate") JdbcTemplate secondaryJdbcTemplate,
                             DatabaseUtil databaseUtil) {
        this.primaryDataSource = primaryDataSource;
        this.secondaryDataSource = secondaryDataSource;
        this.primaryJdbcTemplate = primaryJdbcTemplate;
        this.secondaryJdbcTemplate = secondaryJdbcTemplate;
        this.databaseUtil = databaseUtil;
    }
    
    /**
     * 应用启动时测试数据库连接
     */
    @PostConstruct
    public void testConnectionsOnStartup() {
        log.info("=== 开始测试数据库连接 ===");
        
        // 测试主数据源
        log.info("测试主数据源连接...");
        Map<String, Object> primaryInfo = testDataSource("主数据源", primaryDataSource);
        if (primaryInfo != null) {
            log.info("主数据源连接成功: {}", primaryInfo);
            
            // 检查数据库类型
            DatabaseType dbType = databaseUtil.getDatabaseType();
            log.info("检测到主数据库类型: {}", dbType.getName());
            log.info("是否支持UNLOAD命令: {}", dbType.supportsUnload());
        }
        
        // 测试次数据源
        log.info("测试次数据源连接...");
        Map<String, Object> secondaryInfo = testDataSource("次数据源", secondaryDataSource);
        if (secondaryInfo != null) {
            log.info("次数据源连接成功: {}", secondaryInfo);
        }
        
        log.info("=== 数据库连接测试完成 ===");
    }
    
    /**
     * 测试单个数据源
     */
    private Map<String, Object> testDataSource(String name, DataSource dataSource) {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            Map<String, Object> info = new HashMap<>();
            info.put("name", name);
            info.put("url", metaData.getURL());
            info.put("databaseProductName", metaData.getDatabaseProductName());
            info.put("databaseProductVersion", metaData.getDatabaseProductVersion());
            info.put("driverName", metaData.getDriverName());
            info.put("driverVersion", metaData.getDriverVersion());
            info.put("userName", metaData.getUserName());
            
            return info;
        } catch (SQLException e) {
            log.error("{} 连接失败: {}", name, e.getMessage());
            return null;
        }
    }
    
    /**
     * 测试主数据库查询
     */
    public boolean testPrimaryQuery() {
        try {
            // 对于不同的数据库使用不同的测试查询
            DatabaseType dbType = databaseUtil.getDatabaseType();
            String testQuery;
            
            switch (dbType) {
                case INFORMIX:
                case GBASE:
                    testQuery = "SELECT FIRST 1 1 as test FROM systables";
                    break;
                case H2:
                    testQuery = "SELECT 1 as test";
                    break;
                default:
                    testQuery = "SELECT 1 as test";
            }
            
            Integer result = primaryJdbcTemplate.queryForObject(testQuery, Integer.class);
            log.info("主数据库查询测试成功，结果: {}", result);
            return result != null && result == 1;
        } catch (Exception e) {
            log.error("主数据库查询测试失败", e);
            return false;
        }
    }
    
    /**
     * 测试次数据库查询
     */
    public boolean testSecondaryQuery() {
        try {
            Integer result = secondaryJdbcTemplate.queryForObject("SELECT 1 as test", Integer.class);
            log.info("次数据库查询测试成功，结果: {}", result);
            return result != null && result == 1;
        } catch (Exception e) {
            log.error("次数据库查询测试失败", e);
            return false;
        }
    }
} 