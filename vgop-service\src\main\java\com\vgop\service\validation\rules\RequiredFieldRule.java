package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 必填字段校验规则
 * 检查字段是否为空
 */
@Component
public class RequiredFieldRule extends AbstractValidationRule {
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     */
    public RequiredFieldRule(String fieldName) {
        super(
                "common." + fieldName + ".required",
                "必填字段校验",
                "检查字段是否为空",
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.REQUIRED
        );
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public RequiredFieldRule() {
        this("*");
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 获取字段元数据
        FieldMetadata metadata = context.getFieldMetadata(getFieldName());
        
        // 如果没有元数据或字段不是必填，则直接通过
        if (metadata == null || !metadata.isRequired()) {
            return createValidResult();
        }
        
        // 检查字段值是否为空
        if (StringUtils.isBlank(value)) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 不能为空", getFieldName()),
                    context
            );
        }
        
        return createValidResult();
    }
} 