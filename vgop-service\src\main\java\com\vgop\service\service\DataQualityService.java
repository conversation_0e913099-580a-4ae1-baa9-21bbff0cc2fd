package com.vgop.service.service;

import com.vgop.service.config.VgopProperties;
import com.vgop.service.dao.ValidationAlertsMapper;
import com.vgop.service.entity.ValidationAlert;
import com.vgop.service.util.DateUtil;
import com.vgop.service.util.DateStringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据质量检测服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataQualityService {

    private final VgopProperties vgopProperties;
    private final ValidationAlertsMapper validationAlertsMapper;

    /**
     * 数据质量检测结果
     */
    public static class QualityCheckResult {
        private boolean nonComplianceThresholdExceeded;
        private boolean volumeFluctuationDetected;
        private double nonComplianceRate;
        private double volumeFluctuationRate;
        private long totalRecords;
        private long errorRecords;
        private long previousDayRecords;
        private String reportFilePath;
        private List<String> alerts;

        public QualityCheckResult() {
            this.alerts = new ArrayList<>();
        }

        // Getters and setters
        public boolean isNonComplianceThresholdExceeded() { return nonComplianceThresholdExceeded; }
        public void setNonComplianceThresholdExceeded(boolean nonComplianceThresholdExceeded) { this.nonComplianceThresholdExceeded = nonComplianceThresholdExceeded; }
        
        public boolean isVolumeFluctuationDetected() { return volumeFluctuationDetected; }
        public void setVolumeFluctuationDetected(boolean volumeFluctuationDetected) { this.volumeFluctuationDetected = volumeFluctuationDetected; }
        
        public double getNonComplianceRate() { return nonComplianceRate; }
        public void setNonComplianceRate(double nonComplianceRate) { this.nonComplianceRate = nonComplianceRate; }
        
        public double getVolumeFluctuationRate() { return volumeFluctuationRate; }
        public void setVolumeFluctuationRate(double volumeFluctuationRate) { this.volumeFluctuationRate = volumeFluctuationRate; }
        
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }
        
        public long getErrorRecords() { return errorRecords; }
        public void setErrorRecords(long errorRecords) { this.errorRecords = errorRecords; }
        
        public long getPreviousDayRecords() { return previousDayRecords; }
        public void setPreviousDayRecords(long previousDayRecords) { this.previousDayRecords = previousDayRecords; }
        
        public String getReportFilePath() { return reportFilePath; }
        public void setReportFilePath(String reportFilePath) { this.reportFilePath = reportFilePath; }
        
        public List<String> getAlerts() { return alerts; }
        public void setAlerts(List<String> alerts) { this.alerts = alerts; }
    }

    /**
     * 执行数据质量检测
     * 
     * @param cleanedFilePath 清理后的文件路径
     * @param originalFilePath 原始文件路径
     * @param interfaceId 接口ID
     * @param dataDate 数据日期
     * @param outputDir 输出目录
     * @return 质量检测结果
     */
    public QualityCheckResult performQualityCheck(String cleanedFilePath, String originalFilePath, 
                                                 String interfaceId, String dataDate, String outputDir) {
        QualityCheckResult result = new QualityCheckResult();
        
        if (!vgopProperties.getDataQuality().getEnableQualityCheck()) {
            log.info("数据质量检测已禁用，跳过检测");
            return result;
        }

        try {
            log.info("开始执行数据质量检测，接口ID: {}, 数据日期: {}", interfaceId, dataDate);

            // 1. 计算总记录数和错误记录数
            long totalRecords = countFileLines(originalFilePath);
            long validRecords = countFileLines(cleanedFilePath);
            long errorRecords = totalRecords - validRecords;
            
            result.setTotalRecords(totalRecords);
            result.setErrorRecords(errorRecords);

            // 2. 不合规数据占比校验
            if (totalRecords > 0) {
                double nonComplianceRate = (double) errorRecords / totalRecords;
                result.setNonComplianceRate(nonComplianceRate);
                
                double threshold = vgopProperties.getDataQuality().getNonComplianceThreshold();
                if (nonComplianceRate > threshold) {
                    result.setNonComplianceThresholdExceeded(true);
                    String alertMsg = String.format("接口 %s 不合规数据占比超标: %.2f%% > %.2f%% (错误记录: %d, 总记录: %d)", 
                            interfaceId, nonComplianceRate * 100, threshold * 100, errorRecords, totalRecords);
                    result.getAlerts().add(alertMsg);
                    
                    // 记录系统告警
                    recordSystemAlert(interfaceId, "不合规数据占比超标", alertMsg, dataDate);
                    log.warn(alertMsg);
                } else {
                    log.info("接口 {} 不合规数据占比正常: %.2f%% (错误记录: {}, 总记录: {})", 
                            interfaceId, nonComplianceRate * 100, errorRecords, totalRecords);
                }
            }

            // 3. 数据量波动检测
            performVolumeFluctuationCheck(result, interfaceId, dataDate, totalRecords);

            // 4. 生成不合规数据Excel报告 - 改进逻辑，避免重复执行
            if (vgopProperties.getDataQuality().getGenerateNonComplianceReport() && errorRecords > 0) {
                // 等待一小段时间确保告警记录已经插入完成
                try {
                    Thread.sleep(100); // 100ms缓冲时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                String reportPath = generateNonComplianceReport(interfaceId, dataDate, outputDir);
                result.setReportFilePath(reportPath);
            } else if (errorRecords == 0) {
                log.info("接口 {} 无错误记录，跳过Excel报告生成", interfaceId);
            } else {
                log.info("接口 {} Excel报告生成已禁用，跳过报告生成", interfaceId);
            }

            log.info("数据质量检测完成，接口ID: {}, 总记录: {}, 错误记录: {}, 不合规率: %.2f%%", 
                    interfaceId, totalRecords, errorRecords, result.getNonComplianceRate() * 100);

        } catch (Exception e) {
            log.error("数据质量检测失败，接口ID: {}, 错误: {}", interfaceId, e.getMessage(), e);
            result.getAlerts().add("数据质量检测失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 执行数据量波动检测
     */
    private void performVolumeFluctuationCheck(QualityCheckResult result, String interfaceId, String dataDate, long currentRecords) {
        try {
            String previousDay = DateUtil.getPreviousDay(dataDate);
            long previousDayRecords = getHistoricalRecordCount(interfaceId, previousDay);
            result.setPreviousDayRecords(previousDayRecords);

            if (previousDayRecords == 0) {
                result.setVolumeFluctuationDetected(true);
                String alertMsg = String.format("接口 %s 前一日数据丢失，无法进行数据量波动检测 (当前: %d, 前一天: %d)", 
                        interfaceId, currentRecords, previousDayRecords);
                result.getAlerts().add(alertMsg);
                recordSystemAlert(interfaceId, "前一日数据丢失", alertMsg, dataDate);
                log.warn(alertMsg);
                return;
            }

            // 计算波动率
            double fluctuationRate = Math.abs((double) (currentRecords - previousDayRecords)) / previousDayRecords;
            result.setVolumeFluctuationRate(fluctuationRate);

            double threshold = vgopProperties.getDataQuality().getVolumeFluctuationThreshold();
            if (fluctuationRate > threshold) {
                result.setVolumeFluctuationDetected(true);
                String changeType = currentRecords > previousDayRecords ? "增长" : "下降";
                String alertMsg = String.format("接口 %s 数据量异常波动: %s %.2f%% > %.2f%% (当前: %d, 前一天: %d)", 
                        interfaceId, changeType, fluctuationRate * 100, threshold * 100, currentRecords, previousDayRecords);
                result.getAlerts().add(alertMsg);
                recordSystemAlert(interfaceId, "数据量异常波动", alertMsg, dataDate);
                log.warn(alertMsg);
            } else {
                log.info("接口 {} 数据量波动正常: %.2f%% (当前: {}, 前一天: {})", 
                        interfaceId, fluctuationRate * 100, currentRecords, previousDayRecords);
            }

        } catch (Exception e) {
            result.setVolumeFluctuationDetected(true);
            String alertMsg = String.format("接口 %s 历史数据缺失，无法进行数据量波动检测: %s", interfaceId, e.getMessage());
            result.getAlerts().add(alertMsg);
            recordSystemAlert(interfaceId, "历史数据缺失", alertMsg, dataDate);
            log.error(alertMsg, e);
        }
    }

    /**
     * 生成不合规数据Excel报告
     */
    private String generateNonComplianceReport(String interfaceId, String dataDate, String outputDir) throws Exception {
        String fileName = vgopProperties.getDataQuality().getNonComplianceReportTemplate()
                .replace("{dataDate}", dataDate);
        String reportPath = outputDir + "/" + fileName;

        log.info("开始生成不合规数据Excel报告: {}", reportPath);

        // 查询不合规数据记录 - 优先查询包含详细错误信息的记录
        String currentDateStr = ValidationAlert.getCurrentTimeString().substring(0, 8); // 当前日期YYYYMMDD
        
        // 首先尝试查询包含详细错误信息的数据校验失败记录
        List<ValidationAlert> alerts = validationAlertsMapper.findValidationErrorsByInterfaceAndDate(interfaceId, currentDateStr);
        
        // 如果当天没有找到详细记录，再尝试查询指定数据日期的详细记录
        if (alerts.isEmpty() && !currentDateStr.equals(dataDate)) {
            alerts = validationAlertsMapper.findValidationErrorsByInterfaceAndDate(interfaceId, dataDate);
        }
        
        // 如果仍然没有找到，回退到普通查询并过滤
        if (alerts.isEmpty()) {
            alerts = validationAlertsMapper.findByInterfaceAndDate(interfaceId, currentDateStr);
            if (alerts.isEmpty() && !currentDateStr.equals(dataDate)) {
                alerts = validationAlertsMapper.findByInterfaceAndDate(interfaceId, dataDate);
            }
            
            // 过滤出数据校验失败类型的告警（排除系统质量检测告警）
            alerts = alerts.stream()
                    .filter(alert -> "数据校验失败".equals(alert.getAlertType()))
                    .filter(alert -> alert.getFieldErrors() != null && !alert.getFieldErrors().trim().isEmpty())
                    .collect(java.util.stream.Collectors.toList());
        }
        
        if (alerts.isEmpty()) {
            log.info("没有找到不合规数据记录，跳过Excel报告生成");
            return null;
        }
        
        log.info("找到不合规数据记录 {} 条，开始分析字段错误信息", alerts.size());
        
        // 记录字段错误信息的统计
        int withFieldErrors = 0;
        int withErrorData = 0;
        for (ValidationAlert alert : alerts) {
            if (alert.getFieldErrors() != null && !alert.getFieldErrors().trim().isEmpty()) {
                withFieldErrors++;
            }
            if (alert.getErrorData() != null && !alert.getErrorData().trim().isEmpty()) {
                withErrorData++;
            }
        }
        log.info("记录统计: {} 条包含fieldErrors, {} 条包含errorData", withFieldErrors, withErrorData);

        // 限制记录数量
        int maxRows = vgopProperties.getDataQuality().getMaxReportRows();
        if (alerts.size() > maxRows) {
            alerts = alerts.subList(0, maxRows);
            log.warn("不合规数据记录超过限制 {}，只导出前 {} 条记录", maxRows, maxRows);
        }

        // 创建Excel工作簿
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("不合规数据记录");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"序号", "文件名", "行号", "错误字段", "字段值", "错误信息", "严重程度", "发生时间"};
            
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充数据行
            for (int i = 0; i < alerts.size(); i++) {
                ValidationAlert alert = alerts.get(i);
                Row dataRow = sheet.createRow(i + 1);
                
                dataRow.createCell(0).setCellValue(i + 1);
                dataRow.createCell(1).setCellValue(alert.getFileName() != null ? alert.getFileName() : "");
                dataRow.createCell(2).setCellValue(alert.getLineNumber() != null ? alert.getLineNumber() : 0);
                
                // 解析错误字段和字段值
                String[] fieldInfo = parseFieldErrorInfo(alert);
                dataRow.createCell(3).setCellValue(fieldInfo[0]); // 错误字段
                dataRow.createCell(4).setCellValue(fieldInfo[1]); // 字段值
                
                dataRow.createCell(5).setCellValue(alert.getAlertMessage() != null ? alert.getAlertMessage() : "");
                dataRow.createCell(6).setCellValue(alert.getAlertLevel() != null ? alert.getAlertLevel().toString() : "");
                dataRow.createCell(7).setCellValue(DateStringUtil.formatForDisplay(alert.getAlertTime()));
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream(reportPath)) {
                workbook.write(fileOut);
            }
        }

        log.info("不合规数据Excel报告生成完成: {}, 记录数: {}", reportPath, alerts.size());
        return reportPath;
    }

    /**
     * 计算文件行数
     */
    private long countFileLines(String filePath) throws IOException {
        if (filePath == null || !Files.exists(Paths.get(filePath))) {
            return 0;
        }

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath), StandardCharsets.UTF_8)) {
            long lines = 0;
            while (reader.readLine() != null) {
                lines++;
            }
            return lines;
        }
    }

    /**
     * 获取历史记录数量
     * 这里需要根据实际业务逻辑实现，可能需要查询数据库或文件系统
     */
    private long getHistoricalRecordCount(String interfaceId, String dataDate) {
        // TODO: 实现从数据库或文件系统查询历史记录数量的逻辑
        // 这里先返回模拟数据，实际实现需要根据业务需求调整
        try {
            // 可以查询数据库中的历史记录
            // 或者读取前一天生成的文件来计算行数
            return 10000; // 模拟数据
        } catch (Exception e) {
            log.error("获取历史记录数量失败，接口ID: {}, 数据日期: {}, 错误: {}", interfaceId, dataDate, e.getMessage());
            throw new RuntimeException("历史数据缺失", e);
        }
    }

    /**
     * 解析字段错误信息
     * 从ValidationAlert的fieldErrors和errorData中提取错误字段名称和字段值
     * 
     * @param alert 告警对象
     * @return String数组，[0]为错误字段名，[1]为字段值
     */
    private String[] parseFieldErrorInfo(ValidationAlert alert) {
        String[] result = new String[]{"", ""};
        
        try {
            log.debug("开始解析字段错误信息 - alertId: {}, fileName: {}, lineNumber: {}", 
                    alert.getAlertId(), alert.getFileName(), alert.getLineNumber());
            
            // 优先从fieldErrors字段解析
            if (alert.getFieldErrors() != null && !alert.getFieldErrors().trim().isEmpty()) {
                log.debug("解析fieldErrors: {}", alert.getFieldErrors());
                parseFromFieldErrors(alert.getFieldErrors(), result);
                log.debug("fieldErrors解析结果 - 字段名: {}, 字段值: {}", result[0], result[1]);
            }
            
            // 从errorData字段解析字段值
            if (alert.getErrorData() != null && !alert.getErrorData().trim().isEmpty()) {
                log.debug("解析errorData: {}", alert.getErrorData());
                parseFromErrorData(alert.getErrorData(), result);
                log.debug("errorData解析结果 - 字段名: {}, 字段值: {}", result[0], result[1]);
            }
            
            // 如果还没有字段名，尝试从告警消息中提取
            if (result[0].isEmpty() && alert.getAlertMessage() != null) {
                log.debug("从告警消息解析字段名: {}", alert.getAlertMessage());
                parseFromAlertMessage(alert.getAlertMessage(), result);
                log.debug("告警消息解析结果 - 字段名: {}", result[0]);
            }
            
            // 确保结果不为null
            if (result[0] == null) result[0] = "";
            if (result[1] == null) result[1] = "";
            
            log.debug("最终解析结果 - 字段名: '{}', 字段值: '{}'", result[0], result[1]);
            
        } catch (Exception e) {
            log.debug("解析字段错误信息失败: {}", e.getMessage());
            // 发生异常时返回默认值
            result[0] = "解析失败";
            result[1] = "无法解析";
        }
        
        return result;
    }
    
    /**
     * 从fieldErrors字段解析信息
     */
    private void parseFromFieldErrors(String fieldErrors, String[] result) {
        if (fieldErrors.contains("|")) {
            // 处理 "字段名|字段值|错误信息" 格式
            String[] parts = fieldErrors.split("\\|");
            if (parts.length >= 2) {
                result[0] = parts[0].trim(); // 字段名
                result[1] = parts[1].trim(); // 字段值
            }
        } else if (fieldErrors.contains(":")) {
            // 处理 "fieldName:错误信息" 或 "fieldName:value" 格式
            String[] parts = fieldErrors.split(":", 2);
            if (parts.length >= 1) {
                result[0] = parts[0].trim(); // 字段名
                if (parts.length == 2 && parts[1].trim().length() < 100) {
                    // 如果冒号后面的内容较短，可能是字段值
                    result[1] = parts[1].trim();
                }
            }
        } else if (fieldErrors.contains("=")) {
            // 处理 "fieldName=value" 格式
            String[] parts = fieldErrors.split("=", 2);
            if (parts.length >= 1) {
                result[0] = parts[0].trim(); // 字段名
                if (parts.length == 2) {
                    result[1] = parts[1].trim(); // 字段值
                }
            }
        } else {
            // 如果无法解析，直接使用fieldErrors作为字段名
            result[0] = fieldErrors.length() > 50 ? fieldErrors.substring(0, 50) + "..." : fieldErrors;
        }
    }
    
    /**
     * 从errorData字段解析信息
     */
    private void parseFromErrorData(String errorData, String[] result) {
        if (errorData.contains("fieldName=") || errorData.contains("field=")) {
            // 处理包含fieldName或field键的情况
            String[] pairs = errorData.split("[,;&]");
            for (String pair : pairs) {
                if (pair.contains("=")) {
                    String[] kv = pair.split("=", 2);
                    if (kv.length == 2) {
                        String key = kv[0].trim();
                        String value = kv[1].trim();
                        
                        if (key.toLowerCase().contains("field") && result[0].isEmpty()) {
                            result[0] = value;
                        } else if (key.toLowerCase().contains("value") && result[1].isEmpty()) {
                            result[1] = value.length() > 100 ? value.substring(0, 100) + "..." : value;
                        } else if (result[0].isEmpty() && isFieldName(key)) {
                            result[0] = key;
                        } else if (result[1].isEmpty() && isFieldValue(key, value)) {
                            result[1] = value.length() > 100 ? value.substring(0, 100) + "..." : value;
                        }
                    }
                }
            }
        } else if (errorData.contains("=")) {
            // 处理一般的 "key=value" 格式
            String[] pairs = errorData.split("[,;&]");
            for (String pair : pairs) {
                if (pair.contains("=")) {
                    String[] kv = pair.split("=", 2);
                    if (kv.length == 2) {
                        String key = kv[0].trim();
                        String value = kv[1].trim();
                        
                        // 如果字段名为空，尝试从errorData中获取
                        if (result[0].isEmpty() && isFieldName(key)) {
                            result[0] = key;
                        }
                        
                        // 查找可能的字段值
                        if (result[1].isEmpty() && isFieldValue(key, value)) {
                            result[1] = value.length() > 100 ? value.substring(0, 100) + "..." : value;
                        }
                    }
                }
            }
        } else {
            // 如果无法解析结构化数据，截取部分作为字段值显示
            if (result[1].isEmpty()) {
                result[1] = errorData.length() > 100 ? errorData.substring(0, 100) + "..." : errorData;
            }
        }
    }
    
    /**
     * 从告警消息中解析信息
     */
    private void parseFromAlertMessage(String message, String[] result) {
        // 查找可能的字段名模式
        if (message.contains("字段") && message.contains("格式不正确")) {
            // 提取 "XXX字段格式不正确" 中的字段名
            int fieldIndex = message.indexOf("字段");
            if (fieldIndex > 0) {
                String beforeField = message.substring(0, fieldIndex);
                String[] words = beforeField.split("\\s+");
                if (words.length > 0) {
                    result[0] = words[words.length - 1];
                }
            }
        } else if (message.contains("numstate") || message.contains("phoneNumber") || 
                   message.contains("CallEndTime") || message.contains("openingtime") ||
                   message.contains("Begintime")) {
            // 从常见的VGOP字段错误消息中提取字段名
            String[] commonFields = {"numstate", "phoneNumber", "CallEndTime", "openingtime", "Begintime"};
            for (String field : commonFields) {
                if (message.contains(field)) {
                    result[0] = field;
                    break;
                }
            }
        }
    }
    
    /**
     * 判断是否为字段名
     */
    private boolean isFieldName(String key) {
        if (key == null || key.trim().isEmpty()) {
            return false;
        }
        
        // VGOP系统常见字段名模式
        String[] fieldPatterns = {
            // 电话相关
            "phone", "mobile", "phonenumber", "msisdn", "callingnumber", "callednumber",
            // 设备相关  
            "imei", "imsi", "lac", "ci", "cellid",
            // 时间相关
            "time", "date", "begintime", "endtime", "callendtime", "openingtime", "duration",
            // 身份相关
            "id", "userid", "customerid", "accountid", "number", "serialnumber",
            // 状态相关
            "status", "state", "numstate", "callstate", "result",
            // 业务相关
            "name", "code", "type", "service", "channel", "province", "city",
            "value", "amount", "count", "fee", "charge", "balance",
            // 网络相关
            "ip", "port", "protocol", "url", "domain", "apn",
            // 位置相关
            "location", "longitude", "latitude", "address"
        };
        
        String lowerKey = key.toLowerCase().trim();
        
        // 精确匹配字段名
        for (String pattern : fieldPatterns) {
            if (lowerKey.equals(pattern) || lowerKey.contains(pattern)) {
                return true;
            }
        }
        
        // 匹配常见字段命名规则（驼峰、下划线等）
        if (lowerKey.matches("^[a-z][a-z0-9_]*$") && lowerKey.length() >= 3 && lowerKey.length() <= 30) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断是否为有效的字段值
     */
    private boolean isFieldValue(String key, String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        // 排除一些明显不是字段值的内容
        if (value.equalsIgnoreCase("null") || 
            value.equalsIgnoreCase("undefined") ||
            value.length() < 1) {
            return false;
        }
        
        return true;
    }

    /**
     * 记录系统告警
     */
    private void recordSystemAlert(String interfaceId, String alertType, String alertMessage, String dataDate) {
        try {
            ValidationAlert alert = ValidationAlert.builder()
                    .alertTime(ValidationAlert.getCurrentTimeString())
                    .interfaceName(interfaceId)
                    .alertType(alertType)
                    .alertLevel(ValidationAlert.AlertLevel.WARNING)
                    .alertMessage(alertMessage)
                    .fileName("系统质量检测")
                    .lineNumber(0L)
                    .errorData(String.format("dataDate=%s,interfaceId=%s", dataDate, interfaceId))
                    .status(ValidationAlert.AlertStatus.NEW)
                    .build();

            validationAlertsMapper.insert(alert);
            log.debug("系统告警记录已保存: {}", alertMessage);
        } catch (Exception e) {
            log.error("记录系统告警失败: {}", e.getMessage(), e);
        }
    }
} 