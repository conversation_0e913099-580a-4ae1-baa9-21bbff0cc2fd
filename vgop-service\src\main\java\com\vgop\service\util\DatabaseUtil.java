package com.vgop.service.util;

import com.vgop.service.config.DatabaseType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;

/**
 * 数据库工具类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class DatabaseUtil {
    
    private final DataSource primaryDataSource;
    private volatile DatabaseType databaseType;
    
    @Autowired
    public DatabaseUtil(@Qualifier("primaryDataSource") DataSource primaryDataSource) {
        this.primaryDataSource = primaryDataSource;
    }
    
    /**
     * 获取当前数据库类型
     */
    public DatabaseType getDatabaseType() {
        if (databaseType == null) {
            synchronized (this) {
                if (databaseType == null) {
                    databaseType = detectDatabaseType();
                }
            }
        }
        return databaseType;
    }
    
    /**
     * 检测数据库类型
     */
    private DatabaseType detectDatabaseType() {
        try (Connection connection = primaryDataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            String url = metaData.getURL();
            String productName = metaData.getDatabaseProductName();
            
            log.info("检测到数据库产品: {}, URL: {}", productName, url);
            
            // 优先通过URL判断
            DatabaseType type = DatabaseType.fromJdbcUrl(url);
            if (type != DatabaseType.UNKNOWN) {
                return type;
            }
            
            // 其次通过产品名判断
            if (productName != null) {
                String lowerProductName = productName.toLowerCase();
                if (lowerProductName.contains("informix")) {
                    return DatabaseType.INFORMIX;
                } else if (lowerProductName.contains("gbase")) {
                    return DatabaseType.GBASE;
                } else if (lowerProductName.contains("h2")) {
                    return DatabaseType.H2;
                }
            }
            
            return DatabaseType.UNKNOWN;
        } catch (SQLException e) {
            log.error("检测数据库类型失败", e);
            return DatabaseType.UNKNOWN;
        }
    }
    
    /**
     * 检查当前数据库是否支持UNLOAD命令
     */
    public boolean supportsUnload() {
        return getDatabaseType().supportsUnload();
    }
    
    /**
     * 构建UNLOAD TO语句
     * 
     * @param query SQL查询语句
     * @param filePath 输出文件路径
     * @param delimiter 字段分隔符
     * @return 完整的UNLOAD语句
     */
    public String buildUnloadStatement(String query, String filePath, String delimiter) {
        DatabaseType dbType = getDatabaseType();
        
        if (!dbType.supportsUnload()) {
            throw new UnsupportedOperationException("当前数据库 " + dbType.getName() + " 不支持UNLOAD命令");
        }
        
        // 构建UNLOAD TO语句，与shell脚本语法保持一致
        StringBuilder sql = new StringBuilder();
        
        // 构建UNLOAD TO语句（不包含set lock mode）
        sql.append("unload to ").append(filePath).append(" ");
        
        // 添加delimiter（默认使用|分隔符）
        String sep = (delimiter != null && !delimiter.isEmpty()) ? delimiter : "|";
        sql.append("delimiter '").append(sep).append("' ");
        
        // 添加查询语句，不包含分号
        sql.append(query);
        
        return sql.toString();
    }
    
    /**
     * 获取设置锁模式的SQL语句
     * 
     * @return 锁模式设置语句
     */
    public String getLockModeStatement() {
        return "set lock mode to wait 10;";
    }
    
    /**
     * 转义文件路径（针对不同数据库）
     */
    public String escapeFilePath(String filePath) {
        // Informix和GBase使用相同的路径格式
        return filePath.replace("\\", "/");
    }
    
    /**
     * 获取数据库名称（用于dbaccess命令）
     * 
     * @return 数据库名称
     */
    public String getDatabaseName() {
        try (Connection connection = primaryDataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            String url = metaData.getURL();
            
            // 从JDBC URL中提取数据库名称
            // 例如: jdbc:informix-sqli://localhost:9088/vgop:INFORMIXSERVER=ol_informix1410
            // 或者: jdbc:gbasedbt-sqli://localhost:9088/vgop
            if (url.contains("://")) {
                String[] parts = url.split("://");
                if (parts.length > 1) {
                    String hostAndDb = parts[1];
                    if (hostAndDb.contains("/")) {
                        String[] dbParts = hostAndDb.split("/");
                        if (dbParts.length > 1) {
                            String dbName = dbParts[1];
                            // 移除参数部分（如果有的话）
                            if (dbName.contains(":")) {
                                dbName = dbName.split(":")[0];
                            }
                            if (dbName.contains("?")) {
                                dbName = dbName.split("\\?")[0];
                            }
                            log.debug("从URL中提取的数据库名称: {}", dbName);
                            return dbName;
                        }
                    }
                }
            }
            
            // 如果无法从URL提取，尝试使用catalog name
            String catalogName = metaData.getUserName();
            if (catalogName != null && !catalogName.isEmpty()) {
                log.debug("使用catalog名称作为数据库名称: {}", catalogName);
                return catalogName;
            }
            
            // 默认返回通用数据库名称
            log.warn("无法确定数据库名称，使用默认值");
            return "vgop";
            
        } catch (SQLException e) {
            log.error("获取数据库名称失败", e);
            return "vgop";
        }
    }
} 