# 数据字典核心分析

本文档是 `数据字典.md` 的精简分析版本，重点关注核心表、关键字段及其关联，为后续开发和数据分析提供快速索引。

## 核心业务表

### 1. `mcn_user_major` - 用户主号码信息表

-   **用途**: 存储用户主号码（真实手机号）的基础信息。
-   **主键**: `phonenumber` (主号码).
-   **关键字段**:
    -   `phonenumber`: 用户真实手机号码，是整个业务系统的核心ID。
    -   `phonestate`: 主号码业务状态（'0'正常, '1'暂停），决定主号能否使用业务。
    -   `provinceid`: 主号码归属省份ID，用于地域划分和统计。
    -   `openingtime`: 开户时间，用于分析用户生命周期。

### 2. `mcn_user_minor` - 用户副号码信息表

-   **用途**: 存储用户副号码信息，以及主副号的绑定关系。这是系统的核心表之一。
-   **主键**: `mcnnumber` (副号码).
-   **外键**: `phonenumber` -> `mcn_user_major.phonenumber`.
-   **关键字段**:
    -   `mcnnumber`: 副号码。
    -   `phonenumber`: 关联的主号码。
    -   `business`: 副号码业务状态（'0'正常, '1'预开户, '2'申请中...），核心状态字段。
    -   `shutdown`: 副号码停机标识（'0'正常, '1'停机, '2'销号...），重要状态字段。
    -   `mcnnature`: 副号码类型（'0'虚拟, '1'实体）。
    -   `mcntype`: 用户类别（'0'普通个人用户）。
    -   `numstate`: 副号功能状态 (如 '1000000')，一个复合状态字段，按位解析：
        -   第1位: 逻辑开关机状态。
        -   第2位: 语音黑白名单状态。
        -   ...
    -   `ServID`: 业务代码，关联用户订购的套餐。
    -   `Begintime` / `Endtime`: 套餐生/失效时间。

### 3. `mcn_oplog` - 用户操作成功记录表

-   **用途**: 记录用户所有成功的业务操作日志，如申请/注销副号、开关机等。是行为分析和问题排查的重要数据源。
-   **主键**: `streamnumber`.
-   **关键字段**:
    -   `phonenumber`: 操作关联的主号码。
    -   `hmcnnumber`: 操作涉及的副号码。
    -   `optype`: 操作类型代码（'s'申请, 'z'注销, 'kj'开机, 'gj'关机...），极为重要，用于分析用户行为。
    -   `optime`: 操作发生时间。
    -   `opmanner`: 操作渠道（'0'客服网站, '1'短信, '4'客户端...），用于分析渠道偏好。

### 4. `mcn_contralog` - 正常话单表 (CDR)

-   **用途**: 记录使用副号进行的通话记录。
-   **主键**: `streamNumber`.
-   **关键字段**:
    -   `callType`: 呼叫类型（'0'主叫, '1'被叫）。
    -   `callingPartyNumber`: 主叫号码。
    -   `calledPartyNumber`: 被叫号码。
    -   `mcnnumber`: 本次通话使用的副号码。
    -   `CallBeginTime` / `CallEndTime` / `CallDuration`: 通话开始/结束/时长。
    -   `Reason`: 呼叫未接通原因代码，用于分析通话成功率和失败原因。

### 5. `mcn_smslog` - 短信/彩信日志表

-   **用途**: 记录使用副号收发短信和接收彩信的话单。
-   **主键**: `streamnumber`.
-   **关键字段**:
    -   `chargetype`: 话单类型（'0'发短信, '1'收短信, '2'收彩信）。
    -   `phonenumber`: 主号码。
    -   `mcnnumber`: 副号码。
    -   `sendorreceNum`: 对端号码。
    -   `optime`: 收发时间。
    -   `msgResult`: 转发状态/失败原因代码，用于分析短信成功率和失败原因。

## 辅助与配置表

### 1. `bms_vgop_revtimes` - VGOP重传序号表

-   **用途**: 管理数据文件重传的次数和状态。
-   **关键字段**: `tmpfilename`, `times`.

### 2. `bossprovince` - BOSS机构与省份对应表

-   **用途**: 提供BOSS侧机构ID到平台标准省份ID的映射。
-   **关键字段**: `bossid`, `Provinceid`.

### 3. `vgop_channel` / `vgop_shutdown` / `vgop_mcntype`

-   **用途**: 分别是渠道、停机标识、用户类型的代码表，提供代码到名称的映射。
-   **模式**: `*code` (代码), `*name` (名称)。

## 表间关系总结

-   核心是 **主号码 (`mcn_user_major`)** 和 **副号码 (`mcn_user_minor`)** 的一对多关系，通过 `phonenumber` 关联。
-   所有 **操作日志 (`mcn_oplog`)** 和 **话单 (`mcn_contralog`, `mcn_smslog`)** 都通过 `phonenumber` 和 `mcnnumber` 与主副号码信息关联起来。
-   配置表 (如 `bossprovince`, `vgop_channel`) 用于解码主数据表中的代码字段。 