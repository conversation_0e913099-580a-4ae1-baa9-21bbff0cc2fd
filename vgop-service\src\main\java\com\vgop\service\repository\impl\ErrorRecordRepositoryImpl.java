package com.vgop.service.repository.impl;

import com.vgop.service.repository.ErrorRecordRepository;
import com.vgop.service.util.CharsetUtil;
import com.vgop.service.validation.ErrorRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 错误记录仓库实现类
 * 增强字符编码处理，防止数据库字符集转换错误
 */
@Slf4j
@Repository
public class ErrorRecordRepositoryImpl implements ErrorRecordRepository {
    
    /**
     * JDBC模板
     */
    private final JdbcTemplate jdbcTemplate;
    
    /**
     * 错误记录行映射器
     */
    private final RowMapper<ErrorRecord> rowMapper = (rs, rowNum) -> ErrorRecord.builder()
            .id(rs.getLong("id"))
            .interfaceName(rs.getString("interface_name"))
            .fileName(rs.getString("file_name"))
            .dataDate(rs.getString("data_date"))
            .rowNumber(rs.getLong("row_number"))
            .fieldName(rs.getString("field_name"))
            .fieldValue(rs.getString("field_value"))
            .errorMessage(rs.getString("error_message"))
            .ruleId(rs.getString("rule_id"))
            .ruleName(rs.getString("rule_name"))
            .severity(rs.getString("severity"))
            .createTime(rs.getTimestamp("create_time").toLocalDateTime())
            .build();
    
    @Autowired
    public ErrorRecordRepositoryImpl(@Qualifier("primaryJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    @Override
    public ErrorRecord save(ErrorRecord errorRecord) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        
        try {
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(
                        "INSERT INTO vgop_error_records (interface_name, file_name, data_date, row_number, " +
                        "field_name, field_value, error_message, rule_id, rule_name, severity, create_time) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        Statement.RETURN_GENERATED_KEYS);
                
                // 字符编码安全处理：对所有字符串字段进行编码清理
                ps.setString(1, prepareSafeString(errorRecord.getInterfaceName(), 100));
                ps.setString(2, prepareSafeString(errorRecord.getFileName(), 255));
                ps.setString(3, prepareSafeString(errorRecord.getDataDate(), 20));
                ps.setLong(4, errorRecord.getRowNumber());
                ps.setString(5, prepareSafeString(errorRecord.getFieldName(), 100));
                ps.setString(6, prepareSafeString(errorRecord.getFieldValue(), 1000)); // 字段值可能较长
                ps.setString(7, prepareSafeString(errorRecord.getErrorMessage(), 500));
                ps.setString(8, prepareSafeString(errorRecord.getRuleId(), 50));
                ps.setString(9, prepareSafeString(errorRecord.getRuleName(), 100));
                ps.setString(10, prepareSafeString(errorRecord.getSeverity(), 20));
                ps.setTimestamp(11, Timestamp.valueOf(
                        errorRecord.getCreateTime() != null ? errorRecord.getCreateTime() : LocalDateTime.now()));
                
                return ps;
            }, keyHolder);
            
            Number key = keyHolder.getKey();
            if (key != null) {
                errorRecord.setId(key.longValue());
            }
            
            log.debug("成功保存错误记录: 接口={}, 文件={}, 行号={}", 
                    errorRecord.getInterfaceName(), errorRecord.getFileName(), errorRecord.getRowNumber());
            
        } catch (DataAccessException e) {
            // 特殊处理字符编码错误
            if (isCharsetError(e)) {
                log.warn("检测到字符编码错误，尝试使用清理后的数据重新保存: {}", e.getMessage());
                return saveWithFallback(errorRecord);
            } else {
                log.error("保存错误记录失败: 接口={}, 文件={}, 行号={}", 
                        errorRecord.getInterfaceName(), errorRecord.getFileName(), errorRecord.getRowNumber(), e);
                throw e;
            }
        }
        
        return errorRecord;
    }
    
    /**
     * 准备数据库安全字符串
     * 对字符串进行编码清理和长度控制
     */
    private String prepareSafeString(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        
        // 使用字符编码工具类进行安全处理
        String safe = CharsetUtil.prepareDatabaseString(input, maxLength);
        
        // 记录编码清理过程（仅在调试模式下）
        if (log.isDebugEnabled() && !input.equals(safe)) {
            String encoding = CharsetUtil.detectEncoding(input);
            log.debug("字符编码清理: [{}] {} -> {}", encoding, input, safe);
        }
        
        return safe;
    }
    
    /**
     * 检查是否为字符编码相关的数据库错误
     */
    private boolean isCharsetError(DataAccessException e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        // 检查Informix/GBase数据库的字符编码错误特征
        return message.contains("-23103") ||  // Code-set conversion function failed
               message.contains("IX000") ||   // Informix错误码
               message.contains("Code-set conversion function failed") ||
               message.contains("illegal sequence") ||
               message.contains("invalid value") ||
               message.contains("Character set conversion");
    }
    
    /**
     * 字符编码错误时的后备保存方法
     * 使用更激进的字符清理策略
     */
    private ErrorRecord saveWithFallback(ErrorRecord errorRecord) {
        log.info("尝试使用后备方案保存错误记录");
        
        try {
            // 创建一个副本，使用更激进的清理策略
            ErrorRecord fallbackRecord = ErrorRecord.builder()
                    .interfaceName(cleanStringForFallback(errorRecord.getInterfaceName(), 100))
                    .fileName(cleanStringForFallback(errorRecord.getFileName(), 255))
                    .dataDate(cleanStringForFallback(errorRecord.getDataDate(), 20))
                    .rowNumber(errorRecord.getRowNumber())
                    .fieldName(cleanStringForFallback(errorRecord.getFieldName(), 100))
                    .fieldValue(cleanStringForFallback(errorRecord.getFieldValue(), 1000))
                    .errorMessage(cleanStringForFallback(errorRecord.getErrorMessage(), 500))
                    .ruleId(cleanStringForFallback(errorRecord.getRuleId(), 50))
                    .ruleName(cleanStringForFallback(errorRecord.getRuleName(), 100))
                    .severity(cleanStringForFallback(errorRecord.getSeverity(), 20))
                    .createTime(errorRecord.getCreateTime() != null ? errorRecord.getCreateTime() : LocalDateTime.now())
                    .build();
            
            // 使用递归调用，但添加保护避免无限递归
            return save(fallbackRecord);
            
        } catch (Exception e) {
            log.error("后备保存方案也失败，记录错误信息到日志", e);
            
            // 最后的保险措施：只记录关键信息到日志
            log.error("无法保存的错误记录 - 接口: {}, 文件: {}, 行号: {}, 字段: {}, 错误: {}", 
                    errorRecord.getInterfaceName(), 
                    errorRecord.getFileName(), 
                    errorRecord.getRowNumber(), 
                    errorRecord.getFieldName(), 
                    errorRecord.getErrorMessage());
            
            // 返回一个最小化的错误记录
            return ErrorRecord.builder()
                    .id(-1L) // 标记为保存失败
                    .interfaceName(errorRecord.getInterfaceName())
                    .fileName(errorRecord.getFileName())
                    .rowNumber(errorRecord.getRowNumber())
                    .fieldName(errorRecord.getFieldName())
                    .errorMessage("ENCODING_ERROR: " + errorRecord.getErrorMessage())
                    .createTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 后备字符串清理方法（更激进的策略）
     * 只保留安全的ASCII字符
     */
    private String cleanStringForFallback(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        
        // 更激进的清理：只保留基本的ASCII字符和常用汉字范围
        String cleaned = input.replaceAll("[^\\x20-\\x7E\\u4e00-\\u9fa5]", "");
        
        // 长度限制
        if (cleaned.length() > maxLength) {
            cleaned = cleaned.substring(0, maxLength);
        }
        
        log.debug("后备字符串清理: {} -> {}", input, cleaned);
        return cleaned;
    }
    
    @Override
    public List<ErrorRecord> findByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate, int page, int size) {
        
        int offset = page * size;
        
        return jdbcTemplate.query(
                "SELECT * FROM vgop_error_records " +
                "WHERE interface_name = ? AND file_name = ? AND data_date = ? " +
                "ORDER BY row_number ASC, field_name ASC " +
                "LIMIT ? OFFSET ?",
                rowMapper,
                interfaceName, fileName, dataDate, size, offset);
    }
    
    @Override
    public long countByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate) {
        
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM vgop_error_records " +
                "WHERE interface_name = ? AND file_name = ? AND data_date = ?",
                Long.class,
                interfaceName, fileName, dataDate);
    }
    
    @Override
    public int deleteByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate) {
        
        return jdbcTemplate.update(
                "DELETE FROM vgop_error_records " +
                "WHERE interface_name = ? AND file_name = ? AND data_date = ?",
                interfaceName, fileName, dataDate);
    }
} 