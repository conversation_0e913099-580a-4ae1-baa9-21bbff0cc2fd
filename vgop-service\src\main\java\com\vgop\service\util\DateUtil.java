package com.vgop.service.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日期工具类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class DateUtil {
    
    public static final DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter YYYYMMDDHHMMSS = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter YYYYMM = DateTimeFormatter.ofPattern("yyyyMM");
    public static final DateTimeFormatter STANDARD_DATETIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private DateUtil() {
        // 工具类不允许实例化
    }
    
    /**
     * 获取昨天的日期（YYYYMMDD格式）
     */
    public static String getYesterday() {
        return LocalDate.now().minusDays(1).format(YYYYMMDD);
    }
    
    /**
     * 获取指定日期的前一天（YYYYMMDD格式）
     * 
     * @param dateStr 日期字符串，格式为YYYYMMDD
     * @return 前一天的日期字符串
     */
    public static String getPreviousDay(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr, YYYYMMDD);
        return date.minusDays(1).format(YYYYMMDD);
    }
    
    /**
     * 获取今天的日期（YYYYMMDD格式）
     */
    public static String getToday() {
        return LocalDate.now().format(YYYYMMDD);
    }
    
    /**
     * 获取上个月（YYYYMM格式）
     */
    public static String getLastMonth() {
        return LocalDate.now().minusMonths(1).format(YYYYMM);
    }
    
    /**
     * 格式化日期时间
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime.format(YYYYMMDDHHMMSS);
    }
    
    /**
     * 获取指定日期的开始时间（000000）
     */
    public static String getStartTime(String date) {
        return date + "000000";
    }
    
    /**
     * 获取指定日期的结束时间（下一天的000000）
     */
    public static String getEndTime(String date) {
        LocalDate localDate = LocalDate.parse(date, YYYYMMDD);
        return localDate.plusDays(1).format(YYYYMMDD) + "000000";
    }
} 