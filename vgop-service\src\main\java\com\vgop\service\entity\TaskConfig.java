package com.vgop.service.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 任务配置实体类
 * 对应Shell脚本的输入参数配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TaskConfig {
    
    /**
     * 动作实例ID (MmeActionInsId)
     */
    private String actionInstanceId;
    
    /**
     * 镜像路径 (MmeImagePath)
     */
    private String imagePath;
    
    /**
     * 日期ID (MmeDateId)
     */
    private String dateId;
    
    /**
     * 跟踪标志 (TraceFlag)
     */
    private String traceFlag;
    
    /**
     * 源数据库名 (SrcDBName)
     */
    private String sourceDbName;
    
    /**
     * 列分隔符 (ColSep)
     */
    private String columnSeparator;
    
    /**
     * 任务类型：DAY/MONTH
     */
    private TaskType taskType;
    
    /**
     * 任务状态
     */
    private TaskStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    public enum TaskType {
        DAY("日统计"),
        MONTH("月统计");
        
        private final String description;
        
        TaskType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum TaskStatus {
        PENDING("待执行"),
        RUNNING("执行中"),
        SUCCESS("成功"),
        FAILED("失败");
        
        private final String description;
        
        TaskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 