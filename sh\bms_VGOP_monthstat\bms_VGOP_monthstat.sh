#!/bin/sh


# MmeActionInsId Mme{ImagePath} {MmeDate}Id 

# SrcDBName,ColSep
#

echo $#
while [ $# -gt 0 ] ; 
   do
     echo "$1"
     eval $1
     shift
   done


ImagePath=$(cd $MmeImagePath;pwd)

LogPath=${ImagePath}/log
if [ ! -d ${LogPath} ]; then
    mkdir ${LogPath}
fi
LogName=${LogPath}/../../user

modulepath=${LogPath}/../../../module/bms_VGOP_monthstat

echo "${modulepath}/VGOP1-R2.10-24201month.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24201month.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "./VGOP1-R2.10-24202month.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24202month.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "./VGOP1-R2.10-24204.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24204.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "./VGOP1-R2.10-24207month.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24207month.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
