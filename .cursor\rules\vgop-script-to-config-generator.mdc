---
description: 
globs: 
alwaysApply: false
---
# VGOP脚本配置生成规则

## 总体流程

当用户提供VGOP Shell脚本时，按以下步骤生成配置：

### 1. 脚本分析步骤
1. **提取基本信息**：
   - 接口ID (如：24202)
   - 接口名 (如：VGOP1-R2.10-24202)
   - 文件命名模式
   - SQL查询语句

2. **分析脚本逻辑**：
   - WHERE条件提取
   - 字段转换逻辑 (如：case when语句)
   - 固定值处理
   - 文件分割参数 (如：2000000行)

3. **确定任务类型**：
   - daily: 日接口
   - monthly: 月接口

### 2. 配置生成步骤

#### 任务配置模板
```yaml
# 任务: {interfaceId} - {description}
- interfaceId: "{interfaceId}"
  interfaceName: "{interfaceName}"
  enabled: true
  taskType: "{taskType}" # daily或monthly
  export:
    sqlTemplate: "{extractedSQL}"
    tempFileNameTemplate: "{prefix}_{dataDate}_{interfaceName}.unl"
    outputFileNameTemplate: "{prefix}_{dataDate}_{interfaceName}_{revTimes}_{fileNum}.dat"
    verfFileNameTemplate: "{prefix}_{dataDate}_{interfaceName}_{revTimes}.verf"
    maxLinesPerFile: {maxLines} # 从脚本提取，默认2000000
```

#### 校验规则配置模板
```yaml
validation:
  interfaces:
    '{interfaceName}':
      delimiter: "|"
      headerLine: false
      fields:
        # 根据字段清单生成字段配置
      businessRules:
        # 根据脚本业务逻辑生成规则
```

## 标准字段配置模板

基于字段清单，每个字段的标准配置结构：

```yaml
- fieldIndex: {index}
  fieldName: "{fieldName}"
  description: "{description}"
  type: "{type}" # STRING/INTEGER/MOBILE等
  required: {required} # true/false
  rules:
    # 必填校验 (如果required=true)
    - ruleId: "{interfaceName}.{fieldName}.required"
      ruleName: "{fieldName}不能为空"
      type: "REQUIRED"
      enabled: true
      severity: "ERROR"
      message: "{fieldName}是必填项"
    
    # 长度校验
    - ruleId: "{interfaceName}.{fieldName}.length"
      ruleName: "{fieldName}长度校验"
      type: "LENGTH"
      max: {maxLength}
      enabled: true
      severity: "WARNING"
      message: "{fieldName}长度不能超过{maxLength}位"
    
    # 格式校验 (根据字段类型)
    - ruleId: "{interfaceName}.{fieldName}.format"
      ruleName: "{fieldName}格式校验"
      type: "FORMAT"
      pattern: "{pattern}"
      enabled: true
      severity: "ERROR"
      message: "{formatMessage}"
    
    # 枚举值校验 (如果有固定值)
    - ruleId: "{interfaceName}.{fieldName}.enum"
      ruleName: "{fieldName}枚举值校验"
      type: "ENUM"
      values: {enumValues}
      enabled: true
      severity: "WARNING"
      message: "{enumMessage}"
```

## VGOP1-R2.10-24202 字段清单配置

### 字段配置参考 (基于提供的字段清单)

```yaml
fields:
  # 第1位：用户副号号码 - 手机号 VARCHAR2(11)
  - fieldIndex: 0
    fieldName: "mcnnumber"
    description: "用户副号号码"
    type: "MOBILE"
    required: true
    rules:
      - ruleId: "VGOP1-R2.10-24202.mcnnumber.required"
        type: "REQUIRED"
        severity: "ERROR"
        message: "副号码是必填项"
      - ruleId: "VGOP1-R2.10-24202.mcnnumber.format"
        type: "FORMAT"
        pattern: "^1[3-9]\\d{9}$"
        severity: "ERROR"
        message: "副号码格式不正确，应为不带+86的11位数字号码"
      - ruleId: "VGOP1-R2.10-24202.mcnnumber.length"
        type: "LENGTH"
        exact: 11
        severity: "ERROR"
        message: "副号码长度必须为11位"

  # 第2位：用户主号号码 - 手机号 VARCHAR2(14)
  - fieldIndex: 1
    fieldName: "phonenumber"
    description: "用户主号号码"
    type: "STRING"
    required: true
    rules:
      - ruleId: "VGOP1-R2.10-24202.phonenumber.required"
        type: "REQUIRED"
        severity: "ERROR"
        message: "主号码是必填项"
      - ruleId: "VGOP1-R2.10-24202.phonenumber.format"
        type: "FORMAT"
        pattern: "^(1[3-9]\\d{9}|00\\d{11,13})$"
        severity: "ERROR"
        message: "主号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
      - ruleId: "VGOP1-R2.10-24202.phonenumber.length"
        type: "LENGTH"
        max: 14
        severity: "WARNING"
        message: "主号码长度不能超过14位"

  # 第3位：副号码业务状态 - VARCHAR2(1)
  - fieldIndex: 2
    fieldName: "business"
    description: "副号码业务状态"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.business.enum"
        type: "ENUM"
        values: ["0", "1", "2", "3", "4", ""]
        severity: "WARNING"
        message: "业务状态应为：0(正常)、1(预开户)、2(申请中)、3(取消中)、4(预验证)或空值"
      - ruleId: "VGOP1-R2.10-24202.business.length"
        type: "LENGTH"
        exact: 1
        severity: "WARNING"
        message: "业务状态长度应为1位"

  # 第4位：副号码停机标识 - VARCHAR2(2)
  - fieldIndex: 3
    fieldName: "shutdown"
    description: "副号码停机标识"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.shutdown.length"
        type: "LENGTH"
        max: 2
        severity: "WARNING"
        message: "停机标识长度不能超过2位"

  # 第5位：副号码的IMSI号 - VARCHAR2(16)
  - fieldIndex: 4
    fieldName: "mcnimsi"
    description: "副号码的IMSI号"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.mcnimsi.length"
        type: "LENGTH"
        max: 16
        severity: "WARNING"
        message: "IMSI号码长度不能超过16位"
      - ruleId: "VGOP1-R2.10-24202.mcnimsi.format"
        type: "FORMAT"
        pattern: "^(\\d{15,16}|)$"
        severity: "WARNING"
        message: "IMSI应为15-16位数字"

  # 第6位：副号码归属区号 - VARCHAR2(5)
  - fieldIndex: 5
    fieldName: "mcnlocationid"
    description: "副号码归属区号"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.mcnlocationid.length"
        type: "LENGTH"
        max: 5
        severity: "WARNING"
        message: "归属区号长度不能超过5位，不带0"

  # 第7位：副号功能状态 - VARCHAR2(7)
  - fieldIndex: 6
    fieldName: "numstate"
    description: "副号功能状态"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.numstate.length"
        type: "LENGTH"
        exact: 7
        severity: "WARNING"
        message: "功能状态长度应为7位，形如'1000000'"
      - ruleId: "VGOP1-R2.10-24202.numstate.format"
        type: "FORMAT"
        pattern: "^([01]{7}|)$"
        severity: "WARNING"
        message: "功能状态应为7位二进制数字"

  # 第8位：副号码类型 - VARCHAR2(1)
  - fieldIndex: 7
    fieldName: "mcnnature"
    description: "副号码类型"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.mcnnature.enum"
        type: "ENUM"
        values: ["0", "1", ""]
        severity: "WARNING"
        message: "副号码类型应为：0(虚拟副号码)、1(实体副号码)或空值"
      - ruleId: "VGOP1-R2.10-24202.mcnnature.length"
        type: "LENGTH"
        exact: 1
        severity: "WARNING"
        message: "副号码类型长度应为1位"

  # 第9位：副号码序号 - VARCHAR2(2)
  - fieldIndex: 8
    fieldName: "mcnnum"
    description: "副号码序号"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.mcnnum.enum"
        type: "ENUM"
        values: ["1", "2", "3", ""]
        severity: "ERROR"
        message: "副号序号应为：1(第一个副号)、2(第二个副号)、3(第三个副号)或空值"
      - ruleId: "VGOP1-R2.10-24202.mcnnum.length"
        type: "LENGTH"
        max: 2
        severity: "WARNING"
        message: "副号序号长度不能超过2位"

  # 第10位：副号码开户媒介 - VARCHAR2(2)
  - fieldIndex: 9
    fieldName: "channel"
    description: "副号码开户媒介"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.channel.length"
        type: "LENGTH"
        max: 2
        severity: "WARNING"
        message: "开户媒介长度不能超过2位"

  # 第11位：副号码开户渠道 - VARCHAR2(2)
  - fieldIndex: 10
    fieldName: "mj"
    description: "副号码开户渠道"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.mj.enum"
        type: "ENUM"
        values: ["1", "2", "3", ""]
        severity: "WARNING"
        message: "开户渠道应为：1(和多号平台自有渠道)、2(省公司渠道)、3(第三方渠道)或空值"
      - ruleId: "VGOP1-R2.10-24202.mj.length"
        type: "LENGTH"
        max: 2
        severity: "WARNING"
        message: "开户渠道长度不能超过2位"

  # 第12位：开户时间 - VARCHAR2(14)
  - fieldIndex: 11
    fieldName: "openingtime"
    description: "开户时间"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.openingtime.format"
        type: "FORMAT"
        pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
        severity: "WARNING"
        message: "开户时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
      - ruleId: "VGOP1-R2.10-24202.openingtime.length"
        type: "LENGTH"
        exact: 14
        severity: "WARNING"
        message: "开户时间长度应为14位"

  # 第13位：操作时间 - VARCHAR2(14)
  - fieldIndex: 12
    fieldName: "Optime"
    description: "操作时间"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.optime.format"
        type: "FORMAT"
        pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
        severity: "WARNING"
        message: "操作时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
      - ruleId: "VGOP1-R2.10-24202.optime.length"
        type: "LENGTH"
        exact: 14
        severity: "WARNING"
        message: "操作时间长度应为14位"

  # 第14位：实体副号码IMSI号码获取时间 - VARCHAR2(14)
  - fieldIndex: 13
    fieldName: "mcimsitime"
    description: "实体副号码IMSI号码获取时间"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.mcimsitime.format"
        type: "FORMAT"
        pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
        severity: "WARNING"
        message: "IMSI获取时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
      - ruleId: "VGOP1-R2.10-24202.mcimsitime.length"
        type: "LENGTH"
        exact: 14
        severity: "WARNING"
        message: "IMSI获取时间长度应为14位"

  # 第15位：用户类别 - VARCHAR2(2)
  - fieldIndex: 14
    fieldName: "usertype"
    description: "用户类别"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.usertype.length"
        type: "LENGTH"
        max: 2
        severity: "WARNING"
        message: "用户类别长度不能超过2位"

  # 第16位：套餐生效时间 - VARCHAR2(14)
  - fieldIndex: 15
    fieldName: "Begintime"
    description: "套餐生效时间"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.begintime.format"
        type: "FORMAT"
        pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
        severity: "WARNING"
        message: "生效时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
      - ruleId: "VGOP1-R2.10-24202.begintime.length"
        type: "LENGTH"
        exact: 14
        severity: "WARNING"
        message: "生效时间长度应为14位"

  # 第17位：套餐失效时间 - VARCHAR2(14)
  - fieldIndex: 16
    fieldName: "Endtime"
    description: "套餐失效时间"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.endtime.format"
        type: "FORMAT"
        pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
        severity: "WARNING"
        message: "失效时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
      - ruleId: "VGOP1-R2.10-24202.endtime.length"
        type: "LENGTH"
        exact: 14
        severity: "WARNING"
        message: "失效时间长度应为14位"

  # 第18位：业务代码 - VARCHAR2(10)
  - fieldIndex: 17
    fieldName: "ServID"
    description: "业务代码"
    type: "STRING"
    required: false
    rules:
      - ruleId: "VGOP1-R2.10-24202.servid.length"
        type: "LENGTH"
        max: 10
        severity: "WARNING"
        message: "业务代码长度不能超过10位"
```

## 业务逻辑校验规则模板

根据脚本中的业务逻辑生成业务规则：

```yaml
businessRules:
  # WHERE条件约束
  - ruleId: "{interfaceName}.business.where_constraint"
    ruleName: "WHERE条件约束校验"
    description: "基于脚本WHERE条件的数据约束"
    condition: "true"
    assertion: "{scriptWhereCondition}"
    enabled: true
    severity: "ERROR"
    message: "数据不符合脚本WHERE条件"

  # 字段转换逻辑校验
  - ruleId: "{interfaceName}.business.field_transformation"
    ruleName: "字段转换逻辑校验"
    description: "基于脚本字段转换逻辑的校验"
    condition: "{transformCondition}"
    assertion: "{transformAssertion}"
    enabled: true
    severity: "WARNING"
    message: "字段转换逻辑错误"

  # 固定值校验
  - ruleId: "{interfaceName}.business.fixed_value"
    ruleName: "固定值校验"
    description: "基于脚本固定值的校验"
    condition: "{fixedValueCondition}"
    assertion: "{fixedValueAssertion}"
    enabled: true
    severity: "WARNING"
    message: "固定值不正确"
```

## 使用方法

1. **提供脚本**：用户提供VGOP Shell脚本
2. **分析脚本**：按照上述模板分析脚本结构
3. **生成配置**：使用模板生成任务配置和校验规则
4. **应用到环境**：**仅更新开发环境配置文件**

## 重要说明

⚠️ **环境配置更新策略**：
- **开发环境**：需要完整更新所有配置项（任务配置 + 校验规则）
- **测试环境**：**不需要更新**，用户暂时不需要用到测试环境配置
- 后续如有需要，可再单独处理测试环境配置

## 关键配置路径

- **主要配置文件**：[application-dev.yml](mdc:vgop-service/src/main/resources/application-dev.yml)
- **测试环境配置**：[application-test.yml](mdc:vgop-service/src/main/resources/application-test.yml) *(暂不更新)*
- **任务配置节点**：`vgop.tasks.daily` 或 `vgop.tasks.monthly`
- **校验规则节点**：`validation.interfaces`



