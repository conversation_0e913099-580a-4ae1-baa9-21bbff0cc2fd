package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 格式校验规则
 * 使用正则表达式校验字段格式
 */
@Component
public class FormatRule extends AbstractValidationRule {
    
    /**
     * 正则表达式模式
     */
    private final Pattern pattern;
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     * @param regex 正则表达式
     */
    public FormatRule(String fieldName, String regex) {
        super(
                "common." + fieldName + ".format",
                "格式校验",
                "检查字段格式是否符合正则表达式: " + regex,
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.FORMAT
        );
        this.pattern = Pattern.compile(regex);
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public FormatRule() {
        this("*", ".*");
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 如果值为空，则跳过校验（由RequiredFieldRule处理）
        if (StringUtils.isBlank(value)) {
            return createValidResult();
        }
        
        // 获取字段元数据
        FieldMetadata metadata = context.getFieldMetadata(getFieldName());
        
        // 如果有元数据并且有模式，则使用元数据中的模式
        Pattern patternToUse = pattern;
        if (metadata != null && StringUtils.isNotBlank(metadata.getPattern())) {
            patternToUse = Pattern.compile(metadata.getPattern());
        }
        
        // 校验格式
        if (!patternToUse.matcher(value).matches()) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 格式不正确", getFieldName()),
                    context
            );
        }
        
        return createValidResult();
    }
} 