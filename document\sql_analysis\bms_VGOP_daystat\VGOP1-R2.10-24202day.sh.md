# 脚本 `VGOP1-R2.10-24202day.sh` SQL逻辑分析

-   **脚本用途**: 抽取**全量**有效的用户副号码信息。
-   **数据来源**: `mcn_user_minor` (用户副号码信息表)。
-   **核心逻辑**:
    -   `openingtime < '${endtime}'`: 筛选所有开户时间早于执行日当天的记录，实现全量抽取。
    -   `phonenumber != '' and phonenumber is not null`: 确保副号已经绑定到某个主号。
    -   `mcnnum in (1,2,3)`: 筛选特定类型的副号码。
    -   `case channel when '9' then '3' else '1' end as mj`: 根据渠道 (`channel`) 派生出一个新的字段 `mj`。
-   **输出内容**: 全量的副号码详细信息，包括副号码、对应主号码、业务状态、开关机状态等。
-   **说明**: 该脚本生成的是**全量快照**数据，而非日增量数据。 