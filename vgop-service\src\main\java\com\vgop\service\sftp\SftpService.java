package com.vgop.service.sftp;

import com.vgop.service.config.TaskConfig;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.exception.VgopException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * SFTP服务
 * 提供文件上传、目录管理、批量传输等功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class SftpService {
    
    private final VgopAppConfig appConfig;
    
    @Autowired
    public SftpService(VgopAppConfig appConfig) {
        this.appConfig = appConfig;
    }
    
    /**
     * 上传单个文件
     * 
     * @param localFilePath 本地文件路径
     * @param remoteFilePath 远程文件路径
     * @return 是否成功
     */
    public void uploadFile(String localFilePath, String remoteFilePath) throws VgopException {
        int maxRetries = appConfig.getSftp().getRetryTimes();
        int attempt = 0;
        Exception lastException = null;

        while (attempt <= maxRetries) {
            try {
                SftpUtil.upload(appConfig.getSftp(), localFilePath, remoteFilePath);
                return; // Success, exit the method
            } catch (VgopException e) {
                lastException = e;
                attempt++;
                log.error("文件上传失败 (尝试 {}/{})", attempt, maxRetries + 1, e);
                if (attempt <= maxRetries) {
                    try {
                        Thread.sleep(1000 * attempt); // Wait before retrying
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new VgopException("SFTP上传在重试等待中被中断", ie);
                    }
                }
            }
        }
        throw new VgopException("文件上传最终失败，已达最大重试次数: " + localFilePath, lastException);
    }
    
    /**
     * 批量上传文件
     * 
     * @param localFiles 本地文件列表
     * @param remoteDir 远程目录
     * @return 成功上传的文件列表
     */
    public List<String> uploadFiles(List<String> localFilePaths, String remoteDir) {
        List<String> failedFiles = new ArrayList<>();
        log.info("开始批量上传 {} 个文件到: {}", localFilePaths.size(), remoteDir);

        for (String localFilePath : localFilePaths) {
            try {
                Path localPath = Paths.get(localFilePath);
                String remoteFilePath = remoteDir + "/" + localPath.getFileName().toString();
                uploadFile(localFilePath, remoteFilePath);
            } catch (Exception e) {
                log.error("文件上传最终失败: {} -> {}", localFilePath, remoteDir, e);
                failedFiles.add(localFilePath);
            }
        }

        log.info("批量上传完成 - 成功: {}, 失败: {}", localFilePaths.size() - failedFiles.size(), failedFiles.size());
        if (!failedFiles.isEmpty()) {
            log.error("上传失败的文件: {}", failedFiles);
        }
        return failedFiles;
    }
    
    /**
     * 上传目录下的所有文件
     * 
     * @param localDir 本地目录
     * @param remoteDir 远程目录
     * @param filePattern 文件匹配模式（如 "*.dat", "*.verf"）
     * @return 成功上传的文件数量
     */
    public int uploadDirectory(String localDirectoryPath, String remoteDirectoryPath) throws VgopException {
        log.info("准备上传目录: {} -> {}", localDirectoryPath, remoteDirectoryPath);
        File localDir = new File(localDirectoryPath);
        if (!localDir.exists() || !localDir.isDirectory()) {
            throw new VgopException("本地目录不存在或不是一个目录: " + localDirectoryPath);
        }

        File[] files = localDir.listFiles();
        if (files == null || files.length == 0) {
            log.warn("本地目录为空，无需上传: {}", localDirectoryPath);
            return 0;
        }
        
        int successCount = 0;
        for (File file : files) {
            if (file.isFile()) {
                String remoteFilePath = remoteDirectoryPath + "/" + file.getName();
                try {
                    uploadFile(file.getAbsolutePath(), remoteFilePath);
                    successCount++;
                } catch (VgopException e) {
                    log.error("上传文件失败，将继续上传其他文件: {} -> {}", file.getAbsolutePath(), remoteFilePath, e);
                    // Optionally, you could collect failed files here
                }
            }
        }
        log.info("目录上传完成: {} -> {}, 成功上传 {} 个文件", localDirectoryPath, remoteDirectoryPath, successCount);
        return successCount;
    }
    
    /**
     * 上传VGOP数据文件到DMZ
     * 
     * @param dataDate 数据日期
     * @return 上传结果
     */
    public UploadResult uploadVgopDataFiles(String dataDate) {
        // 使用统一的路径配置
        String localPath = appConfig.getBasePath().getExportRoot();
        if (!localPath.endsWith("/")) {
            localPath += "/";
        }
        localPath += dataDate;
        
        String remotePath = appConfig.getSftp().getRemoteBasePath();
        if (!remotePath.endsWith("/")) {
            remotePath += "/";
        }
        remotePath += dataDate;
        
        log.info("=== 开始上传VGOP数据文件 - 日期: {} ===", dataDate);
        log.info("本地路径: {}", localPath);
        log.info("远程路径: {}", remotePath);
        
        UploadResult result = new UploadResult();
        result.setDataDate(dataDate);
        result.setStartTime(System.currentTimeMillis());
        
        // 检查本地目录是否存在
        File localDir = new File(localPath);
        if (!localDir.exists()) {
            log.error("本地数据目录不存在: {}", localPath);
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(false);
            return result;
        }
        
        if (!localDir.isDirectory()) {
            log.error("本地路径不是目录: {}", localPath);
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(false);
            return result;
        }
        
        // 上传.dat文件
        int datCount = uploadDirectory(localPath, remotePath);
        result.setDatFileCount(datCount);
        
        // 上传.verf文件
        int verfCount = uploadDirectory(localPath, remotePath);
        result.setVerfFileCount(verfCount);
        
        result.setEndTime(System.currentTimeMillis());
        result.setSuccess(datCount > 0 && verfCount > 0);
        
        log.info("=== VGOP数据文件上传完成 - .dat文件: {}, .verf文件: {}, 耗时: {}ms ===",
                datCount, verfCount, result.getDuration());
        
        return result;
    }
    
    /**
     * 从文件名中提取接口ID
     * 
     * @param fileName 文件名
     * @return 接口ID，如果无法提取则返回null
     */
    private String extractInterfaceId(String fileName) {
        // 文件名格式：{prefix}_{dataDate}_{interfaceId}_{revision}_{fileNum}.dat/.verf
        String[] nameParts = fileName.split("_");
        if (nameParts.length >= 4) {
            String interfaceId = nameParts[3];
            // 处理.verf文件（没有文件序号部分）
            if (fileName.endsWith(".verf")) {
                interfaceId = interfaceId.replace(".verf", "");
            }
            return interfaceId;
        }
        return null;
    }
    
    /**
     * 从文件名中提取版本号
     * 
     * @param fileName 文件名
     * @return 版本号，如果无法提取则返回-1
     */
    private int extractRevision(String fileName) {
        // 文件名格式：{prefix}_{dataDate}_{interfaceId}_{revision}_{fileNum}.dat/.verf
        String[] nameParts = fileName.split("_");
        if (nameParts.length >= 5) {
            try {
                String revisionPart = nameParts[4];
                if (fileName.endsWith(".verf")) {
                    revisionPart = revisionPart.replace(".verf", "");
                } else if (fileName.endsWith(".dat")) {
                    revisionPart = revisionPart.split("\\.")[0];
                }
                return Integer.parseInt(revisionPart);
            } catch (NumberFormatException e) {
                log.warn("无法解析文件版本号: {}", fileName);
            }
        }
        return -1;
    }
    
    /**
     * 判断文件是否应该被上传
     * 
     * @param fileName 文件名
     * @param interfaceIds 接口ID集合
     * @return true 如果文件应该被上传
     */
    private boolean shouldUploadFile(String fileName, Set<String> interfaceIds) {
        // 如果没有指定接口ID，上传所有文件
        if (interfaceIds == null || interfaceIds.isEmpty()) {
            return true;
        }
        
        // 解析文件名中的接口ID
        // 文件名格式：{prefix}_{dataDate}_{interfaceId}_{revision}_{fileNum}.dat/.verf
        String[] nameParts = fileName.split("_");
        if (nameParts.length >= 4) {
            String fileInterfaceId = nameParts[3];
            
            // 处理.verf文件（没有文件序号部分）
            if (fileName.endsWith(".verf")) {
                fileInterfaceId = fileInterfaceId.replace(".verf", "");
            }
            
            return interfaceIds.contains(fileInterfaceId);
        }
        
        return false;
    }
    
    /**
     * 上传VGOP数据文件到DMZ（支持接口ID过滤）
     * 
     * @param dataDate 数据日期
     * @param interfaceIds 需要传输的接口ID集合，如果为null或空则传输所有文件
     * @param taskType 任务类型（"day"/"month"）
     * @return 上传结果
     */
    public UploadResult uploadVgopDataFilesWithFilter(String dataDate, Set<String> interfaceIds, String taskType) {
        // 使用统一的路径配置
        String localPath = appConfig.getBasePath().getExportRoot();
        if (!localPath.endsWith("/")) {
            localPath += "/";
        }
        localPath += dataDate;
        
        // 根据任务类型确定子目录
        String cycleDir = taskType;
        localPath += "/" + cycleDir;
        
        String remotePath = appConfig.getSftp().getRemoteBasePath();
        if (!remotePath.endsWith("/")) {
            remotePath += "/";
        }
        remotePath += dataDate + "/" + cycleDir;
        
        log.info("=== 开始上传VGOP数据文件（精确传输） - 日期: {}, 任务类型: {}, 接口数: {} ===", 
                 dataDate, taskType, interfaceIds != null ? interfaceIds.size() : 0);
        log.info("本地路径: {}", localPath);
        log.info("远程路径: {}", remotePath);
        if (interfaceIds != null && !interfaceIds.isEmpty()) {
            log.info("过滤接口ID: {}", interfaceIds);
        }
        
        UploadResult result = new UploadResult();
        result.setDataDate(dataDate);
        result.setStartTime(System.currentTimeMillis());
        
        // 检查本地目录是否存在
        File localDir = new File(localPath);
        if (!localDir.exists()) {
            log.error("本地数据目录不存在: {}", localPath);
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(false);
            return result;
        }
        
        if (!localDir.isDirectory()) {
            log.error("本地路径不是目录: {}", localPath);
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(false);
            return result;
        }
        
        // 获取符合条件的文件
        File[] allFiles = localDir.listFiles((dir, name) -> 
            name.endsWith(".dat") || name.endsWith(".verf"));
        
        if (allFiles == null || allFiles.length == 0) {
            log.warn("本地目录中没有找到.dat或.verf文件: {}", localPath);
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(true); // 没有文件也算成功
            return result;
        }
        
        // 使用Map来存储每个接口ID的最新版本文件
        Map<String, Map<String, File>> latestFiles = new HashMap<>(); // <InterfaceId, <FileType, File>>
        
        // 遍历所有文件，找出每个接口的最新版本
        for (File file : allFiles) {
            String fileName = file.getName();
            String fileInterfaceId = extractInterfaceId(fileName);
            String fileType = fileName.endsWith(".dat") ? "dat" : "verf";
            
            if (fileInterfaceId != null && shouldUploadFile(fileName, interfaceIds)) {
                int revision = extractRevision(fileName);
                if (revision >= 0) {
                    Map<String, File> interfaceFiles = latestFiles.computeIfAbsent(fileInterfaceId, k -> new HashMap<>());
                    File existingFile = interfaceFiles.get(fileType);
                    
                    if (existingFile == null || extractRevision(existingFile.getName()) < revision) {
                        interfaceFiles.put(fileType, file);
                    }
                }
            }
        }
        
        log.info("找到 {} 个文件，其中 {} 个接口有最新版本文件", allFiles.length, latestFiles.size());
        
        int datFileCount = 0;
        int verfFileCount = 0;
        int failedCount = 0;
        
        // 上传每个接口的最新版本文件
        for (Map<String, File> interfaceFiles : latestFiles.values()) {
            for (Map.Entry<String, File> entry : interfaceFiles.entrySet()) {
                File file = entry.getValue();
                try {
                    String remoteFilePath = remotePath + "/" + file.getName();
                    uploadFile(file.getAbsolutePath(), remoteFilePath);
                    
                    if (entry.getKey().equals("dat")) {
                        datFileCount++;
                        log.info("成功上传最新版本.dat文件: {}", file.getName());
                    } else {
                        verfFileCount++;
                        log.info("成功上传最新版本.verf文件: {}", file.getName());
                    }
                    
                } catch (Exception e) {
                    failedCount++;
                    log.error("文件上传失败: {}", file.getName(), e);
                }
            }
        }
        
        result.setDatFileCount(datFileCount);
        result.setVerfFileCount(verfFileCount);
        result.setEndTime(System.currentTimeMillis());
        result.setSuccess(failedCount == 0 && (datFileCount > 0 || verfFileCount > 0));
        
        log.info("=== VGOP数据文件上传完成（精确传输） - .dat文件: {}, .verf文件: {}, 失败: {}, 耗时: {}ms ===",
                datFileCount, verfFileCount, failedCount, result.getDuration());
        
        return result;
    }
    
    /**
     * 上传单个接口的数据文件（单任务场景）
     * 
     * @param dataDate 数据日期
     * @param interfaceId 接口ID
     * @return 上传结果
     */
    public UploadResult uploadVgopDataFilesForInterface(String dataDate, String interfaceId) {
        Set<String> interfaceIds = new java.util.HashSet<>();
        interfaceIds.add(interfaceId);
        return uploadVgopDataFilesWithFilter(dataDate, interfaceIds, "day");
    }
    
    /**
     * 上传结果
     */
    public static class UploadResult {
        private String dataDate;
        private boolean success;
        private int datFileCount;
        private int verfFileCount;
        private long startTime;
        private long endTime;
        
        public String getDataDate() { return dataDate; }
        public void setDataDate(String dataDate) { this.dataDate = dataDate; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public int getDatFileCount() { return datFileCount; }
        public void setDatFileCount(int datFileCount) { this.datFileCount = datFileCount; }
        
        public int getVerfFileCount() { return verfFileCount; }
        public void setVerfFileCount(int verfFileCount) { this.verfFileCount = verfFileCount; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getDuration() { return endTime - startTime; }
    }
} 