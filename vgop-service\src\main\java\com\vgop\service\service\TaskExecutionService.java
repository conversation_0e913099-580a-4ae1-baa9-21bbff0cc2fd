package com.vgop.service.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vgop.service.dao.secondary.TaskExecutionMapper;
import com.vgop.service.entity.TaskExecution;
import com.vgop.service.exception.VgopException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 任务执行记录服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class TaskExecutionService {
    
    private final TaskExecutionMapper taskExecutionMapper;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public TaskExecutionService(TaskExecutionMapper taskExecutionMapper, ObjectMapper objectMapper) {
        this.taskExecutionMapper = taskExecutionMapper;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 创建任务执行记录
     * 
     * @param taskType 任务类型（daily/monthly）
     * @param dataDate 数据日期
     * @param stage 执行阶段
     * @param revision 版本号
     * @return 创建的任务执行记录
     */
    @Transactional
    public TaskExecution createExecution(String taskType, String dataDate, String stage, int revision) {
        TaskExecution execution = new TaskExecution();
        execution.setTaskType(taskType);
        execution.setDataDate(dataDate);
        execution.setStage(stage);
        execution.setRevision(revision);
        execution.setStatus("PENDING");
        execution.setStartTime(LocalDateTime.now());
        execution.setCreateTime(LocalDateTime.now());
        execution.setUpdateTime(LocalDateTime.now()); // 设置初始更新时间
        
        taskExecutionMapper.insert(execution);
        log.info("创建任务执行记录: ID={}, 任务类型={}, 数据日期={}, 阶段={}, 版本={}",
                execution.getId(), taskType, dataDate, stage, revision);
        
        return execution;
    }
    
    /**
     * 开始任务执行
     * 
     * @param executionId 执行记录ID
     * @return 更新后的执行记录
     */
    @Transactional
    public TaskExecution startExecution(Long executionId) {
        TaskExecution execution = taskExecutionMapper.findById(executionId);
        if (execution == null) {
            throw new VgopException("任务执行记录不存在: " + executionId);
        }
        
        execution.setStatus("RUNNING");
        execution.setStartTime(LocalDateTime.now());
        execution.setUpdateTime(LocalDateTime.now()); // 设置更新时间，避免使用数据库函数
        
        taskExecutionMapper.update(execution);
        log.info("开始任务执行: ID={}, 任务类型={}, 数据日期={}, 阶段={}",
                execution.getId(), execution.getTaskType(), execution.getDataDate(), execution.getStage());
        
        return execution;
    }
    
    /**
     * 完成任务执行
     * 
     * @param executionId 执行记录ID
     * @param success 是否成功
     * @param processedCount 处理数量
     * @param successCount 成功数量
     * @param failCount 失败数量
     * @param errorMessage 错误信息
     * @param extraInfo 附加信息
     * @return 更新后的执行记录
     */
    @Transactional
    public TaskExecution completeExecution(
            Long executionId,
            boolean success,
            Integer processedCount,
            Integer successCount,
            Integer failCount,
            String errorMessage,
            Map<String, Object> extraInfo) {
        
        TaskExecution execution = taskExecutionMapper.findById(executionId);
        if (execution == null) {
            throw new VgopException("任务执行记录不存在: " + executionId);
        }
        
        LocalDateTime endTime = LocalDateTime.now();
        long duration = 0;
        if (execution.getStartTime() != null) {
            // 计算执行时长（毫秒）
            duration = java.time.Duration.between(execution.getStartTime(), endTime).toMillis();
        }
        
        execution.setStatus(success ? "SUCCESS" : "FAILED");
        execution.setEndTime(endTime);
        execution.setDuration(duration);
        execution.setProcessedCount(processedCount);
        execution.setSuccessCount(successCount);
        execution.setFailCount(failCount);
        execution.setErrorMessage(errorMessage);
        execution.setUpdateTime(LocalDateTime.now()); // 设置更新时间
        
        // 转换附加信息为JSON
        if (extraInfo != null) {
            try {
                execution.setExtraInfo(objectMapper.writeValueAsString(extraInfo));
            } catch (JsonProcessingException e) {
                log.error("序列化extraInfo失败", e);
                execution.setExtraInfo("{}");
            }
        }
        
        taskExecutionMapper.update(execution);
        
        log.info("完成任务执行: ID={}, 任务类型={}, 数据日期={}, 阶段={}, 状态={}, 耗时={}ms",
                execution.getId(), execution.getTaskType(), execution.getDataDate(),
                execution.getStage(), execution.getStatus(), duration);
        
        return execution;
    }
    
    /**
     * 查询指定日期和类型的最新任务执行记录
     */
    public TaskExecution findLatestExecution(String dataDate, String taskType, String stage) {
        return taskExecutionMapper.findLatestExecution(dataDate, taskType, stage);
    }
    
    /**
     * 查询指定日期范围内的任务执行记录
     */
    public List<TaskExecution> findByDateRange(String startDate, String endDate) {
        return taskExecutionMapper.findByDateRange(startDate, endDate);
    }
    
    /**
     * 查询失败的任务执行记录
     */
    public List<TaskExecution> findFailedExecutions(String startDate, String endDate) {
        return taskExecutionMapper.findFailedExecutions(startDate, endDate);
    }
    
    /**
     * 查询正在运行的任务
     */
    public List<TaskExecution> findRunningTasks() {
        return taskExecutionMapper.findRunningTasks();
    }
    
    /**
     * 根据ID查询任务执行记录
     * 
     * @param id 执行记录ID
     * @return 任务执行记录
     */
    public TaskExecution findById(Long id) {
        return taskExecutionMapper.findById(id);
    }
    
    /**
     * 获取今天的日期字符串（格式：yyyyMMdd）
     */
    public static String getTodayDateString() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }
    
    /**
     * 获取昨天的日期字符串（格式：yyyyMMdd）
     */
    public static String getYesterdayDateString() {
        return LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }
    
    /**
     * 获取上个月第一天的日期字符串（格式：yyyyMMdd）
     */
    public static String getLastMonthFirstDayString() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime firstDayOfLastMonth = now.minusMonths(1).withDayOfMonth(1);
        return firstDayOfLastMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }
    
    /**
     * 获取指定任务类型的最近一次执行记录
     * 
     * @param taskType 任务类型（daily/monthly）
     * @return 最近一次执行记录
     */
    public TaskExecution getLastExecution(String taskType) {
        return taskExecutionMapper.getLastExecutionByType(taskType);
    }
    
    /**
     * 获取指定任务类型的执行历史
     * 
     * @param taskType 任务类型（daily/monthly）
     * @param limit 返回记录数量限制
     * @return 执行历史列表
     */
    public List<TaskExecution> getTaskExecutionHistory(String taskType, int limit) {
        return taskExecutionMapper.getTaskExecutionHistory(taskType, limit);
    }
    
    /**
     * 获取指定ID的执行记录
     * 
     * @param executionId 执行记录ID
     * @return 执行记录
     */
    public TaskExecution getExecution(Long executionId) {
        return taskExecutionMapper.findById(executionId);
    }
    
    /**
     * 获取指定日期和类型的任务执行记录
     * 
     * @param dataDate 数据日期
     * @param taskType 任务类型（daily/monthly）
     * @return 执行记录
     */
    public TaskExecution getExecutionByDateAndType(String dataDate, String taskType) {
        return taskExecutionMapper.getExecutionByDateAndType(dataDate, taskType);
    }
    
    /**
     * 获取最近N天的任务执行记录
     * 
     * @param days 天数
     * @return 执行记录列表
     */
    public List<TaskExecution> getRecentExecutions(int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        String startDateStr = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String endDateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return taskExecutionMapper.findByDateRange(startDateStr, endDateStr);
    }
} 