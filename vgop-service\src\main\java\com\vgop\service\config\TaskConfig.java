package com.vgop.service.config;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 接口任务配置
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
public class TaskConfig {
    
    /**
     * 接口ID（如：24101, 24201）
     */
    private String interfaceId;
    
    /**
     * 接口名称（如：VGOP1-R2.11-24101）
     */
    private String interfaceName;
    
    /**
     * 是否启用
     */
    private boolean enabled = true;
    
    /**
     * 任务类型（daily：日数据，monthly：月数据）
     */
    private String taskType = "daily";
    
    /**
     * 导出配置
     */
    private ExportConfig export;
    
    /**
     * 校验配置
     */
    private ValidationConfig validation;
    
    /**
     * 导出配置内部类
     */
    @Data
    public static class ExportConfig {
        /**
         * SQL查询模板
         */
        private String sqlTemplate;
        
        /**
         * 临时文件名模板
         * 例如：a_10000_{dataDate}_VGOP1-R2.11-24101.unl
         */
        private String tempFileNameTemplate;
        
        /**
         * 输出文件名模板
         * 例如：a_10000_{dataDate}_VGOP1-R2.11-24101_{revTimes}_{fileNum}.dat
         */
        private String outputFileNameTemplate;
        
        /**
         * 校验文件名模板（.verf文件）
         * 例如：a_10000_{dataDate}_VGOP1-R2.11-24101_{revTimes}.verf
         */
        private String verfFileNameTemplate;
        
        /**
         * 自定义字段分隔符（默认使用全局配置）
         */
        private String delimiter;
    }
    
    /**
     * 校验配置内部类
     */
    @Data
    public static class ValidationConfig {
        /**
         * 不合规数据占比阈值（0-1之间，如0.1表示10%）
         */
        private Double nonCompliantRatioThreshold = 0.1;
        
        /**
         * 数据量环比波动阈值（如0.1表示10%，5.0表示500%）
         */
        private Double volumeFluctuationThreshold = 0.1;
        
        /**
         * 期望数据量为0（如24207接口）
         */
        private boolean expectZeroRecords = false;
        
        /**
         * 特殊字段列表（不做格式校验的字段）
         */
        private List<String> specialFields;
        
        /**
         * 字段校验规则
         */
        private List<FieldRule> fieldRules;
    }
    
    /**
     * 字段校验规则
     */
    @Data
    public static class FieldRule {
        /**
         * 字段索引（从0开始）
         */
        private int fieldIndex;
        
        /**
         * 字段名称
         */
        private String fieldName;
        
        /**
         * 规则列表
         */
        private List<Rule> rules;
    }
    
    /**
     * 具体的校验规则
     */
    @Data
    public static class Rule {
        /**
         * 规则类型：regex, length, length-range, numeric, date等
         */
        private String type;
        
        /**
         * 规则值（根据类型不同含义不同）
         */
        private String value;
        
        /**
         * 最小值（用于范围校验）
         */
        private Integer min;
        
        /**
         * 最大值（用于范围校验）
         */
        private Integer max;
        
        /**
         * 正则表达式（用于regex类型）
         */
        private String pattern;
        
        /**
         * 错误消息
         */
        private String errorMessage;
    }
} 