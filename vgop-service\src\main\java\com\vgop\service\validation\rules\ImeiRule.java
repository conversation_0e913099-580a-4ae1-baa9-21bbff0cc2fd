package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * IMEI校验规则
 * 检查IMEI格式是否正确并验证校验位
 */
@Component
public class ImeiRule extends AbstractValidationRule {
    
    /**
     * IMEI正则表达式
     * IMEI通常是15位数字
     */
    private static final Pattern IMEI_PATTERN = Pattern.compile("^\\d{15}$");
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     */
    public ImeiRule(String fieldName) {
        super(
                "common." + fieldName + ".imei",
                "IMEI格式校验",
                "检查IMEI格式是否正确并验证校验位",
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.FORMAT
        );
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public ImeiRule() {
        this("imei");
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 如果值为空，则跳过校验（由RequiredFieldRule处理）
        if (StringUtils.isBlank(value)) {
            return createValidResult();
        }
        
        // 校验IMEI格式
        if (!IMEI_PATTERN.matcher(value).matches()) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 的值 (%s) 不是有效的IMEI", getFieldName(), value),
                    context
            );
        }
        
        // 校验IMEI校验位（Luhn算法）
        if (!validateLuhn(value)) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 的值 (%s) IMEI校验位不正确", getFieldName(), value),
                    context
            );
        }
        
        return createValidResult();
    }
    
    /**
     * 使用Luhn算法验证IMEI校验位
     * 
     * @param imei IMEI号码
     * @return 是否有效
     */
    private boolean validateLuhn(String imei) {
        int sum = 0;
        boolean alternate = false;
        
        for (int i = imei.length() - 1; i >= 0; i--) {
            int n = Integer.parseInt(imei.substring(i, i + 1));
            if (alternate) {
                n *= 2;
                if (n > 9) {
                    n = (n % 10) + 1;
                }
            }
            sum += n;
            alternate = !alternate;
        }
        
        return (sum % 10 == 0);
    }
} 