#!/bin/sh
# MmeActionInsId MmeImagePath MmeDateId 
# 

while [ $# -gt 0 ] ; 
   do
     echo "$1"
     eval $1
     shift
   done
   
MmeDate="${MmeDateId:0:8}"   

BeforeMonth=$(date +"%Y%m" -d"${MmeDate} -1month")

ImagePath=$(cd $MmeImagePath;pwd)

MarkId=$MmeActionInsId   

LogPath=$ImagePath/log
if [ ! -d $LogPath ]; then  
    mkdir $LogPath 
fi
LogName=$LogPath/$MarkId.log
:>$LogName

daydatapath=${ImagePath}"/VGOPdata/datafile/"${BeforeMonth} >>$LogName

FTP_UserName="yikaduohao2vgop"
FTP_PassWord="Gcrmd36754@yikaduohao"
FTP_IP="************"
#"***************"
FTP_Port="22"
PATH_Target="/data/yikaduohao/"


echo "FTP_UserName=$FTP_UserName" >>$LogName
echo "FTP_PassWord=$FTP_PassWord" >>$LogName
echo "FTP_IP=$FTP_IP" >>$LogName
echo "FTP_Port=$FTP_Port" >>$LogName
echo "PATH_Target=$PATH_Target" >>$LogName  


#新VGOP主机：
FTP_UserName1="yikaduohao2vgop"
FTP_PassWord1="Lg2021y-kD_Huc"
FTP_IP1="************"
FTP_Port1="22"
PATH_Target="/data/yikaduohao/"



#lftp -p $FTP_Port -u "$FTP_UserName,$FTP_PassWord" sftp://$FTP_IP 1>>$LogName 2>&1  <<FTPEOF
#mirror -I *.dat -I *.verf -R $daydatapath $PATH_Target
#bye
#FTPEOF

#新VGOP主机上传：

#lftp -p $FTP_Port1 -u "$FTP_UserName1,$FTP_PassWord1" sftp://$FTP_IP1 1>>$LogName 2>&1  <<FTPEOF
#mirror -I *.dat -I *.verf -R $daydatapath $PATH_Target
#bye
#FTPEOF

#大数据平台云主机：

FTP_UserName2="yikaduohao2vgop"
FTP_PassWord2="Lg#2021y-kD_Huc"
FTP_IP2="**********"
FTP_IP3="**********"
FTP_Port2="22"
PATH_Target="/data/yikaduohao/"

lftp -p $FTP_Port2 -u "$FTP_UserName2,$FTP_PassWord2" sftp://$FTP_IP2 1>>$LogName 2>&1  <<FTPEOF
mirror -I *.dat -I *.verf -R $daydatapath $PATH_Target

lftp -p $FTP_Port2 -u "$FTP_UserName2,$FTP_PassWord2" sftp://$FTP_IP3 1>>$LogName 2>&1  <<FTPEOF
mirror -I *.dat -I *.verf -R $daydatapath $PATH_Target

bye
FTPEOF
