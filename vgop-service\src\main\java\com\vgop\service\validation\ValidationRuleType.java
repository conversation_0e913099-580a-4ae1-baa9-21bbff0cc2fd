package com.vgop.service.validation;

/**
 * 校验规则类型枚举
 */
public enum ValidationRuleType {
    /**
     * 格式校验 - 检查数据格式是否符合要求
     */
    FORMAT,

    
    /**
     * 长度校验 - 检查数据长度是否符合要求
     */
    LENGTH,
    
    /**
     * 日期格式校验 - 检查日期格式是否正确
     */
    DATE_FORMAT,
    MOBILE_PHONE,
    IMSI_KEY,
    IMEI_KEY,
    /**
     * 范围校验 - 检查数据是否在指定范围内
     */
    RANGE,
    
    /**
     * 必填校验 - 检查必填字段是否有值
     */
    REQUIRED,
    
    /**
     * 枚举值校验 - 检查数据是否在允许的枚举值范围内
     */
    ENUM,
    
    /**
     * 唯一性校验 - 检查数据是否唯一
     */
    UNIQUE,
    
    /**
     * 关联性校验 - 检查数据之间的关联关系
     */
    RELATION,
    
    /**
     * 自定义校验 - 自定义业务逻辑校验
     */
    CUSTOM
} 