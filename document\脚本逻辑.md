# VGOP数据校验系统 - 测试实现方案

本文档面向测试团队，详细说明VGOP数据校验系统的实现逻辑、测试要点和验证规则。

## 1. 系统功能概述

### 1.1. 核心功能
VGOP数据校验系统主要完成两类数据统计任务：
- **日统计任务**: 每日增量数据提取和处理
- **月统计任务**: 每月全量数据快照生成

### 1.2. 业务价值
- 为下游系统提供标准化的数据文件
- 确保数据的完整性和一致性
- 支持运维人员灵活的数据重处理

### 1.3. 系统架构
```
外部调度系统 → 主调度脚本 → 子任务脚本 → 数据库 → 数据文件 → 对端服务
                ↓
           HTTP接口系统 ← 运维人员手动操作
```

---

## 2. 核心业务流程

### 2.1. 标准处理流程
每个子任务脚本都遵循以下**6个标准步骤**：

```
1. 参数解析 → 2. 环境设置 → 3. 时间计算 → 4. 版本控制 → 5. 数据提取 → 6. 文件处理
```

#### 步骤详解：
1. **参数解析**: 接收主调度脚本传入的参数（MmeImagePath, MmeDateId, SrcDBName等）
2. **环境设置**: 初始化日志路径、输出路径等工作环境
3. **时间计算**: 
   - 日统计：计算前一天的时间范围（前一天0点 ~ 当天0点）
   - 月统计：计算前一个月的时间范围
4. **版本控制**: 
   - 查询`bms_vgop_revtimes`表检查是否已生成过文件
   - 如已存在则版本号+1，如不存在则插入版本号00
5. **数据提取**: 
   - 执行SQL查询或调用存储过程
   - 使用数据库unload功能导出到.unl临时文件
6. **文件处理**: 
   - 按200万行拆分数据
   - 格式化处理（加行号、替换分隔符、加回车符）
   - 生成.dat数据文件和.verf校验文件

### 2.2. 关键测试验证点

#### ✅ 时间计算验证
- **测试场景**: 验证不同日期输入下的时间范围计算
- **预期结果**: 
  - 输入2024-01-15，日统计应处理2024-01-14 00:00:00 ~ 2024-01-15 00:00:00
  - 输入2024-02-01，月统计应处理2024-01整个月份

#### ✅ 版本控制验证
- **测试场景**: 同一天多次执行同一脚本
- **预期结果**: 
  - 第一次执行：版本号00
  - 第二次执行：版本号01
  - 第三次执行：版本号02

#### ✅ 数据拆分验证
- **测试场景**: 大数据量（>200万行）的处理
- **预期结果**: 
  - 自动按200万行拆分成多个.dat文件
  - 文件名格式：`{prefix}_{date}_{version}_{sequence}.dat`

#### ✅ 文件格式验证
- **测试场景**: 检查生成文件的格式规范
- **预期结果**: 
  - 每行有序号前缀
  - 分隔符|被替换为\200
  - 行尾有\r回车符
  - .verf文件包含正确的元数据

---

## 3. 具体脚本测试要点

### 3.1. 日统计脚本 (daystat)

| 脚本名称 | 功能描述 | 数据来源 | 测试重点 |
|---------|----------|----------|----------|
| `VGOP1-R2.11-24101.sh` | 业务分析统计 | `bms_vgop_banalyse` | 存储过程调用是否成功 |
| `VGOP1-R2.10-24201day.sh` | 每日新增主号用户 | `mcn_user_major` | 时间过滤条件是否正确 |
| `VGOP1-R2.10-24202day.sh` | 副号用户信息 | `mcn_user_minor` | mcnnum字段过滤逻辑 |
| `VGOP1-R2.10-24203.sh` | 用户活动日志 | `mcn_apploginlog`+`mcn_oplog` | 多表关联和数据合并 |
| `VGOP1-R2.10-24205.sh` | 通话话单记录 | `Mcn_contralog` | 大数据量处理性能 |
| `VGOP1-R2.10-24206.sh` | 短信日志 | `mcn_smslog` | 时间范围准确性 |
| `VGOP1-R2.10-24207day.sh` | 每日新增实体副号 | `mcn_sec_major`+`mcn_sec_major2` | 多表合并逻辑 |
| `VGOP1-R2.13-243XX.sh` | 维表数据 | 各维表 | 全量数据完整性 |

### 3.2. 月统计脚本 (monthstat)

| 脚本名称 | 功能描述 | 测试重点 |
|---------|----------|----------|
| `VGOP1-R2.10-24201month.sh` | 主号用户全量快照 | 数据截止时间准确性 |
| `VGOP1-R2.10-24202month.sh` | 副号用户全量快照 | 全量数据完整性 |
| `VGOP1-R2.10-24204.sh` | 特定操作日志 | 条件过滤逻辑 |
| `VGOP1-R2.10-24207month.sh` | 实体副号用户快照 | 状态过滤准确性 |

---

## 4. HTTP接口测试方案

### 4.1. 接口功能测试

#### 🔍 脚本执行接口测试
**接口**: `POST /api/vgop/scripts/execute`

**正常场景测试**:
```json
// 测试用例1：日统计脚本执行
{
  "scriptType": "daystat",
  "scriptName": "VGOP1-R2.11-24101.sh", 
  "targetDate": "2024-01-15",
  "parameters": {
    "MmeImagePath": "/data/vgop",
    "MmeDateId": "20240115",
    "SrcDBName": "vgop_db"
  },
  "forceRerun": false
}

// 预期结果：返回taskId，状态为submitted
```

**异常场景测试**:
- 无效的scriptName
- 错误的日期格式
- 缺少必要参数
- 数据库连接失败

#### 🔍 任务状态查询测试
**接口**: `GET /api/vgop/tasks/{taskId}/status`

**验证要点**:
- 状态流转：submitted → running → completed/failed
- 进度信息准确性
- 日志信息完整性
- 执行结果数据正确性

#### 🔍 文件上传测试
**接口**: `POST /api/vgop/files/upload`

**测试场景**:
- 单文件上传
- 批量文件上传
- 大文件上传（>1GB）
- 网络中断重试
- 上传失败回滚

### 4.2. 性能测试要点

| 测试项 | 性能指标 | 验证方法 |
|--------|----------|----------|
| 接口响应时间 | <3秒 | 压力测试 |
| 脚本执行时间 | 日统计<30分钟，月统计<2小时 | 监控执行日志 |
| 并发处理能力 | 支持5个并发任务 | 并发测试 |
| 文件处理能力 | 千万级记录<1小时 | 大数据量测试 |

### 4.3. 安全性测试

| 测试项 | 测试方法 | 预期结果 |
|--------|----------|----------|
| 身份验证 | 无效token访问 | 返回401错误 |
| 参数注入 | SQL注入攻击 | 参数被正确转义 |
| 权限控制 | 越权操作 | 返回403错误 |
| 访问频率 | 高频访问 | 触发限流机制 |

---

## 5. 数据验证规则

### 5.1. 文件内容验证
- **行数验证**: .verf文件中的行数与实际.dat文件行数一致
- **文件大小验证**: .verf文件中的字节数与实际文件大小一致
- **格式验证**: 每行开头有序号，分隔符正确，行尾有回车符
- **数据完整性**: 与源数据库查询结果比对，确保无遗漏

### 5.2. 业务逻辑验证
- **时间范围验证**: 提取的数据时间范围与预期一致
- **去重验证**: 确保没有重复记录
- **关联验证**: 多表关联的数据逻辑正确
- **状态验证**: 特定状态筛选条件正确

### 5.3. 系统集成验证
- **版本控制**: 多次执行生成不同版本文件
- **错误恢复**: 执行失败后可以正常重试
- **文件传输**: 生成的文件能够成功上传到对端
- **监控告警**: 异常情况能够及时发现和通知

---

## 6. 测试环境准备

### 6.1. 数据准备
- 准备各个时间段的测试数据
- 确保测试表结构与生产环境一致
- 准备边界数据（空数据、大数据量等）

### 6.2. 环境配置
- 数据库连接配置
- 文件存储路径设置
- 网络和防火墙配置
- 监控和日志系统

### 6.3. 测试工具
- API测试工具（Postman/JMeter）
- 数据库查询工具
- 文件比对工具
- 性能监控工具

---

## 7. 测试检查清单 (Checklist)

### 📋 基础功能验证
- [ ] **参数传递**: 主调度脚本能正确传递参数给子脚本
- [ ] **时间计算**: 日统计计算前一天，月统计计算前一月
- [ ] **版本控制**: 重复执行版本号递增（00→01→02）
- [ ] **数据库连接**: 能正常连接并查询数据库
- [ ] **存储过程**: 能正确调用并获取结果

### 📋 数据处理验证
- [ ] **数据提取**: SQL查询结果正确，无遗漏
- [ ] **数据拆分**: 超过200万行自动拆分
- [ ] **格式转换**: 分隔符|正确替换为\200
- [ ] **行号添加**: 每行开头正确添加序号
- [ ] **回车符**: 行尾正确添加\r

### 📋 文件生成验证
- [ ] **文件命名**: 文件名格式符合规范
- [ ] **文件内容**: .dat文件内容格式正确
- [ ] **.verf文件**: 校验文件包含正确元数据
- [ ] **文件大小**: .verf记录的大小与实际一致
- [ ] **行数统计**: .verf记录的行数与实际一致

### 📋 HTTP接口验证
- [ ] **脚本执行**: POST接口能正确触发脚本
- [ ] **任务状态**: 能查询到任务执行状态
- [ ] **进度跟踪**: 进度信息实时更新
- [ ] **日志查看**: 能获取详细执行日志
- [ ] **文件上传**: 能将文件上传到对端

### 📋 异常处理验证
- [ ] **数据库断开**: 连接失败时有错误提示
- [ ] **磁盘空间**: 空间不足时优雅处理
- [ ] **网络中断**: 上传失败时重试机制
- [ ] **参数错误**: 无效参数时返回明确错误
- [ ] **权限不足**: 无权限时返回401/403

### 📋 性能要求验证
- [ ] **响应时间**: 接口响应<3秒
- [ ] **执行时间**: 日统计<30分钟，月统计<2小时
- [ ] **并发处理**: 支持5个并发任务
- [ ] **大数据量**: 千万级记录处理<1小时
- [ ] **内存使用**: 内存占用在合理范围

### 📋 安全性验证
- [ ] **身份验证**: 无效token被拒绝
- [ ] **SQL注入**: 参数正确转义
- [ ] **权限控制**: 越权操作被阻止
- [ ] **访问日志**: 操作被完整记录
- [ ] **限流机制**: 高频访问被限制

---

## 8. 预期交付物

测试完成后应确保以下交付物质量：

1. **功能测试报告**: 所有脚本执行结果正确
2. **接口测试报告**: 所有HTTP接口功能正常
3. **性能测试报告**: 满足性能指标要求
4. **安全测试报告**: 通过安全性验证
5. **用户手册**: 运维人员操作指南
6. **问题修复记录**: 测试发现问题的修复情况

---

## 💡 测试技巧提示

### 🔧 数据验证技巧
```sql
-- 验证时间范围是否正确
SELECT COUNT(*) FROM mcn_user_major 
WHERE openingtime >= '2024-01-14 00:00:00' 
  AND openingtime < '2024-01-15 00:00:00';

-- 验证版本控制
SELECT scriptname, datadate, revtimes 
FROM bms_vgop_revtimes 
WHERE scriptname = 'VGOP1-R2.11-24101.sh' 
ORDER BY datadate DESC;
```

### 🔧 文件验证技巧
```bash
# 检查文件行数
wc -l *.dat

# 检查文件格式
head -5 i_10000_20240115_00_001.dat

# 验证分隔符替换
hexdump -C i_10000_20240115_00_001.dat | head
```

### 🔧 接口测试技巧
```bash
# 执行脚本
curl -X POST http://localhost:8080/api/vgop/scripts/execute \
  -H "Content-Type: application/json" \
  -d '{"scriptType":"daystat","scriptName":"VGOP1-R2.11-24101.sh"}'

# 查询状态
curl http://localhost:8080/api/vgop/tasks/{taskId}/status
``` 