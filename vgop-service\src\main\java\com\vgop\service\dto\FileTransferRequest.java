package com.vgop.service.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件传输请求DTO
 * 用于内网向DMZ发送文件清单通知
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileTransferRequest {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 数据日期（格式：yyyyMMdd）
     */
    private String dateId;
    
    /**
     * 接口ID
     */
    private String interfaceId;
    
    /**
     * 任务类型（daily/monthly）
     */
    private String taskType;
    
    /**
     * 请求时间
     */
    private LocalDateTime requestTime;
    
    /**
     * 文件清单
     */
    private List<FileInfo> fileList;
    
    /**
     * 扩展信息
     */
    private String remarks;
    
    /**
     * 文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileInfo {
        
        /**
         * 文件名
         */
        private String fileName;
        
        /**
         * 文件完整路径
         */
        private String filePath;
        
        /**
         * 文件类型（dat/verf）
         */
        private String fileType;
        
        /**
         * 文件大小（字节）
         */
        private Long fileSize;
        
        /**
         * 文件MD5校验值
         */
        private String md5;
        
        /**
         * 文件创建时间
         */
        private LocalDateTime createTime;
    }
} 