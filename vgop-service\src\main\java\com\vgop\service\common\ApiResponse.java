package com.vgop.service.common;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * API统一响应格式
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
public class ApiResponse<T> {
    
    /**
     * 响应码
     * 200: 成功
     * 4xx: 客户端错误
     * 5xx: 服务端错误
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间
     */
    private String timestamp;
    
    /**
     * 构造函数
     */
    public ApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
    }
    
    /**
     * 创建成功响应
     * 
     * @param data 响应数据
     * @return API响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "操作成功", data);
    }
    
    /**
     * 创建成功响应（带消息）
     * 
     * @param message 成功消息
     * @param data 响应数据
     * @return API响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(200, message, data);
    }
    
    /**
     * 创建错误响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param data 错误数据
     * @return API响应
     */
    public static <T> ApiResponse<T> error(int code, String message, T data) {
        return new ApiResponse<>(code, message, data);
    }
    
    /**
     * 创建错误响应（无数据）
     * 
     * @param code 错误码
     * @param message 错误消息
     * @return API响应
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null);
    }
    
    /**
     * 创建服务器错误响应
     * 
     * @param message 错误消息
     * @return API响应
     */
    public static <T> ApiResponse<T> serverError(String message) {
        return new ApiResponse<>(500, message, null);
    }
    
    /**
     * 创建客户端错误响应
     * 
     * @param message 错误消息
     * @return API响应
     */
    public static <T> ApiResponse<T> clientError(String message) {
        return new ApiResponse<>(400, message, null);
    }
    
    /**
     * 创建未授权错误响应
     * 
     * @return API响应
     */
    public static <T> ApiResponse<T> unauthorized() {
        return new ApiResponse<>(401, "未授权访问", null);
    }
    
    /**
     * 创建禁止访问错误响应
     * 
     * @return API响应
     */
    public static <T> ApiResponse<T> forbidden() {
        return new ApiResponse<>(403, "禁止访问", null);
    }
    
    /**
     * 创建资源不存在错误响应
     * 
     * @return API响应
     */
    public static <T> ApiResponse<T> notFound() {
        return new ApiResponse<>(404, "资源不存在", null);
    }
} 