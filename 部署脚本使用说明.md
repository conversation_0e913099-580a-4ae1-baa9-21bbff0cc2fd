# VGOP服务自动化部署脚本使用说明

## 概述

本脚本用于在服务器上自动化部署VGOP数据校验服务，包含代码下载、构建打包、服务启动等功能。

## 文件说明

- `deploy.sh` - 主要的部署脚本
- `deploy-config.env` - 配置文件（可选）
- `部署脚本使用说明.md` - 本说明文档

## 前置条件

### 1. 系统要求
- Linux/Unix系统
- 具有sudo权限的用户账户

### 2. 软件依赖
- **Java 8+**: JDK 1.8或更高版本
- **Maven 3.6+**: 用于项目构建
- **Git**: 用于代码下载
- **lsof**: 用于端口检查（通常系统自带）

### 3. 安装依赖（以Ubuntu为例）

```bash
# 更新包列表
sudo apt update

# 安装Java
sudo apt install openjdk-8-jdk

# 安装Maven
sudo apt install maven

# 安装Git
sudo apt install git

# 验证安装
java -version
mvn -version
git --version
```

## 配置说明

### 1. 修改脚本配置

编辑 `deploy.sh` 脚本中的配置部分：

```bash
# 项目配置
GIT_REPO_URL="https://github.com/your-username/vgop.git"  # 修改为实际的Git仓库地址
GIT_BRANCH="main"  # 分支名

# 环境配置
JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"  # Java安装路径
MAVEN_HOME="/opt/maven"  # Maven安装路径
```

### 2. 查找Java和Maven路径

```bash
# 查找Java安装路径
sudo find /usr -name "java" -type f 2>/dev/null | grep bin
# 或者
echo $JAVA_HOME

# 查找Maven安装路径
which mvn
mvn -version
```

### 3. 设置权限

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 确保部署目录有写权限
sudo mkdir -p /opt/vgop
sudo chown $USER:$USER /opt/vgop
```

## 使用方法

### 1. 完整部署

```bash
# 执行完整部署（下载代码、构建、启动服务）
./deploy.sh deploy

# 或者
./deploy.sh start
```

### 2. 其他操作

```bash
# 停止服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看服务状态
./deploy.sh status

# 查看服务日志
./deploy.sh logs

# 仅下载代码和构建（不启动）
./deploy.sh build
```

## 部署流程

脚本执行的完整流程：

1. **环境检查**: 验证Java、Maven、Git是否安装
2. **停止现有服务**: 安全停止已运行的服务实例
3. **下载代码**: 从Git仓库克隆或更新代码
4. **构建项目**: 使用Maven编译打包项目
5. **准备运行环境**: 创建必要的目录和文件
6. **启动服务**: 启动Spring Boot应用
7. **验证部署**: 检查服务是否正常启动

## 目录结构

部署完成后的目录结构：

```
/opt/vgop/
├── vgop/                           # Git仓库代码
│   └── vgop-service/              # Spring Boot项目
│       ├── src/
│       ├── target/
│       └── pom.xml
└── vgop-service/                   # 运行时目录
    ├── vgop-service-1.0.0-SNAPSHOT.jar
    ├── logs/                       # 日志目录
    │   ├── vgop-service.log
    │   └── gc.log
    ├── data/                       # 数据目录
    ├── config/                     # 配置目录
    └── vgop-service.pid           # PID文件
```

## 配置文件

### 1. Spring Boot配置文件

应用会根据 `PROFILE` 变量加载相应的配置文件：
- `application-dev.yml` - 开发环境
- `application-test.yml` - 测试环境
- `application-prod.yml` - 生产环境（需要创建）

### 2. 生产环境配置示例

创建 `vgop-service/src/main/resources/application-prod.yml`：

```yaml
server:
  port: 8080

spring:
  datasource:
    # 数据库配置
    url: ********************************
    username: ${DB_USERNAME:vgop_user}
    password: ${DB_PASSWORD:your_password}

logging:
  level:
    root: INFO
    com.vgop.service: INFO
  file:
    name: logs/vgop-service.log
```

## 常见问题

### 1. Java环境问题

**问题**: `Java环境未找到`
**解决**: 
```bash
# 查找Java安装路径
sudo find /usr -name "java" -type f 2>/dev/null | grep bin
# 更新脚本中的JAVA_HOME变量
```

### 2. Maven环境问题

**问题**: `Maven未找到`
**解决**:
```bash
# 安装Maven
sudo apt install maven
# 或者下载并解压Maven，更新MAVEN_HOME
```

### 3. 端口占用问题

**问题**: `端口8080被占用`
**解决**:
```bash
# 查看占用端口的进程
sudo lsof -i :8080
# 修改配置文件中的端口
# 或者停止占用端口的服务
```

### 4. 权限问题

**问题**: `Permission denied`
**解决**:
```bash
# 给脚本执行权限
chmod +x deploy.sh
# 确保部署目录有写权限
sudo chown -R $USER:$USER /opt/vgop
```

### 5. Git认证问题

**问题**: Git克隆失败
**解决**:
- 使用HTTPS协议并输入用户名密码
- 或者配置SSH密钥
- 或者使用访问令牌

```bash
# 配置Git用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

## 监控和维护

### 1. 日志查看

```bash
# 实时查看日志
tail -f /opt/vgop/vgop-service/logs/vgop-service.log

# 查看最近的错误日志
grep ERROR /opt/vgop/vgop-service/logs/vgop-service.log | tail -20
```

### 2. 性能监控

```bash
# 查看Java进程
ps aux | grep vgop-service

# 查看内存使用
free -h

# 查看磁盘使用
df -h
```

### 3. 定期维护

建议设置定期维护任务：

```bash
# 创建维护脚本
cat > /opt/vgop/maintenance.sh << 'EOF'
#!/bin/bash
# 清理旧日志（保留30天）
find /opt/vgop/vgop-service/logs -name "*.log.*" -mtime +30 -delete
# 清理临时文件
find /tmp -name "*vgop*" -mtime +7 -delete
EOF

chmod +x /opt/vgop/maintenance.sh

# 添加到crontab（每天凌晨2点执行）
echo "0 2 * * * /opt/vgop/maintenance.sh" | crontab -
```

## 安全建议

1. **数据库密码**: 不要在配置文件中明文存储密码，使用环境变量
2. **文件权限**: 限制敏感文件的访问权限
3. **防火墙**: 配置防火墙只开放必要的端口
4. **定期更新**: 定期更新系统和依赖包

## 联系支持

如果遇到问题，请检查：
1. 系统日志: `/var/log/syslog`
2. 应用日志: `/opt/vgop/vgop-service/logs/vgop-service.log`
3. 脚本输出信息

或联系技术支持团队。 