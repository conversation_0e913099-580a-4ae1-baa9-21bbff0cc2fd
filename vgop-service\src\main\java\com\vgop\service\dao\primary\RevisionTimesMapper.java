package com.vgop.service.dao.primary;

import com.vgop.service.entity.RevisionTimes;
import org.apache.ibatis.annotations.*;

/**
 * 版本控制数据访问接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface RevisionTimesMapper {
    
    /**
     * 根据日期和文件名查询版本信息
     * 
     * @param datatime 数据日期
     * @param tmpfilename 临时文件名
     * @return 版本信息
     */
    @Select("SELECT datatime, times, tmpfilename, optime " +
            "FROM bms_vgop_revtimes " +
            "WHERE datatime = #{datatime} AND tmpfilename = #{tmpfilename}")
    RevisionTimes findByDatatimeAndFilename(@Param("datatime") String datatime, 
                                           @Param("tmpfilename") String tmpfilename);
    
    /**
     * 根据ID查询版本信息
     * 注意：当前表结构没有id字段，此方法暂时不可用
     * 
     * @param id 记录ID
     * @return 版本信息
     */
    @Select("SELECT datatime, times, tmpfilename, optime " +
            "FROM bms_vgop_revtimes " +
            "WHERE datatime = #{datatime} AND tmpfilename = #{tmpfilename}")
    RevisionTimes findByCompositeKey(@Param("datatime") String datatime, @Param("tmpfilename") String tmpfilename);
    
    /**
     * 插入新的版本记录
     * 
     * @param revisionTimes 版本信息
     * @return 影响行数
     */
    @Insert("INSERT INTO bms_vgop_revtimes (datatime, times, tmpfilename, optime) " +
            "VALUES (#{datatime}, #{times}, #{tmpfilename}, #{optime})")
    int insert(RevisionTimes revisionTimes);
    
    /**
     * 更新版本号
     * 
     * @param datatime 数据日期
     * @param tmpfilename 临时文件名
     * @param newTimes 新的版本号
     * @param optime 操作时间
     * @return 影响行数
     */
    @Update("UPDATE bms_vgop_revtimes " +
            "SET times = #{newTimes}, optime = #{optime}, update_time = CURRENT_TIMESTAMP " +
            "WHERE datatime = #{datatime} AND tmpfilename = #{tmpfilename}")
    int updateTimes(@Param("datatime") String datatime,
                    @Param("tmpfilename") String tmpfilename,
                    @Param("newTimes") Integer newTimes,
                    @Param("optime") String optime);
    
    /**
     * 获取并自增版本号（使用数据库锁保证并发安全）
     * 注意：这个方法需要在事务中执行
     * 
     * @param datatime 数据日期
     * @param tmpfilename 临时文件名
     * @return 当前版本信息
     */
    @Select("SELECT datatime, times, tmpfilename, optime " +
            "FROM bms_vgop_revtimes " +
            "WHERE datatime = #{datatime} AND tmpfilename = #{tmpfilename} " +
            "FOR UPDATE")
    RevisionTimes selectForUpdate(@Param("datatime") String datatime,
                                 @Param("tmpfilename") String tmpfilename);
    
    // 注意：由于当前表结构不包含 exported、transferred、validated 字段，
    // 以下状态更新方法暂时不可用。如需此功能，请考虑其他实现方式。
} 