package com.vgop.service.controller;

import com.vgop.service.common.ApiResponse;
import com.vgop.service.config.DatabaseCharsetConfig;
import com.vgop.service.entity.TaskExecution;
import com.vgop.service.service.TaskExecutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 监控仪表盘控制器
 * 提供系统运行状态和任务执行情况的查询接口
 */
@Slf4j
@RestController
@RequestMapping("/api/monitor")
@Api(tags = "系统监控接口")
public class MonitorController {

    private final TaskExecutionService taskExecutionService;
    private final DatabaseCharsetConfig.DatabaseCharsetMonitor charsetMonitor;
    
    @Autowired
    public MonitorController(TaskExecutionService taskExecutionService,
                           DatabaseCharsetConfig.DatabaseCharsetMonitor charsetMonitor) {
        this.taskExecutionService = taskExecutionService;
        this.charsetMonitor = charsetMonitor;
    }
    
    /**
     * 获取系统状态概览
     */
    @GetMapping("/status")
    @ApiOperation("获取系统状态概览")
    public ApiResponse<Map<String, Object>> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        String todayStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 获取昨天的日期
        LocalDate yesterday = today.minusDays(1);
        String yesterdayStr = yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 获取当前月第一天
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        String firstDayOfMonthStr = firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 获取上个月第一天
        LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
        String firstDayOfLastMonthStr = firstDayOfLastMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 获取最近的日常任务执行记录
        TaskExecution lastDailyExecution = taskExecutionService.getLastExecution("daily");
        
        // 获取最近的月度任务执行记录
        TaskExecution lastMonthlyExecution = taskExecutionService.getLastExecution("monthly");
        
        // 构建状态信息
        status.put("currentTime", System.currentTimeMillis());
        status.put("today", todayStr);
        status.put("yesterday", yesterdayStr);
        status.put("currentMonth", firstDayOfMonthStr);
        status.put("lastMonth", firstDayOfLastMonthStr);
        
        Map<String, Object> dailyTaskStatus = new HashMap<>();
        if (lastDailyExecution != null) {
            dailyTaskStatus.put("lastExecutionId", lastDailyExecution.getId());
            dailyTaskStatus.put("lastExecutionTime", lastDailyExecution.getStartTime());
            dailyTaskStatus.put("lastExecutionStatus", lastDailyExecution.getStatus());
            dailyTaskStatus.put("lastExecutionSuccess", "SUCCESS".equals(lastDailyExecution.getStatus()));
            dailyTaskStatus.put("lastExecutionDataDate", lastDailyExecution.getDataDate());
            dailyTaskStatus.put("lastExecutionRevision", lastDailyExecution.getRevision());
            dailyTaskStatus.put("isYesterdayTaskCompleted", yesterdayStr.equals(lastDailyExecution.getDataDate()) && "SUCCESS".equals(lastDailyExecution.getStatus()));
        } else {
            dailyTaskStatus.put("lastExecutionStatus", "未执行");
            dailyTaskStatus.put("isYesterdayTaskCompleted", false);
        }
        
        Map<String, Object> monthlyTaskStatus = new HashMap<>();
        if (lastMonthlyExecution != null) {
            monthlyTaskStatus.put("lastExecutionId", lastMonthlyExecution.getId());
            monthlyTaskStatus.put("lastExecutionTime", lastMonthlyExecution.getStartTime());
            monthlyTaskStatus.put("lastExecutionStatus", lastMonthlyExecution.getStatus());
            monthlyTaskStatus.put("lastExecutionSuccess", "SUCCESS".equals(lastMonthlyExecution.getStatus()));
            monthlyTaskStatus.put("lastExecutionDataDate", lastMonthlyExecution.getDataDate());
            monthlyTaskStatus.put("lastExecutionRevision", lastMonthlyExecution.getRevision());
            monthlyTaskStatus.put("isLastMonthTaskCompleted", firstDayOfLastMonthStr.equals(lastMonthlyExecution.getDataDate()) && "SUCCESS".equals(lastMonthlyExecution.getStatus()));
        } else {
            monthlyTaskStatus.put("lastExecutionStatus", "未执行");
            monthlyTaskStatus.put("isLastMonthTaskCompleted", false);
        }
        
        status.put("dailyTaskStatus", dailyTaskStatus);
        status.put("monthlyTaskStatus", monthlyTaskStatus);
        
        return ApiResponse.success(status);
    }
    
    /**
     * 获取任务执行历史
     * 
     * @param taskType 任务类型（daily/monthly）
     * @param limit 返回记录数量限制
     * @return 任务执行历史
     */
    @GetMapping("/history/{taskType}")
    @ApiOperation("获取任务执行历史")
    public ApiResponse<List<TaskExecution>> getTaskHistory(
            @PathVariable String taskType,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<TaskExecution> history = taskExecutionService.getTaskExecutionHistory(taskType, limit);
        return ApiResponse.success(history);
    }
    
    /**
     * 获取任务执行详情
     * 
     * @param executionId 任务执行ID
     * @return 任务执行详情
     */
    @GetMapping("/execution/{executionId}")
    @ApiOperation("获取任务执行详情")
    public ApiResponse<TaskExecution> getTaskExecution(@PathVariable Long executionId) {
        TaskExecution execution = taskExecutionService.getExecution(executionId);
        if (execution == null) {
            return ApiResponse.error(404, "未找到指定的任务执行记录");
        }
        return ApiResponse.success(execution);
    }
    
    /**
     * 获取指定日期的任务执行记录
     * 
     * @param taskType 任务类型（daily/monthly）
     * @param dataDate 数据日期（格式：yyyyMMdd）
     * @return 任务执行记录
     */
    @GetMapping("/execution/{taskType}/{dataDate}")
    @ApiOperation("获取指定日期的任务执行记录")
    public ApiResponse<TaskExecution> getTaskExecutionByDate(
            @PathVariable String taskType,
            @PathVariable String dataDate) {
        
        TaskExecution execution = taskExecutionService.getExecutionByDateAndType(dataDate, taskType);
        if (execution == null) {
            return ApiResponse.error(404, "未找到指定日期和类型的任务执行记录");
        }
        return ApiResponse.success(execution);
    }
    
    /**
     * 获取系统健康状态
     * 
     * @return 系统健康状态
     */
    @GetMapping("/health")
    @ApiOperation("获取系统健康状态")
    public ApiResponse<Map<String, Object>> getHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        
        // 获取最近7天的任务执行记录
        List<TaskExecution> recentExecutions = taskExecutionService.getRecentExecutions(7);
        
        // 计算成功率
        long totalExecutions = recentExecutions.size();
        long successExecutions = recentExecutions.stream()
                .filter(execution -> "SUCCESS".equals(execution.getStatus()))
                .count();
        
        double successRate = totalExecutions > 0 ? (double) successExecutions / totalExecutions * 100 : 0;
        
        health.put("totalExecutions", totalExecutions);
        health.put("successExecutions", successExecutions);
        health.put("successRate", successRate);
        health.put("recentExecutions", recentExecutions);
        
        return ApiResponse.success(health);
    }
    
    /**
     * 获取数据库字符编码状态
     * 
     * @return 数据库字符编码状态信息
     */
    @GetMapping("/charset")
    @ApiOperation("获取数据库字符编码状态")
    public ApiResponse<Map<String, Object>> getCharsetStatus() {
        try {
            Map<String, Object> charsetInfo = new HashMap<>();
            
            // 检查字符编码健康状态
            boolean isHealthy = charsetMonitor.checkCharsetHealth();
            charsetInfo.put("healthy", isHealthy);
            charsetInfo.put("status", isHealthy ? "正常" : "异常");
            
            // 获取数据库字符编码详细信息
            String dbCharsetInfo = charsetMonitor.getCharsetInfo();
            charsetInfo.put("databaseCharsetInfo", dbCharsetInfo);
            
            // 获取JDBC连接字符编码配置
            String jdbcCharsetInfo = charsetMonitor.getJdbcCharsetInfo();
            charsetInfo.put("jdbcCharsetInfo", jdbcCharsetInfo);
            
            // 添加检查时间
            charsetInfo.put("checkTime", System.currentTimeMillis());
            
            log.debug("数据库字符编码状态检查完成: healthy={}", isHealthy);
            
            return ApiResponse.success(charsetInfo);
            
        } catch (Exception e) {
            log.error("获取数据库字符编码状态失败", e);
            return ApiResponse.error(500, "获取数据库字符编码状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试数据库字符编码
     * 
     * @return 字符编码测试结果
     */
    @PostMapping("/charset/test")
    @ApiOperation("测试数据库字符编码")
    public ApiResponse<Map<String, Object>> testCharset() {
        try {
            Map<String, Object> testResult = new HashMap<>();
            
            // 执行字符编码健康检查
            boolean healthCheck = charsetMonitor.checkCharsetHealth();
            testResult.put("healthCheck", healthCheck);
            testResult.put("healthCheckResult", healthCheck ? "通过" : "失败");
            
            // 获取详细的字符编码信息
            String charsetInfo = charsetMonitor.getCharsetInfo();
            testResult.put("detailedInfo", charsetInfo);
            
            // 获取JDBC配置信息
            String jdbcInfo = charsetMonitor.getJdbcCharsetInfo();
            testResult.put("jdbcConfiguration", jdbcInfo);
            
            // 添加测试时间和建议
            testResult.put("testTime", System.currentTimeMillis());
            
            if (healthCheck) {
                testResult.put("recommendation", "数据库字符编码配置正常，可以正常处理中文数据");
            } else {
                testResult.put("recommendation", "数据库字符编码可能存在问题，建议检查JDBC URL中的DB_LOCALE和NEWCODESET参数");
            }
            
            log.info("数据库字符编码测试完成: healthCheck={}", healthCheck);
            
            return ApiResponse.success(testResult);
            
        } catch (Exception e) {
            log.error("测试数据库字符编码失败", e);
            return ApiResponse.error(500, "测试数据库字符编码失败: " + e.getMessage());
        }
    }
} 