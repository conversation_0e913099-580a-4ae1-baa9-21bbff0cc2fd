#!/bin/bash
#
# 脚本功能: 为 mcn_smslog 表生成并导入测试数据。
# 使用方法: ./generate_mcn_smslog_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_smslog_data.sh 20250612 2000 bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"
DATA_FILE=$(mktemp "mcn_smslog_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 的 mcn_smslog 表生成 '${DATA_COUNT}' 条数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- Helper Functions ---
PHONE_PREFIX="19$(date +%N | cut -c 1-5)"

generate_phone_11_digit() {
    printf "%s%04d" "${PHONE_PREFIX}" "$1"
}

generate_phone_international() {
    # SQL中对phonenumber的约束是length=11, 所以这里也只生成11位手机号
    generate_phone_11_digit "$1"
}

generate_datetime() {
    HOUR=$(printf "%02d" $(($RANDOM % 24)))
    MINUTE=$(printf "%02d" $(($RANDOM % 60)))
    SECOND=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
}

# --- 生成数据 ---
MSG_RESULT_VALS=('0' '1' '2' '3' '4' '5' '6' '7' '8' '9' '10' '11' '12' '13' '14' '15' '16' '17' '20' '21' '22' '23' '24' '25' '26' '27' '28' '29')
MSG_RESULT_COUNT=${#MSG_RESULT_VALS[@]}

SHUTDOWN_VALS=('0' '1' '2' '4' '5' '6' '7' '8' '9')
SHUTDOWN_COUNT=${#SHUTDOWN_VALS[@]}

> "${DATA_FILE}" # 清空文件

for i in $(seq 1 ${DATA_COUNT}); do
    smsSeqNo=$(tr -dc '0-9' < /dev/urandom | head -c 20)
    msgId=$(tr -dc 'a-zA-Z0-9' < /dev/urandom | head -c 32)
    chargetype=$(($RANDOM % 4))
    phonenumber=$(generate_phone_11_digit $i)
    mcnnumber=$(generate_phone_international $i)
    sendorreceNum=$(generate_phone_11_digit $(($i + 1000)))
    orginalnum=$(generate_phone_11_digit $(($i + 2000)))
    optime=$(generate_datetime)
    msgResult=${MSG_RESULT_VALS[$(($RANDOM % $MSG_RESULT_COUNT))]}
    failReason="" # 保持为空
    business=$(($RANDOM % 4))
    shutdown=${SHUTDOWN_VALS[$(($RANDOM % $SHUTDOWN_COUNT))]}
    numstate=$(tr -dc '0-4' < /dev/urandom | head -c 1)$(tr -dc '0-2' < /dev/urandom | head -c 2)$(tr -dc '0-1' < /dev/urandom | head -c 1)000
    dcs=8
    totalnum=$(($RANDOM % 5 + 1))
    referNum=$(($RANDOM % 256))
    logtype=$(($RANDOM % 3))
    egroupflag=$(($RANDOM % 2))
    hostName="sms_host_$(($RANDOM % 3 + 1))"

    echo "${smsSeqNo}${DELIMITER}${msgId}${DELIMITER}${chargetype}${DELIMITER}${phonenumber}${DELIMITER}${mcnnumber}${DELIMITER}${sendorreceNum}${DELIMITER}${orginalnum}${DELIMITER}${optime}${DELIMITER}${msgResult}${DELIMITER}${failReason}${DELIMITER}${business}${DELIMITER}${shutdown}${DELIMITER}${numstate}${DELIMITER}${dcs}${DELIMITER}${totalnum}${DELIMITER}${referNum}${DELIMITER}${logtype}${DELIMITER}${egroupflag}${DELIMITER}${hostName}" >> "${DATA_FILE}"
done

echo "数据生成完毕."
echo "准备将数据导入数据库..."

# --- 准备 LOAD 命令 ---
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO mcn_smslog (smsSeqNo, Msgid, chargetype, phonenumber, mcnnumber, sendorreceNum, orginalnum, optime, msgResult, failReason, business, Shutdown, Numstate, dcs, totalnum, referNum, logtype, egroupflag, hostName);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
else
  echo "数据导入失败！"
  echo "临时数据文件 '${DATA_FILE}' 已保留。"
  exit 1
fi

echo "脚本执行完毕。" 