package com.vgop.service.config;

import com.vgop.service.validation.ValidationRule;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;
import java.util.Map;

/**
 * 校验规则配置属性类
 * 用于读取 application.yml 中的 validation 配置
 */
@Data
@ConfigurationProperties(prefix = "validation")
public class ValidationRulesProperties {

    /**
     * 接口配置映射
     * key: 接口名 (e.g., "VGOP1-R2.10-24201")
     * value: 该接口对应的校验规则配置列表
     */
    private Map<String, InterfaceConfig> interfaces;

    /**
     * 接口配置类
     */
    @Data
    public static class InterfaceConfig {
        
        /**
         * 字段配置列表（基于索引）
         */
        private List<FieldConfig> fields;
        
        /**
         * 规则配置列表（向后兼容）
         */
        private List<RuleConfig> rules;
        
        /**
         * 分隔符
         */
        private String delimiter = "\\|";
        
        /**
         * 是否有表头行
         */
        private boolean headerLine = false;
    }
    
    /**
     * 字段配置类（基于索引）
     */
    @Data
    public static class FieldConfig {
        
        /**
         * 字段索引（从0开始）
         */
        private Integer fieldIndex;
        
        /**
         * 字段名
         */
        private String fieldName;
        
        /**
         * 字段描述
         */
        private String description;
        
        /**
         * 字段类型
         */
        private String type;
        
        /**
         * 是否必填
         */
        private boolean required = false;
        
        /**
         * 字段规则列表
         */
        private List<RuleConfig> rules;
    }
    
    /**
     * 规则配置类
     */
    @Data
    public static class RuleConfig {
        
        /**
         * 规则ID
         */
        private String ruleId;
        
        /**
         * 规则名称
         */
        private String ruleName;
        
        /**
         * 字段名
         */
        private String fieldName;
        
        /**
         * 规则类型
         */
        private String type;
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * 严重程度
         */
        private String severity = "ERROR";
        
        /**
         * 错误消息
         */
        private String message;
        
        /**
         * 最小长度
         */
        private Integer min;
        
        /**
         * 最大长度
         */
        private Integer max;
        
        /**
         * 精确长度
         */
        private Integer exact;
        
        /**
         * 正则表达式模式
         */
        private String pattern;
        
        /**
         * 日期格式
         */
        private String format;
        
        /**
         * 最小值
         */
        private String minValue;
        
        /**
         * 最大值
         */
        private String maxValue;
        
        /**
         * 枚举值列表
         */
        private List<String> values;
    }
} 