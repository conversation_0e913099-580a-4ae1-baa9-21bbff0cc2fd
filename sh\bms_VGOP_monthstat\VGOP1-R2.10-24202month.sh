#!/bin/sh


# MmeActionInsId Mme{ImagePath} {MmeDate}Id 

# SrcDBName,ColSep
#

MmeActionInsId=$1
MmeImagePath=$2
MmeDateId=$3
SrcDBName=$4
ColSep=$5
ImagePath=$(cd $MmeImagePath;pwd)

LogPath=${ImagePath}/log
if [ ! -d ${LogPath} ]; then
    mkdir ${LogPath}
fi

LogName=${LogPath}/../../user

MmeDate="${MmeDateId:0:6}"
YearDayMonth="${MmeDateId:0:8}"

BeforeMonth=$(date +"%Y%m" -d"${YearDayMonth} -1month")

starttime=${BeforeMonth}"01000000"
endtime=${MmeDate}"01000000"

if [ "" == "$ColSep" ]; then
        ColSep="|"
fi

TmpFileName="i_10000_${BeforeMonth}_VGOP1-R2.10-24202.unl"
nowdate=$(date +"%Y%m%d%H%M%S")
revtimes=$(echo "select * from bms_vgop_revtimes where datatime='${BeforeMonth}' and tmpfilename='${TmpFileName}'" | dbaccess ${SrcDBName} | grep 'times' | awk -F" " '{print$2}')
if [ ${revtimes} ]; then
	revtimes=$(expr ${revtimes} + 1)
	if [ $(expr length ${revtimes}) -eq 1 ]; then
		revtimes="0"${revtimes}
	fi
	echo "update bms_vgop_revtimes set times=${revtimes} where datatime='${BeforeMonth}' and tmpfilename='${TmpFileName}'" | dbaccess ${SrcDBName} 1>>${LogName} 2>&1
else
	revtimes=00
	echo "insert into bms_vgop_revtimes(datatime,times,tmpfilename,optime) values('${BeforeMonth}',${revtimes},'${TmpFileName}','${nowdate}')" | dbaccess ${SrcDBName} 1>>${LogName} 2>&1
fi

monthdatapath=${ImagePath}"/VGOPdata/datafile/"${BeforeMonth}"/month/"
UnloadFileName=${monthdatapath}${TmpFileName}

if [ ! -d ${monthdatapath} ]; then
    mkdir -p ${monthdatapath}  1>>${LogName} 2>&1
fi

echo "[$MmeActionInsId] unloadFileName="${UnloadFileName}  >>${LogName}

sql="select mcnnumber,phonenumber,business,shutdown,mcnimsi,mcnlocationid,numstate,mcnnature,mcnnum,channel,
case channel when '9' then '3' else '1' end as mj,openingtime,
Optime,mcimsitime,'0',Begintime,Endtime,ServID
from mcn_user_minor where openingtime<${endtime} and phonenumber!='' and phonenumber is not null and mcnnum in (1,2,3)"

UnloadCmd="set lock mode to wait 10;unload to ${UnloadFileName} delimiter '$ColSep' ${sql};"

echo "[$MmeActionInsId]"$(date +"%Y%m%d %H%M%S:")"Unload Start: ${UnloadCmd}" >>${LogName}

echo "${UnloadCmd}" | dbaccess $SrcDBName  1>>${LogName} 2>&1

echo "[$MmeActionInsId]"$(date +"%Y%m%d %H%M%S:")"Unload END." >>${LogName}

linenum=$(wc -l ${UnloadFileName} | awk '{print $1}')
if [ ${linenum} -eq 0 ];then
	linenum=1
fi
filenum=001
n1=1
JYFileName=${monthdatapath}"i_10000_${BeforeMonth}_VGOP1-R2.10-24202_${revtimes}.verf"
while [ ${n1} -le ${linenum} ]
do 
	n2=$(expr ${n1} + 2000000) 
	FileName=${monthdatapath}"i_10000_${BeforeMonth}_VGOP1-R2.10-24202_${revtimes}_${filenum}.dat"
	sed -n "${n1},${n2}p" ${UnloadFileName} | nl -s '|' | sed 's/|$//'|sed 's/\r//g'| tr -d '\' | tr -d ' ' | tr '|' '\200' | awk '{print($0"\r")}' >${FileName}	
	echo "$(basename ${FileName}) $(wc -c ${FileName}) $(wc -l ${FileName}) ${BeforeMonth} ${nowdate}" | awk '{print($1"|"$2"|"$4"|"$6"|"$7)}' | tr '|' '\200' | awk '{print($0"\r")}' >>${JYFileName}
	n1=$(expr ${n2} + 1)
	filenum=$(expr ${filenum} + 1)
	filenumlength=$(echo ${filenum} | awk '{print length($0)}')
	if [ ${filenumlength} -eq 1 ];then
		filenum="00"${filenum}
	elif [ ${filenumlength} -eq 2 ];then
		filenum="0"${filenum}
	fi
done
