package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 手机号校验规则
 * 检查手机号格式是否正确
 */
@Component
public class MobilePhoneRule extends AbstractValidationRule {
    
    /**
     * 中国大陆手机号正则表达式
     * 支持13x, 14x, 15x, 16x, 17x, 18x, 19x开头的手机号
     */
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     */
    public MobilePhoneRule(String fieldName) {
        super(
                "common." + fieldName + ".mobile",
                "手机号格式校验",
                "检查手机号格式是否正确",
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.FORMAT
        );
    }
    
    /**
     * 默认构造函数，用于Spring自动注入
     */
    public MobilePhoneRule() {
        this("mobile");
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        // 如果值为空，则跳过校验（由RequiredFieldRule处理）
        if (StringUtils.isBlank(value)) {
            return createValidResult();
        }
        
        // 校验手机号格式
        if (!MOBILE_PATTERN.matcher(value).matches()) {
            return createInvalidResult(
                    value,
                    String.format("字段 %s 的值 (%s) 不是有效的手机号", getFieldName(), value),
                    context
            );
        }
        
        return createValidResult();
    }
} 