package com.vgop.service.service;

import com.vgop.service.dao.RevisionMapper;
import com.vgop.service.entity.RevisionEntity;
import com.vgop.service.exception.DatabaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 版本控制服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RevisionService {
    
    private final RevisionMapper revisionMapper;
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 获取下一个版本号
     * 如果已存在记录，则版本号+1；如果不存在，则插入版本号为00的新记录
     */
    @Transactional
    public String getNextRevision(String dataTime, String tmpFileName) {
        try {
            RevisionEntity existing = revisionMapper.findByDateAndFileName(dataTime, tmpFileName);
            
            if (existing != null) {
                // 已存在记录，版本号+1
                Integer nextTimes = existing.getTimes() + 1;
                String nextRevision = String.format("%02d", nextTimes);
                
                // 更新数据库中的版本号
                int updateResult = revisionMapper.updateTimes(dataTime, tmpFileName, nextTimes);
                if (updateResult <= 0) {
                    throw new DatabaseException("更新版本号失败: " + tmpFileName);
                }
                
                log.info("版本号已更新: {} -> {}", tmpFileName, nextRevision);
                return nextRevision;
                
            } else {
                // 不存在记录，插入新记录，版本号为00
                String nowTime = LocalDateTime.now().format(FORMATTER);
                RevisionEntity newRevision = RevisionEntity.builder()
                    .dataTime(dataTime)
                    .times(0)
                    .tmpFileName(tmpFileName)
                    .opTime(nowTime)
                    .createTime(LocalDateTime.now())
                    .build();
                
                int insertResult = revisionMapper.insert(newRevision);
                if (insertResult <= 0) {
                    throw new DatabaseException("插入版本记录失败: " + tmpFileName);
                }
                
                log.info("新版本记录已创建: {} -> 00", tmpFileName);
                return "00";
            }
            
        } catch (Exception e) {
            log.error("获取版本号失败: dataTime={}, tmpFileName={}", dataTime, tmpFileName, e);
            throw new DatabaseException("获取版本号失败", e);
        }
    }
    
    /**
     * 查询当前版本号
     */
    public String getCurrentRevision(String dataTime, String tmpFileName) {
        try {
            RevisionEntity existing = revisionMapper.findByDateAndFileName(dataTime, tmpFileName);
            if (existing != null) {
                return String.format("%02d", existing.getTimes());
            }
            return null;
        } catch (Exception e) {
            log.error("查询版本号失败: dataTime={}, tmpFileName={}", dataTime, tmpFileName, e);
            throw new DatabaseException("查询版本号失败", e);
        }
    }
    
    /**
     * 格式化版本号为两位数字符串
     */
    public String formatRevision(Integer times) {
        if (times == null) {
            return "00";
        }
        return String.format("%02d", times);
    }
} 