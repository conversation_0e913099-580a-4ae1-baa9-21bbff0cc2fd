package com.vgop.service.validation;

import com.vgop.service.common.MonitorLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * 文件校验器
 * 负责校验文件数据
 */
@Slf4j
@Component
public class FileValidator {
    
    /**
     * 校验引擎
     */
    private final ValidationEngine validationEngine;
    
    /**
     * 监控日志工具
     */
    private final MonitorLogger monitorLogger;
    
    @Autowired
    public FileValidator(ValidationEngine validationEngine, MonitorLogger monitorLogger) {
        this.validationEngine = validationEngine;
        this.monitorLogger = monitorLogger;
    }
    
    /**
     * 校验文件
     * 
     * @param interfaceName 接口名称
     * @param filePath 文件路径
     * @param dataDate 数据日期
     * @param delimiter 分隔符
     * @param headerLine 是否有表头行
     * @param fieldNames 字段名列表
     * @param errorHandler 错误处理器
     * @return 校验摘要
     */
    public ValidationSummary validateFile(String interfaceName, String filePath, String dataDate,
                                        String delimiter, boolean headerLine, List<String> fieldNames,
                                        Consumer<ErrorRecord> errorHandler) {
        File file = new File(filePath);
        String fileName = file.getName();
        
        // 创建校验摘要
        ValidationSummary summary = ValidationSummary.create(interfaceName, fileName, dataDate);
        
        // 记录开始校验日志
        monitorLogger.logValidationStart(interfaceName, fileName, dataDate);
        log.info("开始校验文件: 接口={}, 文件={}, 日期={}", interfaceName, fileName, dataDate);
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            
            // 如果有表头行，则跳过
            if (headerLine) {
                reader.readLine();
            }
            
            // 行号计数器
            AtomicLong rowNumber = new AtomicLong(1);
            
            // 逐行读取文件并校验
            String line;
            while ((line = reader.readLine()) != null) {
                // 解析行数据
                Map<String, String> rowData = parseRow(line, delimiter, fieldNames);
                
                // 校验行数据
                List<ValidationResult> results = validationEngine.validateRow(
                        interfaceName, rowData, rowNumber.get(), fileName, dataDate);
                
                // 处理校验结果
                boolean hasErrors = !results.isEmpty();
                summary.updateRowCount(hasErrors);
                
                // 处理错误记录
                if (hasErrors) {
                    // 创建校验上下文（用于错误记录）
                    ValidationContext context = ValidationContext.builder()
                            .interfaceName(interfaceName)
                            .fileName(fileName)
                            .dataDate(dataDate)
                            .rowNumber(rowNumber.get())
                            .rowData(rowData)
                            .build();
                    
                    // 更新摘要并处理错误
                    for (ValidationResult result : results) {
                        summary.updateWithResult(result);
                        
                        // 创建错误记录并处理
                        ErrorRecord errorRecord = ErrorRecord.fromValidationResult(result, context);
                        errorHandler.accept(errorRecord);
                    }
                }
                
                // 递增行号
                rowNumber.incrementAndGet();
            }
            
            // 完成校验
            summary.complete();
            
            // 记录校验完成日志
            monitorLogger.logValidationComplete(interfaceName, fileName, dataDate, 
                    summary.getTotalRows(), summary.getErrorRows(), summary.getTotalErrors());
            log.info("校验文件完成: 接口={}, 文件={}, 总行数={}, 错误行数={}, 错误总数={}, 错误率={}%", 
                    interfaceName, fileName, summary.getTotalRows(), summary.getErrorRows(), 
                    summary.getTotalErrors(), summary.getErrorRate());
            
        } catch (Exception e) {
            // 标记校验失败
            summary.fail();
            
            // 记录校验失败日志
            monitorLogger.logValidationFailed(interfaceName, fileName, dataDate, e.getMessage());
            log.error("校验文件失败: 接口={}, 文件={}, 原因={}", interfaceName, fileName, e.getMessage(), e);
        }
        
        return summary;
    }
    
    /**
     * 校验文件（支持字段索引映射）
     * 
     * @param interfaceName 接口名称
     * @param filePath 文件路径
     * @param dataDate 数据日期
     * @param delimiter 分隔符
     * @param headerLine 是否有表头行
     * @param fieldIndexMap 字段索引映射（索引 -> 字段名）
     * @param errorHandler 错误处理器
     * @return 校验摘要
     */
    public ValidationSummary validateFileWithIndex(String interfaceName, String filePath, String dataDate,
                                                  String delimiter, boolean headerLine, 
                                                  Map<Integer, String> fieldIndexMap,
                                                  Consumer<ErrorRecord> errorHandler) {
        File file = new File(filePath);
        String fileName = file.getName();
        
        // 创建校验摘要
        ValidationSummary summary = ValidationSummary.create(interfaceName, fileName, dataDate);
        
        // 记录开始校验日志
        monitorLogger.logValidationStart(interfaceName, fileName, dataDate);
        log.info("开始校验文件（索引模式）: 接口={}, 文件={}, 日期={}", interfaceName, fileName, dataDate);
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            
            // 如果有表头行，则跳过
            if (headerLine) {
                reader.readLine();
            }
            
            // 行号计数器
            AtomicLong rowNumber = new AtomicLong(1);
            
            // 逐行读取文件并校验
            String line;
            while ((line = reader.readLine()) != null) {
                // 解析行数据（基于索引）
                Map<String, String> rowData = parseRowByIndex(line, delimiter, fieldIndexMap);
                
                // 校验行数据
                List<ValidationResult> results = validationEngine.validateRow(
                        interfaceName, rowData, rowNumber.get(), fileName, dataDate);
                
                // 处理校验结果
                boolean hasErrors = !results.isEmpty();
                summary.updateRowCount(hasErrors);
                
                // 处理错误记录
                if (hasErrors) {
                    // 创建校验上下文（用于错误记录）
                    ValidationContext context = ValidationContext.builder()
                            .interfaceName(interfaceName)
                            .fileName(fileName)
                            .dataDate(dataDate)
                            .rowNumber(rowNumber.get())
                            .rowData(rowData)
                            .build();
                    
                    // 更新摘要并处理错误
                    for (ValidationResult result : results) {
                        summary.updateWithResult(result);
                        
                        // 创建错误记录并处理
                        ErrorRecord errorRecord = ErrorRecord.fromValidationResult(result, context);
                        errorHandler.accept(errorRecord);
                    }
                }
                
                // 递增行号
                rowNumber.incrementAndGet();
            }
            
            // 完成校验
            summary.complete();
            
            // 记录校验完成日志
            monitorLogger.logValidationComplete(interfaceName, fileName, dataDate, 
                    summary.getTotalRows(), summary.getErrorRows(), summary.getTotalErrors());
            log.info("校验文件完成（索引模式）: 接口={}, 文件={}, 总行数={}, 错误行数={}, 错误总数={}, 错误率={}%", 
                    interfaceName, fileName, summary.getTotalRows(), summary.getErrorRows(), 
                    summary.getTotalErrors(), summary.getErrorRate());
            
        } catch (Exception e) {
            // 标记校验失败
            summary.fail();
            
            // 记录校验失败日志
            monitorLogger.logValidationFailed(interfaceName, fileName, dataDate, e.getMessage());
            log.error("校验文件失败（索引模式）: 接口={}, 文件={}, 原因={}", interfaceName, fileName, e.getMessage(), e);
        }
        
        return summary;
    }
    
    /**
     * 解析行数据
     * 
     * @param line 行文本
     * @param delimiter 分隔符
     * @param fieldNames 字段名列表
     * @return 行数据（字段名 -> 字段值）
     */
    private Map<String, String> parseRow(String line, String delimiter, List<String> fieldNames) {
        Map<String, String> rowData = new HashMap<>();
        
        // 分割行
        String[] values = line.split(delimiter, -1);
        
        // 将值与字段名对应
        for (int i = 0; i < Math.min(values.length, fieldNames.size()); i++) {
            rowData.put(fieldNames.get(i), values[i]);
        }
        
        return rowData;
    }

    /**
     * 基于索引解析行数据
     * 
     * @param line 行文本
     * @param delimiter 分隔符
     * @param fieldIndexMap 字段索引映射（索引 -> 字段名）
     * @return 行数据（字段名 -> 字段值）
     */
    private Map<String, String> parseRowByIndex(String line, String delimiter, Map<Integer, String> fieldIndexMap) {
        Map<String, String> rowData = new HashMap<>();
        
        // 分割行，注意这里使用的是实际的分隔符（可能是\200）
        String[] values = line.split(delimiter);
        
        // 根据字段索引映射来提取字段值
        for (Map.Entry<Integer, String> entry : fieldIndexMap.entrySet()) {
            int index = entry.getKey();
            String fieldName = entry.getValue();
            
            // 检查索引是否有效
            if (index >= 0 && index < values.length) {
                String value = values[index];
                rowData.put(fieldName, value != null ? value.trim() : "");
                
//                log.debug("解析字段: 索引={}, 字段名={}, 值={}", index, fieldName, value);
            } else {
                // 索引超出范围，设置为空值
                rowData.put(fieldName, "");
                log.warn("字段索引超出范围: 索引={}, 字段名={}, 行长度={}", index, fieldName, values.length);
            }
        }
        
//        log.debug("解析行数据完成: 总字段数={}, 行内容={}", rowData.size(), line.length() > 100 ? line.substring(0, 100) + "..." : line);
        return rowData;
    }

    /**
     * 校验文件（支持字段索引映射）并流式导出校验通过的数据
     * 
     * @param interfaceName 接口名称
     * @param filePath 文件路径
     * @param outputFilePath 输出文件路径（校验通过的数据）
     * @param dataDate 数据日期
     * @param delimiter 分隔符
     * @param headerLine 是否有表头行
     * @param fieldIndexMap 字段索引映射（索引 -> 字段名）
     * @param errorHandler 错误处理器
     * @return 校验摘要
     */
    public ValidationSummary validateFileWithIndexAndExport(String interfaceName, String filePath, 
                                                           String outputFilePath, String dataDate,
                                                           String delimiter, boolean headerLine, 
                                                           Map<Integer, String> fieldIndexMap,
                                                           Consumer<ErrorRecord> errorHandler) {
        File file = new File(filePath);
        String fileName = file.getName();
        
        // 创建校验摘要
        ValidationSummary summary = ValidationSummary.create(interfaceName, fileName, dataDate);
        
        // 记录开始校验日志
        monitorLogger.logValidationStart(interfaceName, fileName, dataDate);
        log.info("开始校验文件并导出（索引模式）: 接口={}, 文件={}, 输出文件={}, 日期={}", 
                interfaceName, fileName, outputFilePath, dataDate);
        
        // 创建输出文件目录
        File outputFile = new File(outputFilePath);
        outputFile.getParentFile().mkdirs();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
             BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(outputFile), StandardCharsets.UTF_8))) {
            
            String headerLineContent = null;
            
            // 如果有表头行，先读取并写入输出文件
            if (headerLine) {
                headerLineContent = reader.readLine();
                if (headerLineContent != null) {
                    writer.write(headerLineContent);
                    writer.newLine();
                    log.debug("写入表头行: {}", headerLineContent);
                }
            }
            
            // 行号计数器
            AtomicLong rowNumber = new AtomicLong(1);
            AtomicLong validRowsWritten = new AtomicLong(0);
            
            // 逐行读取文件并校验
            String line;
            while ((line = reader.readLine()) != null) {
                // 解析行数据（基于索引）
                Map<String, String> rowData = parseRowByIndex(line, delimiter, fieldIndexMap);
                
                // 校验行数据
                List<ValidationResult> results = validationEngine.validateRow(
                        interfaceName, rowData, rowNumber.get(), fileName, dataDate);
                
                // 处理校验结果
                boolean hasErrors = !results.isEmpty();
                summary.updateRowCount(hasErrors);
                
                if (hasErrors) {
                    // 处理错误记录
                    ValidationContext context = ValidationContext.builder()
                            .interfaceName(interfaceName)
                            .fileName(fileName)
                            .dataDate(dataDate)
                            .rowNumber(rowNumber.get())
                            .rowData(rowData)
                            .build();
                    
                    // 更新摘要并处理错误
                    for (ValidationResult result : results) {
                        summary.updateWithResult(result);
                        
                        // 创建错误记录并处理
                        ErrorRecord errorRecord = ErrorRecord.fromValidationResult(result, context);
                        errorHandler.accept(errorRecord);
                    }
                } else {
                    // 校验通过，将数据写入输出文件
                    writer.write(line);
                    writer.newLine();
                    validRowsWritten.incrementAndGet();
                    
                    // 每1000行刷新一次缓冲区，避免内存占用过大
                    if (validRowsWritten.get() % 1000 == 0) {
                        writer.flush();
                        log.debug("已写入 {} 行校验通过的数据", validRowsWritten.get());
                    }
                }
                
                // 递增行号
                rowNumber.incrementAndGet();
            }
            
            // 最终刷新缓冲区
            writer.flush();
            
            // 完成校验
            summary.complete();
            
            // 记录校验完成日志
            monitorLogger.logValidationComplete(interfaceName, fileName, dataDate, 
                    summary.getTotalRows(), summary.getErrorRows(), summary.getTotalErrors());
            log.info("校验文件并导出完成（索引模式）: 接口={}, 文件={}, 总行数={}, 错误行数={}, 校验通过行数={}, 错误总数={}, 错误率={}%", 
                    interfaceName, fileName, summary.getTotalRows(), summary.getErrorRows(), 
                    validRowsWritten.get(), summary.getTotalErrors(), summary.getErrorRate());
            
        } catch (Exception e) {
            // 标记校验失败
            summary.fail();
            
            // 记录校验失败日志
            monitorLogger.logValidationFailed(interfaceName, fileName, dataDate, e.getMessage());
            log.error("校验文件并导出失败（索引模式）: 接口={}, 文件={}, 原因={}", interfaceName, fileName, e.getMessage(), e);
            
            // 删除可能不完整的输出文件
            try {
                if (outputFile.exists()) {
                    outputFile.delete();
                    log.info("已删除不完整的输出文件: {}", outputFilePath);
                }
            } catch (Exception deleteEx) {
                log.warn("删除不完整输出文件失败: {}", deleteEx.getMessage());
            }
        }
        
        return summary;
    }
} 