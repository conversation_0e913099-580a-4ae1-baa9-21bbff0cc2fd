package com.vgop.service.exception;

/**
 * VGOP业务异常基类
 */
public class VgopException extends RuntimeException {
    
    private String errorCode;
    
    public VgopException(String message) {
        super(message);
    }
    
    public VgopException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public VgopException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public VgopException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
} 