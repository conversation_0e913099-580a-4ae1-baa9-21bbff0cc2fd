package com.vgop.service.repository;

import com.vgop.service.validation.ErrorRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 错误记录仓库接口
 */
@Repository
public interface ErrorRecordRepository {
    
    /**
     * 保存错误记录
     * 
     * @param errorRecord 错误记录
     * @return 保存后的错误记录
     */
    ErrorRecord save(ErrorRecord errorRecord);
    
    /**
     * 根据条件查询错误记录
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 错误记录列表
     */
    List<ErrorRecord> findByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate, int page, int size);
    
    /**
     * 统计错误记录数量
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 错误记录数量
     */
    long countByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate);
    
    /**
     * 删除错误记录
     * 
     * @param interfaceName 接口名称
     * @param fileName 文件名
     * @param dataDate 数据日期
     * @return 删除的记录数
     */
    int deleteByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate);
} 