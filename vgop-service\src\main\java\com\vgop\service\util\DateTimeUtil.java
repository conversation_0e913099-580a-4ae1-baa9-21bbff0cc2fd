package com.vgop.service.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * 日期时间计算工具类
 * 对应Shell脚本中的时间范围计算逻辑
 */
public class DateTimeUtil {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    
    /**
     * 根据日期ID计算前一天的日期
     * 对应Shell脚本: BeforeDay=$(date +"%Y%m%d" -d"${MmeDate} -1day")
     */
    public static String calculateBeforeDay(String dateId) {
        // 从dateId中提取日期部分（前8位）
        String dateStr = dateId.substring(0, 8);
        LocalDate date = LocalDate.parse(dateStr, DATE_FORMATTER);
        LocalDate beforeDay = date.minusDays(1);
        return beforeDay.format(DATE_FORMATTER);
    }
    
    /**
     * 根据日期ID计算前一个月的月份
     * 用于月统计脚本
     */
    public static String calculateBeforeMonth(String dateId) {
        String dateStr = dateId.substring(0, 8);
        LocalDate date = LocalDate.parse(dateStr, DATE_FORMATTER);
        LocalDate beforeMonth = date.minusMonths(1);
        return beforeMonth.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }
    
    /**
     * 计算日统计的时间范围
     * 返回数组: [startTime, endTime]
     * startTime: 前一天的000000
     * endTime: 当天的000000
     */
    public static String[] calculateDayTimeRange(String dateId) {
        String beforeDay = calculateBeforeDay(dateId);
        String currentDay = dateId.substring(0, 8);
        
        String startTime = beforeDay + "000000";
        String endTime = currentDay + "000000";
        
        return new String[]{startTime, endTime};
    }
    
    /**
     * 计算月统计的时间范围
     * 返回数组: [startTime, endTime]
     * 计算上一个月的第一天到最后一天
     */
    public static String[] calculateMonthTimeRange(String dateId) {
        String dateStr = dateId.substring(0, 8);
        LocalDate date = LocalDate.parse(dateStr, DATE_FORMATTER);
        LocalDate beforeMonth = date.minusMonths(1);
        
        // 上个月第一天
        LocalDate firstDay = beforeMonth.with(TemporalAdjusters.firstDayOfMonth());
        // 上个月最后一天
        LocalDate lastDay = beforeMonth.with(TemporalAdjusters.lastDayOfMonth());
        
        String startTime = firstDay.format(DATE_FORMATTER) + "000000";
        String endTime = lastDay.format(DATE_FORMATTER) + "235959";
        
        return new String[]{startTime, endTime};
    }
    
    /**
     * 获取指定月份的最后一天
     * 
     * @param month 月份字符串，格式为yyyyMM
     * @return 该月最后一天的日期字符串，格式为yyyyMMdd
     */
    public static String getLastDayOfMonth(String month) {
        LocalDate date = LocalDate.parse(month + "01", DATE_FORMATTER);
        LocalDate lastDay = date.with(TemporalAdjusters.lastDayOfMonth());
        return lastDay.format(DATE_FORMATTER);
    }
    
    /**
     * 获取当前时间字符串（yyyyMMddHHmmss格式）
     * 对应Shell脚本: nowdate=$(date +"%Y%m%d%H%M%S")
     */
    public static String getCurrentTimeString() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }
    
    /**
     * 从dateId中提取日期部分（yyyyMMdd）
     */
    public static String extractDate(String dateId) {
        return dateId.substring(0, 8);
    }
    
    /**
     * 验证日期ID格式是否正确
     */
    public static boolean isValidDateId(String dateId) {
        if (dateId == null || dateId.length() < 8) {
            return false;
        }
        try {
            String dateStr = dateId.substring(0, 8);
            LocalDate.parse(dateStr, DATE_FORMATTER);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 格式化日期为文件名中使用的格式
     */
    public static String formatDateForFileName(LocalDate date) {
        return date.format(DATE_FORMATTER);
    }
    
    /**
     * 格式化当前时间为操作时间格式
     */
    public static String formatCurrentTimeForOperation() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }
} 