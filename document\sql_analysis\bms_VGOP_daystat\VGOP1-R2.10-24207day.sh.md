# 脚本 `VGOP1-R2.10-24207day.sh` SQL逻辑分析

-   **脚本用途**: 抽取前一天新开户的**所有类型**的"第二号码"用户信息。
-   **数据来源**:
    -   `mcn_sec_major`
    -   `mcn_sec_major2`
-   **核心逻辑**:
    -   使用 `UNION ALL` 将两个表的数据合并。
    -   从 `mcn_sec_major` 表中，通过 `openingtime` 筛选出前一天新开户的记录。
    -   从 `mcn_sec_major2` 表中，通过 `openingtime` 筛选出前一天新开户的记录。
-   **输出内容**: 每日新增的第二号码用户信息，包含主号码、副号码（第二号码）、IMSI、业务状态等。
-   **说明**: `mcn_sec_major` 和 `mcn_sec_major2` 这两个表并未在 `分析.md` 中定义，推测它们是用于存储特殊业务（如第二号码）的用户数据。此脚本将两部分增量数据合并后输出。 