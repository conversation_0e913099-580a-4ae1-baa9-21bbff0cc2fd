# 脚本 `VGOP1-R2.10-24201day.sh` SQL逻辑分析

-   **脚本用途**: 抽取**前一天**新开户的用户主号码信息。
-   **数据来源**: `mcn_user_major` (用户主号码信息表), `bossprovince` (省份代码表)。
-   **核心逻辑**:
    -   `openingtime >= '${starttime}' and openingtime < '${endtime}'`: 按开户时间，筛选出前一天的新增用户。
    -   `phonestate in ('0','1')`: 筛选出状态为正常或暂停的用户。
    -   `left join bossprovince`: 关联省份代码表，将平台 `provinceid` 转换为 `bossid` 的前三位作为输出。
-   **输出内容**: 前一天新开户用户的详细信息，如手机号、状态、IMSI、IMEI、开户时间等。
-   **说明**: 此脚本为日增量抽取，用于同步每日新增用户主数据。 