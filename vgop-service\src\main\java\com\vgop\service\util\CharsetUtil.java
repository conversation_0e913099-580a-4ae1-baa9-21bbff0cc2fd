package com.vgop.service.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CharsetEncoder;
import java.nio.charset.CodingErrorAction;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * 字符编码处理工具类
 * 专门用于处理VGOP系统中的字符编码转换和乱码字符清理
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class CharsetUtil {
    
    /**
     * 乱码字符正则模式
     * 匹配常见的乱码字符：问号、方块、无效Unicode字符等
     */
    private static final Pattern INVALID_CHAR_PATTERN = Pattern.compile(
            "[\\uFFFD\\u003F\\u25A1\\u2B1C\\u2610\\u2611\\u2588\\u00BF]|" +  // 替换字符、问号、方块等
            "[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]|" +                      // 控制字符
            "\\p{Cn}|" +                                                       // 未分配的Unicode字符
            "[\\uD800-\\uDFFF]|" +                                            // 代理字符
            "����|" +                                                          // 常见乱码序列
            "\\?{2,}"                                                          // 连续问号
    );
    
    /**
     * GBK字符集
     */
    private static final Charset GBK_CHARSET = Charset.forName("GBK");
    
    /**
     * UTF-8字符集
     */
    private static final Charset UTF8_CHARSET = StandardCharsets.UTF_8;
    
    /**
     * ISO-8859-1字符集（数据库连接使用）
     */
    private static final Charset ISO_8859_1_CHARSET = StandardCharsets.ISO_8859_1;
    
    private CharsetUtil() {
        // 工具类不允许实例化
    }
    
    /**
     * 清理字符串中的乱码字符
     * 
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public static String cleanInvalidChars(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        try {
            // 1. 移除或替换明显的乱码字符
            String cleaned = INVALID_CHAR_PATTERN.matcher(input).replaceAll("");
            
            // 2. 尝试字符编码修复
            cleaned = tryFixEncoding(cleaned);
            
            // 3. 确保字符串在数据库字符集范围内
            cleaned = ensureDatabaseCompatible(cleaned);
            
            if (!input.equals(cleaned)) {
                log.debug("字符清理: {} -> {}", input, cleaned);
            }
            
            return cleaned;
            
        } catch (Exception e) {
            log.warn("字符清理失败，返回空字符串: {}", input, e);
            return "";
        }
    }
    
    /**
     * 尝试修复字符编码问题
     * 
     * @param input 输入字符串
     * @return 修复后的字符串
     */
    private static String tryFixEncoding(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        try {
            // 检查是否包含乱码序列"����"
            if (input.contains("����")) {
                log.debug("检测到乱码序列，尝试编码修复: {}", input);
                
                // 尝试不同的编码组合修复
                String[] attempts = {
                    convertEncoding(input, ISO_8859_1_CHARSET, GBK_CHARSET),
                    convertEncoding(input, ISO_8859_1_CHARSET, UTF8_CHARSET),
                    convertEncoding(input, GBK_CHARSET, UTF8_CHARSET),
                    input.replace("����", ""), // 最后选择直接移除
                };
                
                // 选择最合理的结果
                for (String attempt : attempts) {
                    if (attempt != null && !attempt.contains("����") && isValidString(attempt)) {
                        log.debug("编码修复成功: {} -> {}", input, attempt);
                        return attempt;
                    }
                }
            }
            
            return input;
        } catch (Exception e) {
            log.debug("编码修复失败: {}", input, e);
            return input;
        }
    }
    
    /**
     * 字符编码转换
     * 
     * @param input 输入字符串
     * @param fromCharset 源字符集
     * @param toCharset 目标字符集
     * @return 转换后的字符串，失败时返回null
     */
    private static String convertEncoding(String input, Charset fromCharset, Charset toCharset) {
        try {
            byte[] bytes = input.getBytes(fromCharset);
            return new String(bytes, toCharset);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 检查字符串是否有效（不包含明显的乱码）
     * 
     * @param input 输入字符串
     * @return 是否有效
     */
    private static boolean isValidString(String input) {
        if (input == null) {
            return false;
        }
        
        // 检查是否包含乱码特征
        return !INVALID_CHAR_PATTERN.matcher(input).find();
    }
    
    /**
     * 确保字符串与数据库字符集兼容
     * 
     * @param input 输入字符串
     * @return 兼容的字符串
     */
    private static String ensureDatabaseCompatible(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        try {
            // 使用数据库连接字符集（GBK）进行编码测试
            CharsetEncoder encoder = GBK_CHARSET.newEncoder()
                    .onMalformedInput(CodingErrorAction.REPLACE)
                    .onUnmappableCharacter(CodingErrorAction.REPLACE);
            
            CharsetDecoder decoder = GBK_CHARSET.newDecoder()
                    .onMalformedInput(CodingErrorAction.REPLACE)
                    .onUnmappableCharacter(CodingErrorAction.REPLACE);
            
            // 编码到字节数组
            ByteBuffer byteBuffer = encoder.encode(CharBuffer.wrap(input));
            
            // 解码回字符串
            CharBuffer charBuffer = decoder.decode(byteBuffer);
            
            return charBuffer.toString();
            
        } catch (Exception e) {
            log.debug("数据库兼容性检查失败: {}", input, e);
            // 最后的保险措施：只保留ASCII字符
            return input.replaceAll("[^\\x20-\\x7E]", "");
        }
    }
    
    /**
     * 截断字符串到指定长度（按字节长度）
     * 确保不会在多字节字符中间截断
     * 
     * @param input 输入字符串
     * @param maxBytes 最大字节数
     * @param charset 字符集
     * @return 截断后的字符串
     */
    public static String truncateByBytes(String input, int maxBytes, Charset charset) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        try {
            byte[] bytes = input.getBytes(charset);
            if (bytes.length <= maxBytes) {
                return input;
            }
            
            // 逐字符检查，确保不在多字节字符中间截断
            StringBuilder result = new StringBuilder();
            for (char c : input.toCharArray()) {
                String temp = result.toString() + c;
                if (temp.getBytes(charset).length <= maxBytes) {
                    result.append(c);
                } else {
                    break;
                }
            }
            
            String truncated = result.toString();
            if (!truncated.equals(input)) {
                log.debug("字符串截断: {} -> {}", input, truncated);
            }
            
            return truncated;
            
        } catch (Exception e) {
            log.warn("字符串截断失败: {}", input, e);
            return input.substring(0, Math.min(input.length(), maxBytes / 2)); // 保守估计
        }
    }
    
    /**
     * 安全地准备数据库字符串
     * 综合应用字符清理、编码修复和长度控制
     * 
     * @param input 输入字符串
     * @param maxLength 最大长度（字符数）
     * @return 安全的数据库字符串
     */
    public static String prepareDatabaseString(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        
        // 1. 清理无效字符
        String cleaned = cleanInvalidChars(input);
        
        // 2. 按字符长度截断
        if (cleaned.length() > maxLength) {
            cleaned = cleaned.substring(0, maxLength);
            log.debug("字符串长度截断: {} chars -> {} chars", input.length(), maxLength);
        }
        
        // 3. 按字节长度检查（GBK每个中文字符2字节，英文1字节）
        int maxBytes = maxLength * 2; // 保守估计
        cleaned = truncateByBytes(cleaned, maxBytes, GBK_CHARSET);
        
        return cleaned;
    }
    
    /**
     * 检测字符串的可能编码
     * 
     * @param input 输入字符串
     * @return 可能的编码名称
     */
    public static String detectEncoding(String input) {
        if (input == null || input.isEmpty()) {
            return "UNKNOWN";
        }
        
        // 简单的编码检测逻辑
        if (INVALID_CHAR_PATTERN.matcher(input).find()) {
            return "CORRUPTED";
        }
        
        // 检查是否为纯ASCII
        if (input.matches("^[\\x00-\\x7F]*$")) {
            return "ASCII";
        }
        
        // 检查是否为有效的UTF-8
        try {
            byte[] bytes = input.getBytes(UTF8_CHARSET);
            String decoded = new String(bytes, UTF8_CHARSET);
            if (decoded.equals(input)) {
                return "UTF-8";
            }
        } catch (Exception e) {
            // 忽略
        }
        
        // 检查是否为有效的GBK
        try {
            byte[] bytes = input.getBytes(GBK_CHARSET);
            String decoded = new String(bytes, GBK_CHARSET);
            if (decoded.equals(input)) {
                return "GBK";
            }
        } catch (Exception e) {
            // 忽略
        }
        
        return "MIXED";
    }
} 