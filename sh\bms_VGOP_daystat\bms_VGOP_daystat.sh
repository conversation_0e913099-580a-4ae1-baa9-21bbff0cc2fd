#!/bin/sh


# MmeActionInsId Mme{ImagePath} {MmeDate}Id 

# TraceFlag,SrcDBName,ColSep
#

echo $#
while [ $# -gt 0 ] ; 
   do
     echo "$1"
     eval $1
     shift
   done


ImagePath=$(cd $MmeImagePath;pwd)

LogPath=${ImagePath}/log
if [ ! -d ${LogPath} ]; then
    mkdir ${LogPath}
fi
LogName=${LogPath}/../../user

modulepath=${LogPath}/../../../module/bms_VGOP_daystat

echo "${modulepath}/VGOP1-R2.11-24101.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${TraceFlag} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.11-24101.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${TraceFlag} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.10-24201day.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24201day.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.10-24202day.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24202day.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.10-24203.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24203.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.10-24205.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24205.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.10-24206.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24206.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.13-24301.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.13-24301.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.13-24302.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.13-24302.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.13-24303.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.13-24303.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.13-24304.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.13-24304.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
echo "${modulepath}/VGOP1-R2.10-24207day.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}" >>${LogName}
${modulepath}/VGOP1-R2.10-24207day.sh ${MmeActionInsId} ${MmeImagePath} ${MmeDateId} ${SrcDBName} ${ColSep}
