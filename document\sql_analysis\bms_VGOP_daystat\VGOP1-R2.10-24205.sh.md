# 脚本 `VGOP1-R2.10-24205.sh` SQL逻辑分析

-   **脚本用途**: 抽取前一天发生的、被视为**有效的国内手机间**的通话记录。
-   **数据来源**: `mcn_contralog` (正常话单表)。
-   **核心逻辑**:
    -   `CallBeginTime >= '${starttime}' and CallBeginTime < '${endtime}'`: 按通话开始时间，筛选出前一天的通话记录。
    -   `Cause != '80 81' and reason != '1'`: 过滤掉因为特定原因导致未接通或异常的话单。`Cause` 和 `reason` 是通话失败/异常原因代码。
    -   `length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11`: 要求主叫和被叫号码长度都为11位。
    -   `callingPartyNumber like '1%' and calledPartyNumber like '1%'`: 要求主叫和被叫号码都以'1'开头。
-   **输出内容**: 清洗和过滤后的通话话单，包括呼叫类型、主被叫号码、通话所用副号、通话时间、时长等。
-   **说明**: 综合多个`where`条件来看，此脚本旨在筛选出国内手机号码之间（11位且'1'开头）的、成功接通的通话记录。 