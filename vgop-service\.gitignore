# Compiled class file
*.class

# Log files
*.log
logs/
log/

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE - IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# IDE - Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

# IDE - VSCode
.vscode/

# IDE - NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/

# OS
.DS_Store
Thumbs.db

# Application specific
/data/
/backup/
*.dat
*.unl
*.verf
/temp/
/tmp/

# H2 Database files
*.db
*.mv.db
*.trace.db

# Configuration override files (for local development)
application-local.yml
application-local.properties

# Sensitive information
*-prod.yml
*-prod.properties
secrets.properties 