# 脚本 `VGOP1-R2.11-24101.sh` SQL逻辑分析

## 1. 脚本概述

本脚本是VGOP日统计任务的一部分，主要功能是执行一个分析存储过程，然后从结果表中抽取出前一天的数据，生成数据文件。文件名中包含日期和重传版本号。

## 2. 核心SQL分析

脚本中涉及数据抽取的核心SQL语句如下：

```sql
select 
    provinceid,
    locationid,
    phonenum,
    mcnnum,
    appactivenum,
    mcnactivenum,
    paynum,
    feenum,
    secphonenum,
    secmcnnum,
    amcnnum 
from bms_vgop_banalyse 
where stattime='${BeforeDay}'
```

## 3. SQL逻辑解读

-   **数据源**: `bms_vgop_banalyse` 表。根据命名推测，这是一个VGOP业务的日终分析（banalyse = business analyse）结果表。
-   **过滤条件**: `stattime='${BeforeDay}'`，其中 `${BeforeDay}` 是脚本计算出的执行日期的前一天。这表明脚本总是处理前一天的数据。
-   **查询目的**: 查询 `bms_vgop_banalyse` 表中前一天的所有统计记录。从查询的字段来看，这似乎是一个按省份（`provinceid`）和地市（`locationid`）分组的统计报表。
-   **关联存储过程**: 在执行 `UNLOAD` 之前，脚本调用了名为 `bmssp_VGOP_banalyse` 的存储过程。这说明 `bms_vgop_banalyse` 表的数据是由这个存储过程生成的。核心的复杂统计逻辑（如用户数、活跃数、付费数等指标的计算）被封装在了数据库的存储过程里。

## 4. 涉及的库表和字段分析

| 字段 | 来源表 | 含义推测 (根据字段名和业务) |
| :--- | :--- | :--- |
| `provinceid` | `bms_vgop_banalyse` | 省份ID |
| `locationid` | `bms_vgop_banalyse` | 地市ID |
| `phonenum` | `bms_vgop_banalyse` | （主号）用户数 |
| `mcnnum` | `bms_vgop_banalyse` | （副号）用户数 |
| `appactivenum` | `bms_vgop_banalyse` | App活跃用户数 |
| `mcnactivenum` | `bms_vgop_banalyse` | 副号活跃用户数 |
| `paynum` | `bms_vgop_banalyse` | 付费用户数 |
| `feenum` | `bms_vgop_banalyse` | 收入金额 (fee num) |
| `secphonenum` | `bms_vgop_banalyse` | 第二号码用户数 |
| `secmcnnum` | `bms_vgop_banalyse` | 第二副号数 |
| `amcnnum` | `bms_vgop_banalyse` | 某类特殊副号数 (如 A类) |

*注意: `bms_vgop_banalyse` 表未在提供的 `分析.md` 中定义，以上字段含义是基于通用业务命名规范的推测。测试时应重点关注该表数据的生成逻辑（即 `bmssp_VGOP_banalyse` 存储过程）是否正确。*

## 5. 重传机制

脚本中包含对 `bms_vgop_revtimes` 表的读写操作，用于记录和更新文件的重传次数（`revtimes`）。如果当天同样的文件已经生成过，则版本号加一，确保下游系统能识别出是重传数据。 