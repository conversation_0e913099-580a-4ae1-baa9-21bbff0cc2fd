package com.vgop.service.sftp;

import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.exception.VgopException;
import lombok.extern.slf4j.Slf4j;
import net.schmizz.sshj.SSHClient;
import net.schmizz.sshj.sftp.SFTPClient;
import net.schmizz.sshj.transport.verification.PromiscuousVerifier;
import net.schmizz.sshj.xfer.FileSystemFile;

import java.io.File;
import java.io.IOException;

/**
 * SFTP工具类 (基于SSHJ)，采用短连接模式。
 * 每次操作都会建立新连接，操作完成后断开。
 */
@Slf4j
public final class SftpUtil {

    private SftpUtil() {
        // 工具类，防止实例化
    }

    /**
     * 上传单个文件。
     *
     * @param sftpConfig     SFTP配置
     * @param localFilePath  本地文件路径
     * @param remoteFilePath 远程文件路径
     * @throws VgopException 如果上传失败
     */
    public static void upload(VgopAppConfig.SftpConfig sftpConfig, String localFilePath, String remoteFilePath) throws VgopException {
        try (SSHClient sshClient = new SSHClient()) {
            sshClient.addHostKeyVerifier(new PromiscuousVerifier());
            sshClient.connect(sftpConfig.getHost(), sftpConfig.getPort());
            sshClient.authPassword(sftpConfig.getUsername(), sftpConfig.getPassword());

            try (SFTPClient sftpClient = sshClient.newSFTPClient()) {
                // 确保远程目录存在
                String remoteDirPath = new File(remoteFilePath).getParent().replace('\\', '/');
                createRemoteDirectory(sftpClient, remoteDirPath);

                // 上传文件
                log.debug("开始上传文件: {} -> {}", localFilePath, remoteFilePath);
                sftpClient.put(new FileSystemFile(localFilePath), remoteFilePath);
                log.info("文件上传成功: {} -> {}", localFilePath, remoteFilePath);
            }
        } catch (IOException e) {
            log.error("SFTP操作失败: local={}, remote={}", localFilePath, remoteFilePath, e);
            throw new VgopException("SFTP文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 递归创建远程目录。
     *
     * @param sftpClient SFTP客户端
     * @param path       要创建的目录路径
     * @throws IOException 如果创建失败
     */
    private static void createRemoteDirectory(SFTPClient sftpClient, String path) throws IOException {
        try {
            sftpClient.stat(path);
        } catch (IOException e) {
            // "No such file" or similar error indicates the directory doesn't exist.
            log.info("远程目录不存在，正在创建: {}", path);
            sftpClient.mkdirs(path);
        }
    }
} 