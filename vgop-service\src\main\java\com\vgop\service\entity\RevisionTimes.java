package com.vgop.service.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 版本控制实体类
 * 对应数据库表：bms_vgop_revtimes
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RevisionTimes {
    
    /**
     * 数据日期，格式：YYYYMMDD（最长14位）
     */
    private String datatime;
    
    /**
     * 修订次数，从0开始
     */
    private Integer times;
    
    /**
     * 临时文件名（含接口标识）
     * 例如：a_10000_20240115_VGOP1-R2.10-24201.unl
     */
    private String tmpfilename;
    
    /**
     * 操作时间，格式：YYYYMMDDHHmmss（最长14位）
     */
    private String optime;
    
    /**
     * 构建完整的文件版本号（用于文件名）
     * @return 格式化的版本号，如 "00", "01", "02"
     */
    public String getFormattedTimes() {
        return String.format("%02d", times);
    }
    
    /**
     * 获取周期类型
     * @return 周期类型（daily/monthly）
     */
    public String getCycleType() {
        if (tmpfilename == null) {
            return null;
        }
        
        if (tmpfilename.contains("daily")) {
            return "daily";
        } else if (tmpfilename.contains("monthly")) {
            return "monthly";
        }
        
        // 默认返回daily
        return "daily";
    }
    
    /**
     * 获取版本号
     *
     * @return 版本号
     */
    public int getRevision() {
        return times != null ? times : 0;
    }
} 