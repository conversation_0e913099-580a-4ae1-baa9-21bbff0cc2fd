package com.vgop.service.validation.rules;

import com.vgop.service.validation.*;

/**
 * 接口专用手机号校验规则
 */
public class InterfaceMobilePhoneRule extends AbstractValidationRule {
    
    public InterfaceMobilePhoneRule(String interfaceName, String fieldName) {
        super(
                interfaceName + "." + fieldName + ".mobile",
                "手机号格式校验",
                "检查手机号格式是否正确",
                fieldName,
                ValidationSeverity.ERROR,
                ValidationRuleType.FORMAT
        );
    }
    
    @Override
    public ValidationResult validate(String value, ValidationContext context) {
        if (value == null || value.trim().isEmpty()) {
            return createValidResult();
        }
        
        // 11位手机号或国际号码(加字冠00)
        if (!value.matches("^(1[3-9]\\d{9}|00\\d{11,13})$")) {
            return createInvalidResult(
                    value,
                    String.format("手机号格式不正确: %s", value),
                    context
            );
        }
        
        return createValidResult();
    }
} 