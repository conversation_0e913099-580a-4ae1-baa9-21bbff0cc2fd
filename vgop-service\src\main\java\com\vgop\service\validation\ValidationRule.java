package com.vgop.service.validation;

/**
 * 数据校验规则接口
 * 所有具体的校验规则都需要实现此接口
 */
public interface ValidationRule {
    
    /**
     * 获取规则ID
     * 
     * @return 规则ID
     */
    String getRuleId();
    
    /**
     * 获取规则名称
     * 
     * @return 规则名称
     */
    String getRuleName();
    
    /**
     * 获取规则描述
     * 
     * @return 规则描述
     */
    String getDescription();
    
    /**
     * 获取规则适用的字段名
     * 
     * @return 字段名
     */
    String getFieldName();
    
    /**
     * 获取规则严重程度
     * 
     * @return 严重程度（ERROR, WARNING, INFO）
     */
    ValidationSeverity getSeverity();
    
    /**
     * 校验数据值
     * 
     * @param value 要校验的值
     * @param context 校验上下文，包含行数据和元数据
     * @return 校验结果
     */
    ValidationResult validate(String value, ValidationContext context);
    
    /**
     * 获取规则类型
     * 
     * @return 规则类型
     */
    ValidationRuleType getRuleType();
} 