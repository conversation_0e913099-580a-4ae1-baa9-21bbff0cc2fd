package com.vgop.service.validation;

import com.vgop.service.common.MonitorLogger;
import com.vgop.service.repository.ErrorRecordRepository;
import com.vgop.service.repository.ValidationSummaryRepository;
import com.vgop.service.config.ValidationRuleConfig;
import com.vgop.service.config.ValidationRulesProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;

/**
 * 校验服务实现类
 */
@Slf4j
@Service
public class ValidationServiceImpl implements ValidationService {
    
    /**
     * 校验引擎
     */
    private final ValidationEngine validationEngine;
    
    /**
     * 文件校验器
     */
    private final FileValidator fileValidator;
    
    /**
     * 错误记录仓库
     */
    private final ErrorRecordRepository errorRecordRepository;
    
    /**
     * 校验摘要仓库
     */
    private final ValidationSummaryRepository validationSummaryRepository;
    
    /**
     * 监控日志工具
     */
    private final MonitorLogger monitorLogger;
    
    /**
     * 接口配置类
     */
    private static class InterfaceConfig {
        String delimiter = ",";
        boolean headerLine = false;
        List<String> fieldNames = new ArrayList<>();
    }
    
    private final ValidationRuleConfig validationRuleConfig;
    
    @Autowired
    public ValidationServiceImpl(ValidationEngine validationEngine, FileValidator fileValidator,
                               ErrorRecordRepository errorRecordRepository,
                               ValidationSummaryRepository validationSummaryRepository,
                               MonitorLogger monitorLogger,
                               ValidationRuleConfig validationRuleConfig) {
        this.validationEngine = validationEngine;
        this.fileValidator = fileValidator;
        this.errorRecordRepository = errorRecordRepository;
        this.validationSummaryRepository = validationSummaryRepository;
        this.monitorLogger = monitorLogger;
        this.validationRuleConfig = validationRuleConfig;
    }
    
    @Override
    @Transactional
    public ValidationSummary validateFile(String interfaceName, String filePath, String dataDate) {
        // 获取接口配置
        ValidationRulesProperties.InterfaceConfig config = validationRuleConfig.getInterfaceConfig(interfaceName.replace(".", "-"));
        
        if (config == null) {
            log.warn("未找到接口配置: {}", interfaceName);
            // 使用默认配置
            return validateFile(interfaceName, filePath, dataDate, "|", false, Collections.emptyList());
        }
        
        // 检查是否使用基于索引的配置
        if (config.getFields() != null && !config.getFields().isEmpty()) {
            return validateFileWithIndex(interfaceName, filePath, dataDate, config);
        } else {
            // 向后兼容：使用传统字段名列表方式
            List<String> fieldNames = extractFieldNames(config);
            String delimiter = config.getDelimiter() != null ? config.getDelimiter() : "|";
            return validateFile(interfaceName, filePath, dataDate, delimiter, config.isHeaderLine(), fieldNames);
        }
    }
    
    /**
     * 使用基于索引的校验
     */
    @Transactional
    public ValidationSummary validateFileWithIndex(String interfaceName, String filePath, String dataDate,
                                                  ValidationRulesProperties.InterfaceConfig config) {
        // 获取字段索引映射
        Map<Integer, String> fieldIndexMap = validationRuleConfig.getFieldIndexMapping(interfaceName.replace(".", "-"));
        
        if (fieldIndexMap.isEmpty()) {
            log.warn("接口 {} 没有配置字段索引映射", interfaceName);
            return ValidationSummary.create(interfaceName, filePath, dataDate);
        }
        
        log.info("开始校验文件（索引模式）: 接口={}, 文件={}, 分隔符={}, 字段映射数={}",
                interfaceName, filePath, config.getDelimiter(), fieldIndexMap.size());
        
        // 创建错误处理器
        Consumer<ErrorRecord> errorHandler = this::saveErrorRecord;
        
        // 执行基于索引的文件校验
        ValidationSummary summary = fileValidator.validateFileWithIndex(
                interfaceName, filePath, dataDate, "\\|", config.isHeaderLine(),
                fieldIndexMap, errorHandler);
        
        // 保存校验摘要
        saveSummary(summary);
        
        return summary;
    }
    
    /**
     * 使用基于索引的校验并导出校验通过的数据
     * 
     * @param interfaceName 接口名称
     * @param filePath 输入文件路径
     * @param outputFilePath 输出文件路径（校验通过的数据）
     * @param dataDate 数据日期
     * @return 校验摘要
     */
    @Transactional
    public ValidationSummary validateFileWithIndexAndExport(String interfaceName, String filePath, 
                                                           String outputFilePath, String dataDate) {
        // 获取接口配置
        ValidationRulesProperties.InterfaceConfig config = validationRuleConfig.getInterfaceConfig(interfaceName);
        
        if (config == null) {
            log.warn("未找到接口配置: {}", interfaceName);
            return ValidationSummary.create(interfaceName, filePath, dataDate);
        }
        
        // 获取字段索引映射
        Map<Integer, String> fieldIndexMap = validationRuleConfig.getFieldIndexMapping(interfaceName);
        
        if (fieldIndexMap.isEmpty()) {
            log.warn("接口 {} 没有配置字段索引映射", interfaceName);
            return ValidationSummary.create(interfaceName, filePath, dataDate);
        }
        
        log.info("开始校验文件并导出（索引模式）: 接口={}, 输入文件={}, 输出文件={}, 分隔符={}, 字段映射数={}",
                interfaceName, filePath, outputFilePath, config.getDelimiter(), fieldIndexMap.size());
        
        // 创建错误处理器
        Consumer<ErrorRecord> errorHandler = this::saveErrorRecord;
        
        // 执行基于索引的文件校验并导出
        ValidationSummary summary = fileValidator.validateFileWithIndexAndExport(
                interfaceName, filePath, outputFilePath, dataDate, "\\|", config.isHeaderLine(),
                fieldIndexMap, errorHandler);
        
        // 保存校验摘要
        saveSummary(summary);
        
        return summary;
    }
    
    /**
     * 确定实际使用的分隔符
     */
    private String determineDelimiter(String configDelimiter) {
        if (configDelimiter == null) {
            // 默认使用\200（八进制200，ASCII 128）
            return "\200";
        }
        
        // 处理特殊分隔符配置
        switch (configDelimiter) {
            case "\\200":
                return "\200";
            case "\\|":
                return "\\|";
            case "|":
                return "\\|";  // 因为|在正则表达式中有特殊含义，需要转义
            default:
                return configDelimiter;
        }
    }
    
    /**
     * 从配置中提取字段名列表（向后兼容）
     */
    private List<String> extractFieldNames(ValidationRulesProperties.InterfaceConfig config) {
        List<String> fieldNames = new ArrayList<>();
        
        if (config.getFields() != null) {
            // 按字段索引排序
            config.getFields().stream()
                    .sorted(Comparator.comparing(f -> f.getFieldIndex() != null ? f.getFieldIndex() : 0))
                    .forEach(f -> fieldNames.add(f.getFieldName()));
        }
        
        return fieldNames;
    }
    
    @Override
    @Transactional
    public ValidationSummary validateFile(String interfaceName, String filePath, String dataDate,
                                        String delimiter, boolean headerLine, List<String> fieldNames) {
        // 创建错误处理器
        Consumer<ErrorRecord> errorHandler = this::saveErrorRecord;
        
        // 执行文件校验
        ValidationSummary summary = fileValidator.validateFile(
                interfaceName, filePath, dataDate, delimiter, headerLine, fieldNames, errorHandler);
        
        // 保存校验摘要
        saveSummary(summary);
        
        return summary;
    }
    
    @Override
    public void registerMetadata(String interfaceName, Map<String, FieldMetadata> metadata) {
        validationEngine.registerMetadata(interfaceName, metadata);
        log.info("注册接口元数据: 接口={}, 字段数量={}", interfaceName, metadata.size());
    }
    
    @Override
    public void registerFieldMetadata(String interfaceName, String fieldName, FieldMetadata metadata) {
        // 获取接口元数据
        Map<String, FieldMetadata> interfaceMetadata = getMetadata(interfaceName);
        
        // 更新字段元数据
        interfaceMetadata.put(fieldName, metadata);
        
        // 重新注册接口元数据
        registerMetadata(interfaceName, interfaceMetadata);
        
        log.info("注册字段元数据: 接口={}, 字段={}", interfaceName, fieldName);
    }
    
    @Override
    public Map<String, FieldMetadata> getMetadata(String interfaceName) {
        // 从校验引擎获取元数据（这里需要在ValidationEngine中添加相应方法）
        // 如果ValidationEngine没有提供获取元数据的方法，可以在本类中缓存元数据
        return new HashMap<>(); // 临时返回空映射
    }
    
    @Override
    public Set<String> getAllInterfaces() {
        return validationEngine.getAllInterfaces();
    }
    
    @Override
    public Set<String> getFieldsByInterface(String interfaceName) {
        return validationEngine.getFieldsByInterface(interfaceName);
    }
    
    @Override
    public List<ValidationRule> getRules(String interfaceName, String fieldName) {
        if (fieldName != null) {
            return validationEngine.getRulesByField(interfaceName, fieldName);
        } else {
            return validationEngine.getRulesByInterface(interfaceName);
        }
    }
    
    @Override
    public ValidationSummary getSummary(String interfaceName, String fileName, String dataDate) {
        return validationSummaryRepository.findByInterfaceNameAndFileNameAndDataDate(
                interfaceName, fileName, dataDate);
    }
    
    @Override
    public List<ErrorRecord> getErrors(String interfaceName, String fileName, String dataDate, int page, int size) {
        return errorRecordRepository.findByInterfaceNameAndFileNameAndDataDate(
                interfaceName, fileName, dataDate, page, size);
    }
    
    @Override
    public long getErrorCount(String interfaceName, String fileName, String dataDate) {
        return errorRecordRepository.countByInterfaceNameAndFileNameAndDataDate(
                interfaceName, fileName, dataDate);
    }
    
    /**
     * 保存错误记录
     */
    private void saveErrorRecord(ErrorRecord errorRecord) {
        try {
            errorRecordRepository.save(errorRecord);
        } catch (Exception e) {
            log.error("保存错误记录失败: {}", errorRecord, e);
        }
    }
    
    /**
     * 保存校验摘要
     */
    private void saveSummary(ValidationSummary summary) {
        try {
            validationSummaryRepository.save(summary);
        } catch (Exception e) {
            log.error("保存校验摘要失败: {}", summary, e);
        }
    }
} 