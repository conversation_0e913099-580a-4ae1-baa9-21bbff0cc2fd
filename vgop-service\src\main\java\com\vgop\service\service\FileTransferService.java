package com.vgop.service.service;

import com.vgop.service.config.TaskConfig;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.entity.RevisionTimes;
import com.vgop.service.exception.VgopException;
import com.vgop.service.sftp.SftpService;
import com.vgop.service.validation.ValidationService;
import com.vgop.service.validation.ValidationSummary;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 文件传输服务
 * 集成数据导出、数据校验和SFTP上传功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class FileTransferService {
    
    private final VgopAppConfig appConfig;
    private final RevisionTimesService revisionTimesService;
    private final DataExportService dataExportService;
    private final ValidationService validationService;
    private final SftpService sftpService;
    private final DirectoryInitService directoryInitService;
    
    private final ExecutorService transferExecutor;
    
    @Autowired
    public FileTransferService(
            VgopAppConfig appConfig,
            RevisionTimesService revisionTimesService,
            DataExportService dataExportService,
            ValidationService validationService,
            SftpService sftpService,
            DirectoryInitService directoryInitService) {
        this.appConfig = appConfig;
        this.revisionTimesService = revisionTimesService;
        this.dataExportService = dataExportService;
        this.validationService = validationService;
        this.sftpService = sftpService;
        this.directoryInitService = directoryInitService;
        
        // 创建固定大小的线程池用于并行处理任务
        this.transferExecutor = Executors.newFixedThreadPool(5, r -> {
            Thread t = new Thread(r);
            t.setName("FileTransfer-Worker");
            return t;
        });
    }
    
    /**
     * 执行日常数据传输任务
     * 
     * @param dataDate 数据日期（格式：yyyyMMdd）
     * @return 处理结果
     */
    public TransferResult processDailyTransfer(String dataDate) {
        log.info("开始处理日常数据传输任务，数据日期: {}", dataDate);
        
        // 获取所有启用的日常任务
        List<TaskConfig> tasks = appConfig.getEnabledDailyTasks();
        if (tasks.isEmpty()) {
            log.warn("没有配置启用的日常任务");
            return createEmptyResult(dataDate, "daily");
        }
        
        return processTransfer(tasks, dataDate, "daily");
    }
    
    /**
     * 执行月度数据传输任务
     * 
     * @param dataDate 数据日期（格式：yyyyMMdd）
     * @return 处理结果
     */
    public TransferResult processMonthlyTransfer(String dataDate) {
        log.info("开始处理月度数据传输任务，数据日期: {}", dataDate);
        
        // 获取所有启用的月度任务
        List<TaskConfig> tasks = appConfig.getEnabledMonthlyTasks();
        if (tasks.isEmpty()) {
            log.warn("没有配置启用的月度任务");
            return createEmptyResult(dataDate, "monthly");
        }
        
        return processTransfer(tasks, dataDate, "monthly");
    }
    
    /**
     * 处理数据传输（导出+校验+上传）
     */
    private TransferResult processTransfer(List<TaskConfig> tasks, String dataDate, String cycleType) {
        long startTime = System.currentTimeMillis();
        TransferResult result = new TransferResult();
        result.setDataDate(dataDate);
        result.setCycleType(cycleType);
        result.setStartTime(startTime);
        
        try {
            // 0. 确保目录存在
            log.info("确保数据目录存在: {}", dataDate);
            directoryInitService.ensureDateDirectoryExists(dataDate);
            
            // 1. 获取版本号
            RevisionTimes revisionTimes = revisionTimesService.getRevisionTimes(dataDate, cycleType);
            if (revisionTimes == null) {
                throw new VgopException("无法获取版本信息，数据日期: " + dataDate + ", 周期: " + cycleType);
            }
            
            // ===============================
            // 阶段一：数据导出
            // ===============================
            log.info("开始阶段一：数据导出 - 日期: {}, 周期: {}", dataDate, cycleType);
            List<CompletableFuture<DataExportService.ExportResult>> exportFutures = new ArrayList<>();
            
            for (TaskConfig task : tasks) {
                CompletableFuture<DataExportService.ExportResult> future = CompletableFuture.supplyAsync(
                        () -> dataExportService.exportData(task, dataDate, revisionTimes.getRevision()),
                        transferExecutor
                );
                exportFutures.add(future);
            }
            
            // 等待所有导出任务完成
            List<DataExportService.ExportResult> exportResults = exportFutures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
            
            // 统计导出结果
            int totalExportedFiles = 0;
            int totalExportedRows = 0;
            List<String> failedInterfaces = new ArrayList<>();
            
            for (DataExportService.ExportResult exportResult : exportResults) {
                if (exportResult.isSuccess()) {
                    totalExportedFiles += exportResult.getFileCount();
                    totalExportedRows += exportResult.getTotalRows();
                } else {
                    failedInterfaces.add(exportResult.getInterfaceId());
                }
            }
            
            result.setExportedFiles(totalExportedFiles);
            result.setExportedRows(totalExportedRows);
            result.setFailedInterfaces(failedInterfaces);
            
            // 如果有接口导出失败，整体任务失败，不进行后续处理
            if (!failedInterfaces.isEmpty()) {
                result.setSuccess(false);
                result.setErrorMessage("部分接口导出失败: " + String.join(", ", failedInterfaces));
                return result;
            }
            
            log.info("阶段一完成：数据导出成功 - 导出文件数: {}, 导出行数: {}", totalExportedFiles, totalExportedRows);
            
            // ===============================
            // 阶段二：数据校验
            // ===============================
            log.info("开始阶段二：数据校验 - 日期: {}, 周期: {}", dataDate, cycleType);
            
            // 执行数据校验（这里假设有ValidationService，需要根据实际情况调整）
            boolean validationSuccess = true;
            List<String> validationErrors = new ArrayList<>();
            
            for (TaskConfig task : tasks) {
                try {
                    // 构造原始数据文件路径（.unl文件）
                    // 使用与DataExportService一致的路径：./VGOPdata/datafile/{dataDate}/day/
                    String tempFileName = generateTempFileName(task, dataDate);
                    String validationFilePath = "./VGOPdata/datafile/" + dataDate + "/day/" + tempFileName;
                    
                    // 执行数据校验
                    ValidationSummary validationSummary = validationService.validateFile(
                            task.getInterfaceName(), 
                            validationFilePath, 
                            dataDate
                    );
                    
                    // 检查校验结果
                    if (validationSummary.getErrorRate() != null && validationSummary.getErrorRate() > 0) {
                        log.warn("接口 {} 校验发现错误，错误率: {}%", task.getInterfaceId(), validationSummary.getErrorRate());
                        // 根据业务需求决定是否将有错误的数据视为校验失败
                        // 这里暂时只记录警告，不阻止上传
                    }
                    
                    log.info("接口 {} 数据校验完成，总行数: {}, 错误行数: {}, 错误率: {}%", 
                            task.getInterfaceId(), 
                            validationSummary.getTotalRows(),
                            validationSummary.getErrorRows(),
                            validationSummary.getErrorRate());
                            
                } catch (Exception e) {
                    log.error("接口 {} 数据校验失败: {}", task.getInterfaceId(), e.getMessage());
                    validationSuccess = false;
                    validationErrors.add(task.getInterfaceId() + ": " + e.getMessage());
                }
            }
            
            // 如果校验失败，不进行文件上传
            if (!validationSuccess) {
                result.setSuccess(false);
                result.setErrorMessage("数据校验失败: " + String.join(", ", validationErrors));
                return result;
            }
            
            log.info("阶段二完成：数据校验成功");
            
            // ===============================
            // 阶段三：生成.dat文件
            // ===============================
            log.info("开始阶段三：生成.dat文件 - 日期: {}, 周期: {}", dataDate, cycleType);
            
            int generatedDatFiles = 0;
            List<String> datGenerationErrors = new ArrayList<>();
            
            for (TaskConfig task : tasks) {
                try {
                    // 构造源文件路径（.unl文件）
                    String tempFileName = generateTempFileName(task, dataDate);
                    String sourceFilePath = "./VGOPdata/datafile/" + dataDate + "/day/" + tempFileName;
                    
                    // 构造目标文件路径（.dat文件）
                    String datFileName = generateDatFileName(task, dataDate, revisionTimes.getRevision());
                    String targetFilePath = "./VGOPdata/datafile/" + dataDate + "/" + datFileName;
                    
                    // 检查源文件是否存在
                    File sourceFile = new File(sourceFilePath);
                    if (!sourceFile.exists()) {
                        throw new VgopException("源数据文件不存在: " + sourceFilePath);
                    }
                    
                    // 将校验成功的数据写入.dat文件
                    // 这里简单的复制文件，实际可能需要根据校验结果过滤数据
                    Files.copy(Paths.get(sourceFilePath), Paths.get(targetFilePath));
                    
                    // 生成对应的.verf校验文件
                    generateVerfFile(targetFilePath, task, dataDate, revisionTimes.getRevision());
                    
                    generatedDatFiles++;
                    log.info("接口 {} .dat文件生成成功: {}", task.getInterfaceId(), datFileName);
                    
                } catch (Exception e) {
                    log.error("接口 {} .dat文件生成失败: {}", task.getInterfaceId(), e.getMessage());
                    datGenerationErrors.add(task.getInterfaceId() + ": " + e.getMessage());
                }
            }
            
            // 如果.dat文件生成失败，不进行文件上传
            if (!datGenerationErrors.isEmpty()) {
                result.setSuccess(false);
                result.setErrorMessage(".dat文件生成失败: " + String.join(", ", datGenerationErrors));
                return result;
            }
            
            log.info("阶段三完成：.dat文件生成成功 - 生成文件数: {}", generatedDatFiles);
            
            // ===============================
            // 阶段四：文件传输（精确传输当前批次的文件）
            // ===============================
            log.info("开始阶段四：文件传输 - 日期: {}, 周期: {}", dataDate, cycleType);
            
            // 构建当前批次的任务上下文
            Set<String> interfaceIds = tasks.stream()
                    .map(TaskConfig::getInterfaceId)
                    .collect(Collectors.toSet());
            
            // 使用精确文件传输
            SftpService.UploadResult uploadResult = sftpService.uploadVgopDataFilesWithFilter(
                    dataDate, interfaceIds, 
                    cycleType.equals("daily") ? "day" : "month");
            
            result.setUploadedDatFiles(uploadResult.getDatFileCount());
            result.setUploadedVerfFiles(uploadResult.getVerfFileCount());
            result.setSuccess(uploadResult.isSuccess());
            
            if (!uploadResult.isSuccess()) {
                result.setErrorMessage("文件上传失败");
                return result;
            }
            
            log.info("阶段四完成：文件传输成功 - 上传.dat文件: {}, 上传.verf文件: {}", 
                    uploadResult.getDatFileCount(), uploadResult.getVerfFileCount());
            
            // 5. 整体任务完成
            if (result.isSuccess()) {
                log.info("四阶段数据处理成功完成 - 日期: {}, 周期: {}", dataDate, cycleType);
            }
            
        } catch (Exception e) {
            log.error("数据传输处理异常", e);
            result.setSuccess(false);
            result.setErrorMessage("处理异常: " + e.getMessage());
        } finally {
            result.setEndTime(System.currentTimeMillis());
            logTransferResult(result);
        }
        
        return result;
    }
    
    /**
     * 创建空结果
     */
    private TransferResult createEmptyResult(String dataDate, String cycleType) {
        TransferResult result = new TransferResult();
        result.setDataDate(dataDate);
        result.setCycleType(cycleType);
        result.setStartTime(System.currentTimeMillis());
        result.setEndTime(System.currentTimeMillis());
        result.setSuccess(false);
        result.setErrorMessage("没有配置启用的" + (cycleType.equals("daily") ? "日常" : "月度") + "任务");
        return result;
    }
    
    /**
     * 记录传输结果 
     */
    private void logTransferResult(TransferResult result) {
        StringBuilder sb = new StringBuilder();
        sb.append("\n========== 数据传输任务结果 ==========\n");
        sb.append("数据日期: ").append(result.getDataDate()).append("\n");
        sb.append("周期类型: ").append(result.getCycleType().equals("daily") ? "日常" : "月度").append("\n");
        sb.append("处理状态: ").append(result.isSuccess() ? "成功" : "失败").append("\n");
        sb.append("导出文件数: ").append(result.getExportedFiles()).append("\n");
        sb.append("导出数据行数: ").append(result.getExportedRows()).append("\n");
        sb.append("上传.dat文件数: ").append(result.getUploadedDatFiles()).append("\n");
        sb.append("上传.verf文件数: ").append(result.getUploadedVerfFiles()).append("\n");
        sb.append("处理时间: ").append(result.getDuration()).append("ms\n");
        
        if (!result.isSuccess()) {
            sb.append("错误信息: ").append(result.getErrorMessage()).append("\n");
            if (!result.getFailedInterfaces().isEmpty()) {
                sb.append("失败接口: ").append(String.join(", ", result.getFailedInterfaces())).append("\n");
            }
        }
        
        sb.append("=====================================");
        
        if (result.isSuccess()) {
            log.info(sb.toString());
        } else {
            log.error(sb.toString());
        }
    }
    
    /**
     * 生成临时文件名
     * 
     * @param task 任务配置
     * @param dataDate 数据日期
     * @return 临时文件名
     */
    private String generateTempFileName(TaskConfig task, String dataDate) {
        if (task.getExport() == null || task.getExport().getTempFileNameTemplate() == null) {
            // 使用默认模板
            return String.format("a_10000_%s_%s.unl", dataDate, task.getInterfaceName());
        }
        
        // 替换模板中的占位符
        return task.getExport().getTempFileNameTemplate()
                .replace("{dataDate}", dataDate)
                .replace("{interfaceName}", task.getInterfaceName())
                .replace("{interfaceId}", task.getInterfaceId());
    }
    
    /**
     * 生成.dat文件名
     * 
     * @param task 任务配置
     * @param dataDate 数据日期
     * @param revision 版本号
     * @return .dat文件名
     */
    private String generateDatFileName(TaskConfig task, String dataDate, Integer revision) {
        // 根据任务配置或使用默认模板生成.dat文件名
        // 格式：a_10000_yyyymmdd_VGOP1-R2.10-24201_00_001.dat
        return String.format("a_10000_%s_%s_%s_001.dat", 
                dataDate, 
                task.getInterfaceName(), 
                revision);
    }
    
    /**
     * 生成.verf校验文件
     * 
     * @param datFilePath .dat文件路径
     * @param task 任务配置
     * @param dataDate 数据日期
     * @param revision 版本号
     */
    private void generateVerfFile(String datFilePath, TaskConfig task, String dataDate, Integer revision) {
        try {
            // 构造.verf文件路径
            String verfFilePath = datFilePath.replace(".dat", ".verf");
            
            // 获取.dat文件信息
            File datFile = new File(datFilePath);
            if (!datFile.exists()) {
                throw new VgopException(".dat文件不存在: " + datFilePath);
            }
            
            long fileSize = datFile.length();
            long lineCount = Files.lines(Paths.get(datFilePath)).count();
            
            // 生成.verf文件内容
            StringBuilder verfContent = new StringBuilder();
            verfContent.append("FILE_NAME=").append(datFile.getName()).append("\n");
            verfContent.append("FILE_SIZE=").append(fileSize).append("\n");
            verfContent.append("LINE_COUNT=").append(lineCount).append("\n");
            verfContent.append("DATA_DATE=").append(dataDate).append("\n");
            verfContent.append("INTERFACE_ID=").append(task.getInterfaceId()).append("\n");
            verfContent.append("REVISION=").append(revision).append("\n");
            verfContent.append("GENERATE_TIME=").append(
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))).append("\n");
            
            // 写入.verf文件
            Files.write(Paths.get(verfFilePath), verfContent.toString().getBytes());
            
            log.debug("生成.verf文件成功: {}", verfFilePath);
            
        } catch (Exception e) {
            log.error("生成.verf文件失败: {}", e.getMessage());
            throw new VgopException("生成.verf文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 传输结果类
     */
    public static class TransferResult {
        private String dataDate;
        private String cycleType;
        private boolean success;
        private int exportedFiles;
        private int exportedRows;
        private int uploadedDatFiles;
        private int uploadedVerfFiles;
        private List<String> failedInterfaces = new ArrayList<>();
        private String errorMessage;
        private long startTime;
        private long endTime;
        
        public String getDataDate() { return dataDate; }
        public void setDataDate(String dataDate) { this.dataDate = dataDate; }
        
        public String getCycleType() { return cycleType; }
        public void setCycleType(String cycleType) { this.cycleType = cycleType; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public int getExportedFiles() { return exportedFiles; }
        public void setExportedFiles(int exportedFiles) { this.exportedFiles = exportedFiles; }
        
        public int getExportedRows() { return exportedRows; }
        public void setExportedRows(int exportedRows) { this.exportedRows = exportedRows; }
        
        public int getUploadedDatFiles() { return uploadedDatFiles; }
        public void setUploadedDatFiles(int uploadedDatFiles) { this.uploadedDatFiles = uploadedDatFiles; }
        
        public int getUploadedVerfFiles() { return uploadedVerfFiles; }
        public void setUploadedVerfFiles(int uploadedVerfFiles) { this.uploadedVerfFiles = uploadedVerfFiles; }
        
        public List<String> getFailedInterfaces() { return failedInterfaces; }
        public void setFailedInterfaces(List<String> failedInterfaces) { this.failedInterfaces = failedInterfaces; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getDuration() { return endTime - startTime; }
    }
} 